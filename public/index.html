<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>NetStream</title>
  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg">
  <link rel="icon" type="image/png" href="/favicon.png">
  <link rel="apple-touch-icon" sizes="180x180" href="/favicon/apple-touch-icon.png">
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link rel="manifest" href="/site.webmanifest">
  <meta name="theme-color" content="#00bcd4">
  <!-- Stylesheets -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="/css/styles.css">
  <link rel="stylesheet" href="/css/livetv.css">
  <link rel="stylesheet" href="/css/remote-control.css">
  <link rel="stylesheet" href="/css/player.css">
  <link rel="stylesheet" href="/css/subtitle-styles.css">
  <link rel="stylesheet" href="/css/admin-panel.css">
</head>
<body>
  <div class="container">
    <div class="sidebar" role="navigation" aria-label="Main Navigation">
      <h2>NetStream</h2>
      <ul>
        <li><a href="/#search" data-section="search" aria-label="Search"><i class="fas fa-search"></i> Search</a></li>
        <li><a href="/#movies" data-section="movies" aria-label="Movies"><i class="fas fa-film"></i> Movies</a></li>
        <li><a href="/#series" data-section="series" aria-label="Series"><i class="fas fa-tv"></i> Series</a></li>
        <li><a href="/#anime" data-section="anime" aria-label="Anime"><i class="fas fa-frog"></i> Anime</a></li>
        <li><a href="/#livetv" data-section="livetv" aria-label="Live TV"><i class="fas fa-broadcast-tower"></i> Live TV</a></li>
      </ul>
    </div>
    <main class="content">
      <div class="search-bar">
        <input type="text" id="search-input" placeholder="Search..." aria-label="Search input">
        <button id="search-button" aria-label="Search"><i class="fas fa-search"></i> Search</button>
      </div>
      <section id="search" class="section">
        <h2>Search Results</h2>
        <div id="search-list" class="grid"></div>
      </section>
      <section id="movies" class="section active">
        <!-- Hero Section -->
        <div class="hero-section" id="movies-hero">
          <div class="hero-background" id="movies-hero-bg"></div>
          <div class="hero-content">
            <div class="hero-info">
              <h1 class="hero-title">Discover Amazing Movies</h1>
              <p class="hero-description">Explore our vast collection of movies from around the world. From blockbusters to indie gems, find your next favorite film.</p>
              <div class="hero-actions">
                <button class="hero-btn primary" onclick="document.querySelector('#search-input').focus()">
                  <i class="fas fa-search"></i>
                  Start Exploring
                </button>
                <button class="hero-btn secondary" onclick="document.querySelector('#movies-trending-carousel').scrollIntoView({behavior: 'smooth'})">
                  <i class="fas fa-fire"></i>
                  View Trending
                </button>
              </div>
            </div>
          </div>
          <div class="hero-gradient"></div>
        </div>


        <div class="trending-carousel-container" id="movies-trending-carousel">
          <h3>Trending Now</h3>
          <div class="carousel-navigation">
            <button class="carousel-nav prev" aria-label="Previous"><i class="fas fa-chevron-left"></i></button>
            <div class="carousel-items"></div>
            <button class="carousel-nav next" aria-label="Next"><i class="fas fa-chevron-right"></i></button>
          </div>
        </div>

        <div class="trending-carousel-container" id="movies-wishlist-carousel">
          <h3>My Wish List</h3>
          <div class="carousel-navigation">
            <button class="carousel-nav prev" aria-label="Previous"><i class="fas fa-chevron-left"></i></button>
            <div class="carousel-items"></div>
            <button class="carousel-nav next" aria-label="Next"><i class="fas fa-chevron-right"></i></button>
          </div>
        </div>

        <div class="trending-carousel-container" id="movies-recently-watched-carousel">
          <h3>Recently Watched</h3>
          <div class="carousel-navigation">
            <button class="carousel-nav prev" aria-label="Previous"><i class="fas fa-chevron-left"></i></button>
            <div class="carousel-items"></div>
            <button class="carousel-nav next" aria-label="Next"><i class="fas fa-chevron-right"></i></button>
          </div>
        </div>

        <div class="trending-carousel-container" id="movies-latest-carousel">
          <div class="carousel-header">
            <h3>Latest Updates</h3>
            <button class="carousel-refresh-btn" onclick="window.refreshLatestMoviesCarousel()" title="Refresh latest movies">
              <i class="fas fa-sync-alt"></i>
            </button>
          </div>
          <div class="carousel-navigation">
            <button class="carousel-nav prev" aria-label="Previous"><i class="fas fa-chevron-left"></i></button>
            <div class="carousel-items"></div>
            <button class="carousel-nav next" aria-label="Next"><i class="fas fa-chevron-right"></i></button>
          </div>
        </div>

        <div class="trending-carousel-container" id="movies-ancien-carousel">
          <div class="carousel-header">
            <h3>Films Anciens</h3>
            <button class="carousel-refresh-btn" onclick="window.refreshAncienMoviesCarousel()" title="Refresh ancien movies">
              <i class="fas fa-sync-alt"></i>
            </button>
          </div>
          <div class="carousel-navigation">
            <button class="carousel-nav prev" aria-label="Previous"><i class="fas fa-chevron-left"></i></button>
            <div class="carousel-items"></div>
            <button class="carousel-nav next" aria-label="Next"><i class="fas fa-chevron-right"></i></button>
          </div>
        </div>

        <!-- Genre-based carousels for movies -->
        <div id="movies-genre-carousels"></div>
      </section>
      <section id="series" class="section">
        <!-- Hero Section -->
        <div class="hero-section" id="series-hero">
          <div class="hero-background" id="series-hero-bg"></div>
          <div class="hero-content">
            <div class="hero-info">
              <h1 class="hero-title">Binge-Worthy Series</h1>
              <p class="hero-description">Dive into captivating storylines and unforgettable characters. From drama to comedy, find your next obsession.</p>
              <div class="hero-actions">
                <button class="hero-btn primary" onclick="document.querySelector('#search-input').focus()">
                  <i class="fas fa-search"></i>
                  Find Series
                </button>
                <button class="hero-btn secondary" onclick="document.querySelector('#series-trending-carousel').scrollIntoView({behavior: 'smooth'})">
                  <i class="fas fa-fire"></i>
                  Popular Now
                </button>
              </div>
            </div>
          </div>
          <div class="hero-gradient"></div>
        </div>


        <div class="trending-carousel-container" id="series-trending-carousel">
          <h3>Trending Now</h3>
          <div class="carousel-navigation">
            <button class="carousel-nav prev" aria-label="Previous"><i class="fas fa-chevron-left"></i></button>
            <div class="carousel-items"></div>
            <button class="carousel-nav next" aria-label="Next"><i class="fas fa-chevron-right"></i></button>
          </div>
        </div>

        <div class="trending-carousel-container" id="series-wishlist-carousel">
          <h3>My Wish List</h3>
          <div class="carousel-navigation">
            <button class="carousel-nav prev" aria-label="Previous"><i class="fas fa-chevron-left"></i></button>
            <div class="carousel-items"></div>
            <button class="carousel-nav next" aria-label="Next"><i class="fas fa-chevron-right"></i></button>
          </div>
        </div>

        <div class="trending-carousel-container" id="series-recently-watched-carousel">
          <h3>Recently Watched</h3>
          <div class="carousel-navigation">
            <button class="carousel-nav prev" aria-label="Previous"><i class="fas fa-chevron-left"></i></button>
            <div class="carousel-items"></div>
            <button class="carousel-nav next" aria-label="Next"><i class="fas fa-chevron-right"></i></button>
          </div>
        </div>

        <div class="trending-carousel-container" id="series-latest-carousel">
          <div class="carousel-header">
            <h3>Latest Updates</h3>
            <button class="carousel-refresh-btn" onclick="window.refreshLatestSeriesCarousel()" title="Refresh latest series">
              <i class="fas fa-sync-alt"></i>
            </button>
          </div>
          <div class="carousel-navigation">
            <button class="carousel-nav prev" aria-label="Previous"><i class="fas fa-chevron-left"></i></button>
            <div class="carousel-items"></div>
            <button class="carousel-nav next" aria-label="Next"><i class="fas fa-chevron-right"></i></button>
          </div>
        </div>

        <!-- Action Series carousel -->
        <div class="trending-carousel-container genre-carousel" id="series-action-carousel">
          <div class="carousel-header">
            <h3>Action Series</h3>
            <button class="carousel-refresh-btn" onclick="window.refreshActionSeriesCarousel()" title="Refresh Action Series">
              <i class="fas fa-sync-alt"></i>
            </button>
          </div>
          <div class="carousel-navigation">
            <button class="carousel-nav prev" aria-label="Previous"><i class="fas fa-chevron-left"></i></button>
            <div class="carousel-items"></div>
            <button class="carousel-nav next" aria-label="Next"><i class="fas fa-chevron-right"></i></button>
          </div>
        </div>

        <!-- Genre-based carousels for series -->
        <div id="series-genre-carousels"></div>
      </section>
      <section id="anime" class="section">
        <!-- Hero Section -->
        <div class="hero-section" id="anime-hero">
          <div class="hero-background" id="anime-hero-bg"></div>
          <div class="hero-content">
            <div class="hero-info">
              <h1 class="hero-title">Anime Universe</h1>
              <p class="hero-description">Enter the world of anime with epic adventures, heartfelt stories, and stunning animation. Your journey starts here.</p>
              <div class="hero-actions">
                <button class="hero-btn primary" onclick="document.querySelector('#search-input').focus()">
                  <i class="fas fa-search"></i>
                  Explore Anime
                </button>
                <button class="hero-btn secondary" onclick="document.querySelector('#anime-trending-carousel').scrollIntoView({behavior: 'smooth'})">
                  <i class="fas fa-fire"></i>
                  Trending Anime
                </button>
              </div>
            </div>
          </div>
          <div class="hero-gradient"></div>
        </div>


        <div class="trending-carousel-container" id="anime-trending-carousel">
          <h3>Trending Now</h3>
          <div class="carousel-navigation">
            <button class="carousel-nav prev" aria-label="Previous"><i class="fas fa-chevron-left"></i></button>
            <div class="carousel-items"></div>
            <button class="carousel-nav next" aria-label="Next"><i class="fas fa-chevron-right"></i></button>
          </div>
        </div>

        <div class="trending-carousel-container" id="anime-wishlist-carousel">
          <h3>My Wish List</h3>
          <div class="carousel-navigation">
            <button class="carousel-nav prev" aria-label="Previous"><i class="fas fa-chevron-left"></i></button>
            <div class="carousel-items"></div>
            <button class="carousel-nav next" aria-label="Next"><i class="fas fa-chevron-right"></i></button>
          </div>
        </div>

        <div class="trending-carousel-container" id="anime-recently-watched-carousel">
          <h3>Recently Watched</h3>
          <div class="carousel-navigation">
            <button class="carousel-nav prev" aria-label="Previous"><i class="fas fa-chevron-left"></i></button>
            <div class="carousel-items"></div>
            <button class="carousel-nav next" aria-label="Next"><i class="fas fa-chevron-right"></i></button>
          </div>
        </div>

        <div class="trending-carousel-container" id="anime-latest-carousel">
          <div class="carousel-header">
            <h3>Latest Updates</h3>
            <button class="carousel-refresh-btn" onclick="window.refreshLatestAnimeCarousel()" title="Refresh latest anime">
              <i class="fas fa-sync-alt"></i>
            </button>
          </div>
          <div class="carousel-navigation">
            <button class="carousel-nav prev" aria-label="Previous"><i class="fas fa-chevron-left"></i></button>
            <div class="carousel-items"></div>
            <button class="carousel-nav next" aria-label="Next"><i class="fas fa-chevron-right"></i></button>
          </div>
        </div>

        <div class="trending-carousel-container" id="anime-movies-carousel">
          <div class="carousel-header">
            <h3>Anime Movies</h3>
            <button class="carousel-refresh-btn" onclick="window.refreshAnimeMoviesCarousel()" title="Refresh anime movies">
              <i class="fas fa-sync-alt"></i>
            </button>
          </div>
          <div class="carousel-navigation">
            <button class="carousel-nav prev" aria-label="Previous"><i class="fas fa-chevron-left"></i></button>
            <div class="carousel-items"></div>
            <button class="carousel-nav next" aria-label="Next"><i class="fas fa-chevron-right"></i></button>
          </div>
        </div>

        <!-- Genre-based carousels for anime -->
        <div id="anime-genre-carousels"></div>
      </section>
      <section id="livetv" class="section" tabindex="0">
        <!-- Sidebar Hover Trigger -->
        <div class="livetv-sidebar-trigger" id="livetv-sidebar-trigger"></div>

        <!-- Activation Hint -->
        <div class="livetv-activation-hint" id="livetv-activation-hint">
          <div class="hint-content">
            <i class="fas fa-arrow-right"></i>
            <span>Press → or click to access channels</span>
          </div>
        </div>

        <!-- Full Screen TV Player -->
        <div class="livetv-player-container">
          <div id="livetv-player-logo" class="tv-logo">NetStream Live TV</div>
          <video id="livetv-player-video" playsinline></video>
          <iframe id="livetv-player-iframe" allowfullscreen></iframe>

          <!-- Simple TV Controls -->
          <div class="tv-controls">
            <button class="tv-control-btn" id="livetv-play-pause" title="Play/Pause">
              <i class="fas fa-play"></i>
            </button>
            <button class="tv-control-btn" id="livetv-fullscreen-btn" title="Fullscreen">
              <i class="fas fa-expand"></i>
            </button>
          </div>

          <!-- Channel Selector Overlay (Right Side) -->
          <div class="livetv-channel-selector" id="livetv-channel-selector">
            <div class="selector-header">
              <div class="favorites-indicator">
                <i class="fas fa-star"></i>
                <i class="fas fa-arrow-right"></i>
              </div>
            </div>

            <!-- Rolling Channel Cards -->
            <div class="channel-cards-container">
              <!-- Previous channels -->
              <div class="channel-card prev-2" id="livetv-prev-2">
                <div class="favorite-icon" data-position="prev-2"><i class="far fa-star"></i></div>
                <div class="channel-number">--</div>
                <div class="channel-name">Channel</div>
              </div>
              <div class="channel-card prev-1" id="livetv-prev-1">
                <div class="favorite-icon" data-position="prev-1"><i class="far fa-star"></i></div>
                <div class="channel-number">--</div>
                <div class="channel-name">Channel</div>
              </div>

              <!-- Current channel (center) -->
              <div class="channel-card current" id="livetv-current">
                <div class="favorite-icon" data-position="current"><i class="far fa-star"></i></div>
                <div class="channel-number">--</div>
                <div class="channel-name">Select a channel</div>
                <div class="channel-category">--</div>
              </div>

              <!-- Next channels -->
              <div class="channel-card next-1" id="livetv-next-1">
                <div class="favorite-icon" data-position="next-1"><i class="far fa-star"></i></div>
                <div class="channel-number">--</div>
                <div class="channel-name">Channel</div>
              </div>
              <div class="channel-card next-2" id="livetv-next-2">
                <div class="favorite-icon" data-position="next-2"><i class="far fa-star"></i></div>
                <div class="channel-number">--</div>
                <div class="channel-name">Channel</div>
              </div>
            </div>




          </div>

          <!-- Favorites List (Second Panel) -->
          <div class="livetv-favorites-list" id="livetv-favorites-list">
            <div class="favorites-header">
              <h3>Favorites</h3>
              <button class="favorites-back-btn" id="livetv-favorites-back" title="Back to Channels">
                <i class="fas fa-arrow-left"></i>
              </button>
            </div>

            <!-- Rolling Favorite Channel Cards -->
            <div class="favorites-cards-container">
              <!-- Previous favorites -->
              <div class="channel-card prev-2" id="livetv-fav-prev-2">
                <div class="favorite-icon" data-position="fav-prev-2"><i class="far fa-star"></i></div>
                <div class="channel-number">--</div>
                <div class="channel-name">Channel</div>
              </div>
              <div class="channel-card prev-1" id="livetv-fav-prev-1">
                <div class="favorite-icon" data-position="fav-prev-1"><i class="far fa-star"></i></div>
                <div class="channel-number">--</div>
                <div class="channel-name">Channel</div>
              </div>

              <!-- Current favorite (center) -->
              <div class="channel-card current" id="livetv-fav-current">
                <div class="favorite-icon" data-position="fav-current"><i class="far fa-star"></i></div>
                <div class="channel-number">--</div>
                <div class="channel-name">No Favorites</div>
                <div class="channel-category">--</div>
              </div>

              <!-- Next favorites -->
              <div class="channel-card next-1" id="livetv-fav-next-1">
                <div class="favorite-icon" data-position="fav-next-1"><i class="far fa-star"></i></div>
                <div class="channel-number">--</div>
                <div class="channel-name">Channel</div>
              </div>
              <div class="channel-card next-2" id="livetv-fav-next-2">
                <div class="favorite-icon" data-position="fav-next-2"><i class="far fa-star"></i></div>
                <div class="channel-number">--</div>
                <div class="channel-name">Channel</div>
              </div>
            </div>


          </div>

          <!-- Show Channel Selector Button -->
          <button class="livetv-show-selector-btn" id="livetv-show-selector-btn" title="Show Channels">
            <i class="fas fa-list"></i>
          </button>
        </div>
      </section>
    </main>
  </div>

  <!-- Mobile Search Modal -->
  <div class="mobile-search-modal" id="mobile-search-modal" style="display: none;">
    <div class="search-content">
      <div class="search-header">
        <h3 class="search-title">Search</h3>
        <button class="close-search" id="close-mobile-search" aria-label="Close search">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <input type="text" id="mobile-search-input" placeholder="Search movies, series, anime..." aria-label="Search input">
      <button id="mobile-search-button" aria-label="Search">
        <i class="fas fa-search"></i> Search
      </button>
    </div>
  </div>

  <div id="player-container" class="hidden">
    <div id="player-wrapper">
      <div id="player-logo">NetStream</div>
      <video id="player" playsinline></video>
      <iframe id="player-iframe" allowfullscreen></iframe>
      <!-- Player controls will be created by player.js -->
    </div>
    <button id="close-player" aria-label="Close player">&times;</button>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
  <script src="/js/performance.js"></script>
  <script src="/js/debug-helper.js"></script>
  <script src="/js/subtitle-proxy.js"></script>
  <script src="/js/addic7ed-service.js"></script>
  <script src="/js/player.js"></script>
  <script src="/js/player-integration.js"></script>
  <script src="/js/script.js"></script>
  <script src="/js/jikanClient.js"></script>
  <script src="/js/recentlyWatched.js"></script>
  <script src="/js/wishlist.js"></script>
  <script src="/js/home.js"></script>
  <script src="/js/livetv.js"></script>
  <script src="/js/remoteControl.js"></script>
  <script src="/js/admin.js?v=1.1"></script>

  <script>
    // Initialize performance optimizations
    document.addEventListener('DOMContentLoaded', function() {
      // Setup lazy loading for carousels
      const carousels = document.querySelectorAll('.carousel, .carousel-navigation');
      carousels.forEach(carousel => {
        if (typeof loadCarouselData === 'function') {
          performanceOptimizer.setupLazyCarousel(carousel, loadCarouselData);
        }
      });

      // Preload critical images
      const criticalImages = [
        '/images/hi.png',
        '/default-thumbnail.jpg'
      ];
      performanceOptimizer.preloadImages(criticalImages);

      // Log performance metrics every 30 seconds
      setInterval(() => {
        console.log('Performance Metrics:', performanceOptimizer.getMetrics());
      }, 30000);

      // Mobile Search Modal functionality
      function initMobileSearch() {
        const mobileSearchModal = document.getElementById('mobile-search-modal');
        const mobileSearchInput = document.getElementById('mobile-search-input');
        const mobileSearchButton = document.getElementById('mobile-search-button');
        const closeMobileSearch = document.getElementById('close-mobile-search');

        // Check if we're on mobile (768px or below)
        function isMobile() {
          return window.innerWidth <= 768;
        }

        // Show mobile search modal when search nav item is clicked on mobile
        function handleSearchNavClick(e) {
          if (isMobile()) {
            e.preventDefault();
            mobileSearchModal.classList.add('show');
            // Focus the input after animation
            setTimeout(() => {
              mobileSearchInput.focus();
            }, 300);
          }
        }

        // Close mobile search modal
        function closeMobileSearchModal() {
          mobileSearchModal.classList.remove('show');
        }

        // Handle mobile search
        function handleMobileSearch() {
          const query = mobileSearchInput.value.trim();
          if (query) {
            // Set the main search input value
            const mainSearchInput = document.getElementById('search-input');
            if (mainSearchInput) {
              mainSearchInput.value = query;
            }

            // Trigger the same search logic as desktop
            // First switch to search section
            window.location.hash = 'search';

            // Then perform the search (same as desktop triggerSearch function)
            if (!query) {
              console.log("Mobile search: Search triggered with empty query.");
              const list = document.getElementById('search-list');
              if (list) list.innerHTML = '<p>Please enter a search term.</p>';
              closeMobileSearchModal();
              return;
            }

            console.log(`Mobile search: Search triggered, query: ${query}`);

            // Close the modal first
            closeMobileSearchModal();

            // Wait a bit for the modal to close, then trigger search
            setTimeout(() => {
              // Trigger the search button click to use existing functionality
              const searchButton = document.getElementById('search-button');
              if (searchButton) {
                searchButton.click();
              }
            }, 100);
          }
        }

        // Event listeners
        const searchNavLink = document.querySelector('a[data-section="search"]');
        if (searchNavLink) {
          searchNavLink.addEventListener('click', handleSearchNavClick);
        }

        if (closeMobileSearch) {
          closeMobileSearch.addEventListener('click', closeMobileSearchModal);
        }

        if (mobileSearchButton) {
          mobileSearchButton.addEventListener('click', handleMobileSearch);
        }

        if (mobileSearchInput) {
          mobileSearchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
              handleMobileSearch();
            }
          });
        }

        // Close modal when clicking outside
        mobileSearchModal.addEventListener('click', function(e) {
          if (e.target === mobileSearchModal) {
            closeMobileSearchModal();
          }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
          if (!isMobile() && mobileSearchModal.classList.contains('show')) {
            closeMobileSearchModal();
          }
        });
      }

      // Initialize mobile search
      initMobileSearch();
    });
  </script>
</body>
</html>