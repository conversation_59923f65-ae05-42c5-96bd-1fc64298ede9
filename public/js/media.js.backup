// File: public/js/media.js
// COMPLETE CODE - Fix for Jikan field placement

console.log('Media.js script loaded');
document.addEventListener('DOMContentLoaded', async () => {
  const playerContainer = document.getElementById('player-container');
  let player = document.getElementById('player');
  let playerIframe = document.getElementById('player-iframe');
  const seasonsContainer = document.getElementById('seasons');
  const episodesDiv = document.getElementById('episodes');
  const providersDiv = document.getElementById('providers');
  const banner = document.getElementById('banner');
  const mediaTitle = document.getElementById('media-title');
  const synopsisElem = document.getElementById('media-synopsis');

  // --- Robust Element Checks ---
  const requiredElements = { mediaTitle, banner, seasonsContainer, episodesDiv, providersDiv, playerContainer, synopsisElem };
  const missingElements = Object.entries(requiredElements).filter(([_, el]) => !el).map(([key]) => key);
  if (missingElements.length > 0) {
      console.error('Missing critical DOM elements:', missingElements);
      document.body.innerHTML = `<p style="color: red; padding: 20px;">Error: Page structure incomplete (${missingElements.join(', ')} missing). Cannot load media details.</p>`;
      return;
  }
  // --- End Element Checks ---

  console.log('Initial playerContainer classes:', playerContainer.className);
  playerContainer.classList.add('hidden');

  // --- URL Parsing and Validation ---
  const path = window.location.pathname.split('/');
  const typeSlug = path[1]?.toLowerCase();
  const itemId = path[2];
  let itemType;

  switch (typeSlug) {
      case 'movies': itemType = 'MOVIE'; break;
      case 'series': itemType = 'SERIES'; break;
      case 'anime': itemType = 'ANIME'; break;
      case 'livetv': itemType = 'LIVETV'; break;
      default: itemType = null;
  }

  if (!itemType) {
      console.error(`Invalid item type derived from URL slug: "${typeSlug}"`);
       document.body.innerHTML = `<p style="color: red; padding: 20px;">Error: Invalid media type "${typeSlug || '(empty)'}" in URL.</p>`;
       return;
  }
  if (!itemId) {
      console.error(`Invalid item ID derived from URL: "${itemId || '(empty)'}"`);
       document.body.innerHTML = `<p style="color: red; padding: 20px;">Error: Missing media ID in URL.</p>`;
       return;
  }
  if (!/^[a-f\d]{24}$/i.test(itemId)) {
      console.warn(`Potentially invalid item ID format: "${itemId}"`);
      // Optionally display warning or error page
  }
  // --- End URL Parsing ---

  let mediaData = null;
  let hlsInstance = null;

  // --- GraphQL Helper ---
  async function fetchGraphQL(query, variables = {}) {
    // console.log('Media.js: Fetching GraphQL', { query: query.substring(0, 100) + '...', variables });
    try {
      const response = await fetch('/graphql', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
        body: JSON.stringify({ query, variables }),
      });

      if (!response.ok) {
        let errorBody = `Status: ${response.status} ${response.statusText}`;
        try { const bodyText = await response.text(); errorBody += ` - Body: ${bodyText.substring(0, 200)}`; } catch(e) {}
        throw new Error(`GraphQL fetch failed: ${errorBody}`);
      }

      const responseJson = await response.json();
      const { data, errors } = responseJson;

      if (errors) {
        console.error('Media.js: GraphQL Errors:', errors);
        const combinedErrorMessage = errors.map(e => e.message + (e.path ? ` (path: ${e.path.join('.')})` : '')).join('; ');
        throw new Error(`GraphQL Error: ${combinedErrorMessage}`);
      }
      // console.log('Media.js: GraphQL fetch successful');
      return data;
    } catch (err) {
      console.error('Media.js: fetchGraphQL Error:', err);
      throw err;
    }
  }

  // --- Player Logic ---
  async function playItem(url, isSourceStream = false, method = 'GET') {
    if (!url || !playerContainer) {
      console.error('Media.js: No valid URL or player container to play:', { url, playerContainer: !!playerContainer });
      alert("Error: Cannot play video. Invalid URL provided.");
      return;
    }

    // Check if this is a video provider URL that should be handled as an iframe
    const isVideoProviderUrl = url && (
      url.includes('waaw1.tv') ||
      url.includes('do7go.com') ||
      url.includes('streamtape.com') ||
      url.includes('doodstream.com') ||
      url.includes('vidoza.net') ||
      url.includes('voe.sx') ||
      url.includes('upstream.to') ||
      url.includes('mixdrop.co') ||
      url.includes('vudeo.net') ||
      url.includes('tipfly.xyz') ||
      url.includes('lulu.st') ||
      url.includes('/e/') // Common pattern for embed URLs
    );

    console.log('Media.js: URL check for iframe:', {
      url,
      isVideoProviderUrl,
      method,
      isSourceStream
    });

    // Force iframe method for video provider URLs
    if (isVideoProviderUrl && method !== 'iframe') {
      console.log('Media.js: Detected video provider URL, forcing iframe method:', url);
      method = 'iframe';
      isSourceStream = false;
    }

    console.log('Media.js: playItem called', { url, isSourceStream, method, itemType });

    if (playerContainer.classList.contains('hidden')) {
      history.pushState({ playerOpen: true }, '', window.location.href);
    }

    playerContainer.classList.remove('hidden');
    playerContainer.style.display = 'flex';

    // Make sure player controls are visible
    const playerControls = document.getElementById('player-controls');
    if (playerControls) {
      playerControls.style.opacity = '1';
      playerControls.classList.add('active');
    }

    // Set the title in the player
    if (mediaData) {
      let title = mediaData.displayTitle || mediaData.title || 'Now Playing';

      // Add episode info if available
      // First, check if we already have a title with episode info
      const currentTitle = window.modernPlayer && typeof window.modernPlayer.getCurrentMediaTitle === 'function'
        ? window.modernPlayer.getCurrentMediaTitle()
        : '';

      // DEBUG: Log current title check
      if (window.netStreamDebug) {
        window.netStreamDebug.log('PlayItem Title Check', {
          currentTitle: currentTitle,
          hasEpisodeInfo: currentTitle && (currentTitle.includes('S') && currentTitle.includes('E')),
          timestamp: new Date().toISOString()
        });
      }

      // If we already have a title with episode info, use that instead
      if (currentTitle && currentTitle.includes('S') && currentTitle.includes('E')) {
        console.log('Media.js: Using existing title with episode info:', currentTitle);
        title = currentTitle;
      } else {
        // Otherwise, try to get episode info from the UI
        const episodeItem = document.querySelector('#episodes .grid-item[style*="border: 1px solid"]');

        // DEBUG: Log episode item
        if (window.netStreamDebug) {
          window.netStreamDebug.log('PlayItem Episode Item', {
            episodeElement: episodeItem ? true : false,
            episodeNumber: episodeItem ? episodeItem.dataset.ep : 'None',
            episodeStyle: episodeItem ? episodeItem.getAttribute('style') : 'None',
            allHighlightedEpisodes: document.querySelectorAll('#episodes .grid-item[style*="border: 1px solid"]').length,
            timestamp: new Date().toISOString()
          });
        }

        if (episodeItem && episodeItem.dataset.ep) {
          const seasonSelect = document.getElementById('season-select');
          const currentSeason = seasonSelect ? seasonSelect.value : mediaData.season || '1';

          // Format the title for series/anime
          if (itemType === 'SERIES') {
            title = `Series: ${title} - S${currentSeason}:E${episodeItem.dataset.ep}`;
          } else if (itemType === 'ANIME') {
            title = `Anime: ${title} - S${currentSeason}:E${episodeItem.dataset.ep}`;
          } else {
            title += ` - S${currentSeason}:E${episodeItem.dataset.ep}`;
          }

          console.log('Media.js: Updated player title with episode info:', title);
        }
      }

      // Add content type indicator
      if (itemType) {
        let typeLabel = '';
        switch (itemType) {
          case 'MOVIE': typeLabel = 'Movie'; break;
          case 'SERIES': typeLabel = 'Series'; break;
          case 'ANIME': typeLabel = 'Anime'; break;
          case 'LIVETV': typeLabel = 'Live TV'; break;
          default: typeLabel = '';
        }

        if (typeLabel && !title.includes(typeLabel)) {
          title = `${typeLabel}: ${title}`;
        }
      }

      // Try to set the title using the modern player
      if (window.modernPlayer && typeof window.modernPlayer.setTitle === 'function') {
        window.modernPlayer.setTitle(title);
        console.log('Media.js: Set player title using modernPlayer.setTitle to:', title);
      } else {
        console.log('Media.js: Modern player not available, setting title directly');

        // Try to set the title directly
        const playerTitle = document.getElementById('player-title');
        if (playerTitle) {
          playerTitle.textContent = title;
          console.log('Media.js: Set player title directly to:', title);
        } else {
          console.log('Media.js: Player title element not found');

          // Try again after a short delay
          setTimeout(() => {
            const delayedPlayerTitle = document.getElementById('player-title');
            if (delayedPlayerTitle) {
              delayedPlayerTitle.textContent = title;
              console.log('Media.js: Set player title after delay to:', title);
            } else {
              console.error('Media.js: Player title element still not found after delay');
            }
          }, 500);
        }
      }
    } else {
      console.log('Media.js: Media data not available for title setting');
    }

    // Track this item as watched
    if (mediaData) {
      // Get episode info if available
      let episodeInfo = null;
      const episodeItem = document.querySelector('#episodes .grid-item[style*="border: 1px solid"]');
      if (episodeItem && episodeItem.dataset.ep) {
        const seasonSelect = document.getElementById('season-select');
        const currentSeason = seasonSelect ? seasonSelect.value : mediaData.season || '1';
        episodeInfo = `S${currentSeason}:E${episodeItem.dataset.ep}`;
      }

      // Create watched item object
      const watchedItem = {
        id: itemId,
        title: mediaData.displayTitle || mediaData.title,
        thumbnail: mediaData.thumbnail,
        image: mediaData.image,
        type: itemType === 'MOVIE' ? 'movies' : itemType, // Convert MOVIE to lowercase 'movies' for consistency
        episodeInfo: episodeInfo
      };

      // For anime, add additional data needed for proper image display
      if (itemType === 'ANIME') {
        // Add Jikan data if available
        if (mediaData.jikan && mediaData.jikan.mal_id) {
          watchedItem.jikan = {
            mal_id: mediaData.jikan.mal_id
          };
        }

        // Add TMDB data if available
        if (mediaData.tmdb && mediaData.tmdb.id) {
          watchedItem.tmdb = {
            id: mediaData.tmdb.id
          };
        }

        // Add anime-specific properties
        watchedItem.season = mediaData.season || '1';
        watchedItem.animeLanguage = mediaData.animeLanguage || 'VOSTFR';

        // Store the banner URL if we have one
        if (banner && banner.style && banner.style.backgroundImage) {
          const bgImage = banner.style.backgroundImage;
          if (bgImage && bgImage !== 'url(/default-banner.jpg)') {
            watchedItem.itemBannerUrl = bgImage.replace('url("', '').replace('")', '');
          }
        }
      }

      // Dispatch custom event for recently watched tracking
      document.dispatchEvent(new CustomEvent('media:watched', {
        detail: {
          item: watchedItem,
          progress: 0 // Initial progress
        }
      }));
    }

    const providerGridItem = document.querySelector(`.grid-item[data-stream-id] button[data-source-url="${url}"]`)?.closest('.grid-item') || document.querySelector(`.grid-item[data-url="${url}"]`);
    const embedReferer = providerGridItem?.dataset.url || window.location.href;
    let targetUrl = url;

    if (isSourceStream && !url.startsWith('/proxy-video')) {
      // Check if the URL already contains headers parameter
      if (url.includes('headers=')) {
        // Extract the headers parameter
        const headersMatch = url.match(/headers=([^&]+)/);
        if (headersMatch && headersMatch[1]) {
          const headersParam = headersMatch[1];
          // Remove the headers parameter from the URL
          const cleanUrl = url.replace(/[?&]headers=[^&]+/, '');
          // Add it to the proxy URL
          targetUrl = `/proxy-video?url=${encodeURIComponent(cleanUrl)}&referer=${encodeURIComponent(embedReferer)}&headers=${headersParam}`;
        } else {
          targetUrl = `/proxy-video?url=${encodeURIComponent(url)}&referer=${encodeURIComponent(embedReferer)}`;
        }
      } else {
        targetUrl = `/proxy-video?url=${encodeURIComponent(url)}&referer=${encodeURIComponent(embedReferer)}`;
      }
      console.log("Media.js: Using proxy URL:", targetUrl);
    }
    else if (isSourceStream) {
      console.log("Media.js: Using already proxied URL:", targetUrl);
    }

    if (hlsInstance) hlsInstance.destroy();
    hlsInstance = null;

    if (player) {
      player.src = '';
      player.style.display = 'none';
      player.removeAttribute('src');
      player.load();
    }

    if (playerIframe) {
      playerIframe.src = 'about:blank';
      playerIframe.style.display = 'none';
    }

    // Check if this is a witv.skin URL
    const isWitvSkin = url.includes('witv.skin') || url.includes('play.witv');

    // Ensure streams with .m3u8 URLs are always played via HLS if supported
    // For witv.skin URLs, we'll try direct playback first if it's a LiveTV item
    if (isSourceStream && url.includes('.m3u8') && Hls.isSupported() && player) {
        console.log('Media.js: Playing HLS stream via HTML5 player');

        // Make sure we preserve the current title with episode info
        if (window.modernPlayer && typeof window.modernPlayer.getCurrentMediaTitle === 'function') {
          const currentTitle = window.modernPlayer.getCurrentMediaTitle();

          // DEBUG: Log current title for HLS stream
          if (window.netStreamDebug) {
            window.netStreamDebug.log('HLS Stream Title Check', {
              currentTitle: currentTitle,
              hasEpisodeInfo: currentTitle && (currentTitle.includes('S') && currentTitle.includes('E')),
              url: url,
              isOneupload: url.includes('oneupload.to'),
              timestamp: new Date().toISOString()
            });
          }

          // If we have a title with episode info, make sure it's set in the player
          if (currentTitle && currentTitle.includes('S') && currentTitle.includes('E')) {
            console.log('Media.js: Preserving title with episode info for HLS stream:', currentTitle);

            // Set the title in the player
            if (window.modernPlayer && typeof window.modernPlayer.setTitle === 'function') {
              window.modernPlayer.setTitle(currentTitle);
            }
          }
        }

        // For LiveTV, if the URL is already proxied (starts with /proxy-video), remove the proxy part to prevent double proxying
        if (itemType === 'LIVETV' && targetUrl.startsWith('/proxy-video?')) {
          targetUrl = decodeURIComponent(targetUrl.split('url=')[1].split('&')[0]);
        }

        // Special handling for witv.skin URLs which might be MP4 content with m3u8 extension
        if (isWitvSkin) {
          console.log('Media.js: Detected witv.skin URL, adding special handling');

          // For LiveTV witv.skin URLs, try direct playback first
          if (itemType === 'LIVETV') {
            console.log('Media.js: LiveTV witv.skin URL detected, trying direct playback first');

            // Check if the URL contains a token
            const hasToken = targetUrl.includes('token=');

            // Also check if the URL is already a proxied URL
            const isProxied = targetUrl.startsWith('/proxy-video');

            if (hasToken || isProxied) {
              console.log(`Media.js: URL ${hasToken ? 'contains token' : 'is proxied'}, using direct playback`);

              // We'll still initialize HLS as a fallback, but we'll try direct playback first
              try {
                // Set up direct playback
                player.style.display = 'block';
                player.src = targetUrl;

                // Add event listener for errors to fall back to HLS if direct playback fails
                const errorHandler = function(e) {
                  console.error('Media.js: Direct playback error for witv.skin URL:', e);
                  console.log('Media.js: Falling back to HLS playback for witv.skin URL');

                  // Remove the error handler to avoid duplicate handling
                  player.removeEventListener('error', errorHandler);

                  // Continue with HLS initialization
                  initializeHlsPlayback();
                };

                player.addEventListener('error', errorHandler);

                // Try to play directly
                player.play().catch(err => {
                  console.error('Media.js: Direct play error for witv.skin URL:', err);
                  // Continue with HLS initialization
                  initializeHlsPlayback();
                });

                // Return early to avoid initializing HLS right away
                // We'll initialize it only if direct playback fails
                return;
              } catch (err) {
                console.error('Media.js: Error setting up direct playback for witv.skin URL:', err);
                // Continue with HLS initialization
              }
            } else {
              console.log('Media.js: URL does not contain token, using HLS playback');
              // Continue with HLS initialization
            }
          }
        }

        // Force the use of the modern player UI
        let playerWrapper = document.getElementById('player-wrapper');

        // If the wrapper doesn't exist, create it
        if (!playerWrapper) {
          console.log('Media.js: Creating player wrapper');

          // First, check if there's an existing wrapper and remove it
          const existingWrapper = document.getElementById('player-wrapper');
          if (existingWrapper) {
            // Remove the existing wrapper
            playerContainer.removeChild(existingWrapper);
          }

          // Create the wrapper
          playerWrapper = document.createElement('div');
          playerWrapper.id = 'player-wrapper';

          // Create the logo
          const playerLogo = document.createElement('div');
          playerLogo.id = 'player-logo';
          playerLogo.textContent = 'NetStream';

          // Create the controls
          const playerControls = document.createElement('div');
          playerControls.id = 'player-controls';
          playerControls.innerHTML = `
            <div id="player-title-bar">
              <div id="player-title">Now Playing</div>
            </div>

            <div id="player-progress-container">
              <div id="player-progress-buffer"></div>
              <div id="player-progress-bar"></div>
              <div id="player-time-tooltip">00:00</div>
            </div>

            <div id="player-buttons">
              <div class="player-button-group">
                <button id="player-play-pause" class="player-button" aria-label="Play/Pause">
                  <i class="fas fa-play"></i>
                </button>

                <div id="player-volume-container">
                  <button id="player-volume-toggle" class="player-button" aria-label="Mute/Unmute">
                    <i class="fas fa-volume-up"></i>
                  </button>
                  <div id="player-volume-slider">
                    <div id="player-volume-level"></div>
                  </div>
                </div>

                <div id="player-time-display">
                  <span id="player-current-time">00:00</span> / <span id="player-duration">00:00</span>
                </div>
              </div>

              <div class="player-button-group">
                <button id="player-settings" class="player-button" aria-label="Settings">
                  <i class="fas fa-cog"></i>
                </button>
                <button id="player-fullscreen" class="player-button" aria-label="Fullscreen">
                  <i class="fas fa-expand"></i>
                </button>
              </div>
            </div>
          `;

          // Create the settings menu
          const settingsMenu = document.createElement('div');
          settingsMenu.id = 'player-settings-menu';
          settingsMenu.innerHTML = `
            <div class="player-settings-item" data-setting="quality">
              <span>Quality</span>
              <span id="player-quality-value">Auto</span>
            </div>
            <div class="player-settings-item" data-setting="speed">
              <span>Speed</span>
              <span id="player-speed-value">Normal</span>
            </div>
          `;

          // Get the close button
          const closeButton = document.getElementById('close-player');

          // Get the video and iframe elements
          const videoElement = document.getElementById('player');
          const iframeElement = document.getElementById('player-iframe');

          // Remove them from their current parent if they exist
          if (videoElement && videoElement.parentNode) {
            videoElement.parentNode.removeChild(videoElement);
          }

          if (iframeElement && iframeElement.parentNode) {
            iframeElement.parentNode.removeChild(iframeElement);
          }

          // Add elements to the wrapper
          playerWrapper.appendChild(playerLogo);

          // Re-add the video and iframe to the wrapper
          if (videoElement) {
            playerWrapper.appendChild(videoElement);
          } else {
            // Create a new video element if it doesn't exist
            const newVideo = document.createElement('video');
            newVideo.id = 'player';
            newVideo.setAttribute('playsinline', '');
            playerWrapper.appendChild(newVideo);
            player = newVideo; // Update the player reference
          }

          if (iframeElement) {
            playerWrapper.appendChild(iframeElement);
          } else {
            // Create a new iframe element if it doesn't exist
            const newIframe = document.createElement('iframe');
            newIframe.id = 'player-iframe';
            newIframe.setAttribute('allowfullscreen', '');
            playerWrapper.appendChild(newIframe);
            playerIframe = newIframe; // Update the iframe reference
          }

          playerWrapper.appendChild(playerControls);
          playerWrapper.appendChild(settingsMenu);

          // Add the wrapper to the container before the close button
          if (closeButton) {
            playerContainer.insertBefore(playerWrapper, closeButton);
          } else {
            playerContainer.appendChild(playerWrapper);
          }

          console.log('Media.js: Player wrapper created successfully');
        } else {
          console.log('Media.js: Using existing player wrapper');
        }

        // Function to style the player and ensure video is in the wrapper
        function stylePlayer() {
          console.log('Media.js: Ensuring video is in the player wrapper');

          // Get the player wrapper
          let playerWrapper = document.getElementById('player-wrapper');

          // Get the existing video element
          let videoElement = document.getElementById('player');

          // Only create a new video element if one doesn't exist
          if (!videoElement) {
            console.log('Media.js: Video element not found, creating a new one');

            // Create a new video element
            videoElement = document.createElement('video');
            videoElement.id = 'player';
            videoElement.setAttribute('playsinline', '');

            // Add it to the wrapper
            if (playerWrapper) {
              // Insert as the first child
              if (playerWrapper.firstChild) {
                playerWrapper.insertBefore(videoElement, playerWrapper.firstChild);
              } else {
                playerWrapper.appendChild(videoElement);
              }
            } else {
              console.error('Media.js: Player wrapper not found for video element creation');
            }
          } else {
            console.log('Media.js: Using existing video element');
          }

          // Style the video element
          if (videoElement) {
            videoElement.style.display = 'block';
            videoElement.style.width = '100%';
            videoElement.style.height = '100%';
            videoElement.style.objectFit = 'contain';
            videoElement.style.position = 'static';
            videoElement.style.zIndex = 'auto';
            videoElement.style.opacity = '1';
            videoElement.style.visibility = 'visible';

            // Update the global player reference
            player = videoElement;
          }
          console.log('Media.js: Styling player elements');

          // Refresh the wrapper reference
          playerWrapper = document.getElementById('player-wrapper');
          if (playerWrapper) {
            console.log('Media.js: Styling player wrapper');
            playerWrapper.style.position = 'relative';
            playerWrapper.style.width = '90%';
            playerWrapper.style.height = '90%';
            playerWrapper.style.maxWidth = '1280px';
            playerWrapper.style.maxHeight = '720px';
            playerWrapper.style.borderRadius = '8px';
            playerWrapper.style.overflow = 'hidden';
            playerWrapper.style.background = '#000';
            playerWrapper.style.opacity = '1';
            playerWrapper.style.transform = 'scale(1)';
            playerWrapper.style.visibility = 'visible';
          } else {
            console.error('Media.js: Player wrapper not found for styling');
          }

          // Make sure all controls are visible
          const playerControls = document.getElementById('player-controls');
          if (playerControls) {
            console.log('Media.js: Styling player controls');
            playerControls.style.position = 'absolute';
            playerControls.style.bottom = '0';
            playerControls.style.left = '0';
            playerControls.style.width = '100%';
            playerControls.style.padding = '20px 20px';
            playerControls.style.background = 'linear-gradient(to top, rgba(0, 0, 0, 0.7) 70%, rgba(0,0,0,0))';
            playerControls.style.display = 'flex';
            playerControls.style.flexDirection = 'column';
            playerControls.style.opacity = '1';
            playerControls.style.visibility = 'visible';
            playerControls.style.zIndex = '2';

            // Make sure all child elements are visible
            const allElements = playerControls.querySelectorAll('*');
            allElements.forEach(el => {
              el.style.opacity = '1';
              el.style.visibility = 'visible';
            });
          } else {
            console.error('Media.js: Player controls not found for styling');
          }

          // Make sure the title bar is properly styled
          const playerTitleBar = document.getElementById('player-title-bar');
          if (playerTitleBar) {
            console.log('Media.js: Styling player title bar');
            playerTitleBar.style.display = 'flex';
            playerTitleBar.style.justifyContent = 'space-between';
            playerTitleBar.style.alignItems = 'center';
            playerTitleBar.style.marginBottom = '15px';
            playerTitleBar.style.width = '100%';
          } else {
            console.error('Media.js: Player title bar not found for styling');
          }

          // Make sure the title is visible and properly styled
          const playerTitle = document.getElementById('player-title');
          if (playerTitle) {
            console.log('Media.js: Styling player title');
            playerTitle.style.color = '#ffffff';
            playerTitle.style.fontSize = '1.2em';
            playerTitle.style.fontWeight = '500';
            playerTitle.style.textShadow = '1px 1px 2px rgba(0, 0, 0, 0.8)';
            playerTitle.style.whiteSpace = 'nowrap';
            playerTitle.style.overflow = 'hidden';
            playerTitle.style.textOverflow = 'ellipsis';
            playerTitle.style.maxWidth = '80%';
            playerTitle.style.opacity = '1';
            playerTitle.style.visibility = 'visible';

            // Set the title if we have media data
            if (mediaData) {
              playerTitle.textContent = mediaData.displayTitle || mediaData.title || 'Now Playing';
              console.log('Media.js: Set player title to:', playerTitle.textContent);
            }
          } else {
            console.error('Media.js: Player title not found for styling');
          }

          // Make sure the progress container is properly styled
          const progressContainer = document.getElementById('player-progress-container');
          if (progressContainer) {
            console.log('Media.js: Styling progress container');
            progressContainer.style.width = '100%';
            progressContainer.style.height = '6px';
            progressContainer.style.background = 'rgba(255, 255, 255, 0.2)';
            progressContainer.style.borderRadius = '5px';
            progressContainer.style.cursor = 'pointer';
            progressContainer.style.position = 'relative';
            progressContainer.style.marginBottom = '15px';
            progressContainer.style.overflow = 'hidden';
          } else {
            console.error('Media.js: Progress container not found for styling');
          }

          // Make sure the buttons container is properly styled
          const playerButtons = document.getElementById('player-buttons');
          if (playerButtons) {
            console.log('Media.js: Styling player buttons');
            playerButtons.style.display = 'flex';
            playerButtons.style.alignItems = 'center';
            playerButtons.style.justifyContent = 'space-between';
            playerButtons.style.width = '100%';
            playerButtons.style.marginTop = '5px';
            playerButtons.style.opacity = '1';
            playerButtons.style.visibility = 'visible';
          } else {
            console.error('Media.js: Player buttons not found for styling');
          }

          // Make sure all button groups are properly styled
          const buttonGroups = document.querySelectorAll('.player-button-group');
          if (buttonGroups.length > 0) {
            console.log('Media.js: Styling button groups');
            buttonGroups.forEach(group => {
              group.style.display = 'flex';
              group.style.alignItems = 'center';
              group.style.gap = '15px';
            });
          } else {
            console.error('Media.js: Button groups not found for styling');
          }

          // Make sure all buttons are properly styled
          const buttons = document.querySelectorAll('.player-button');
          if (buttons.length > 0) {
            console.log('Media.js: Styling buttons');
            buttons.forEach(button => {
              button.style.background = 'transparent';
              button.style.border = 'none';
              button.style.color = '#ffffff';
              button.style.fontSize = '1.2em';
              button.style.cursor = 'pointer';
              button.style.width = '40px';
              button.style.height = '40px';
              button.style.display = 'flex';
              button.style.alignItems = 'center';
              button.style.justifyContent = 'center';
              button.style.borderRadius = '50%';
              button.style.padding = '0';
              button.style.margin = '0';
            });
          } else {
            console.error('Media.js: Buttons not found for styling');
          }

          // Make sure the time display is properly styled
          const timeDisplay = document.getElementById('player-time-display');
          if (timeDisplay) {
            console.log('Media.js: Styling time display');
            timeDisplay.style.color = '#ffffff';
            timeDisplay.style.fontSize = '0.9em';
            timeDisplay.style.margin = '0 15px';
            timeDisplay.style.whiteSpace = 'nowrap';
            timeDisplay.style.minWidth = '100px';
            timeDisplay.style.textAlign = 'center';
          } else {
            console.error('Media.js: Time display not found for styling');
          }

          // Make sure the logo is properly styled
          const playerLogo = document.getElementById('player-logo');
          if (playerLogo) {
            console.log('Media.js: Styling player logo');
            playerLogo.style.position = 'absolute';
            playerLogo.style.top = '20px';
            playerLogo.style.left = '20px';
            playerLogo.style.color = '#00bcd4';
            playerLogo.style.fontSize = '1.5em';
            playerLogo.style.fontWeight = '700';
            playerLogo.style.fontFamily = 'Poppins, sans-serif';
            playerLogo.style.textShadow = '1px 1px 2px rgba(0, 0, 0, 0.8)';
            playerLogo.style.opacity = '0.8';
            playerLogo.style.zIndex = '2';
            playerLogo.style.background = 'rgba(0, 0, 0, 0.5)';
            playerLogo.style.padding = '5px 10px';
            playerLogo.style.borderRadius = '4px';
            playerLogo.style.display = 'block';
            playerLogo.style.width = 'auto';
            playerLogo.style.height = 'auto';
          } else {
            console.error('Media.js: Player logo not found for styling');
          }

          // Make sure the settings menu is properly styled
          const settingsMenu = document.getElementById('player-settings-menu');
          if (settingsMenu) {
            console.log('Media.js: Styling settings menu');
            settingsMenu.style.position = 'absolute';
            settingsMenu.style.bottom = '70px';
            settingsMenu.style.right = '20px';
            settingsMenu.style.background = 'rgba(28, 28, 28, 0.9)';
            settingsMenu.style.backdropFilter = 'blur(10px)';
            settingsMenu.style.webkitBackdropFilter = 'blur(10px)';
            settingsMenu.style.borderRadius = '8px';
            settingsMenu.style.padding = '12px';
            settingsMenu.style.display = 'none';
            settingsMenu.style.flexDirection = 'column';
            settingsMenu.style.gap = '10px';
            settingsMenu.style.minWidth = '200px';
            settingsMenu.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.5)';
            settingsMenu.style.zIndex = '3';
            settingsMenu.style.border = '1px solid rgba(255, 255, 255, 0.1)';
            settingsMenu.style.margin = '0';
            settingsMenu.style.width = 'auto';
            settingsMenu.style.height = 'auto';

            // Style the settings items
            const settingsItems = settingsMenu.querySelectorAll('.player-settings-item');
            settingsItems.forEach(item => {
              item.style.display = 'flex';
              item.style.justifyContent = 'space-between';
              item.style.alignItems = 'center';
              item.style.color = '#ffffff';
              item.style.padding = '10px 12px';
              item.style.borderRadius = '4px';
              item.style.cursor = 'pointer';
              item.style.fontFamily = 'Roboto, sans-serif';
              item.style.fontSize = '0.95em';
              item.style.margin = '0';
              item.style.width = '100%';
              item.style.textAlign = 'left';

              // Style the spans inside the settings items
              const spans = item.querySelectorAll('span');
              spans.forEach(span => {
                span.style.display = 'inline-block';
                span.style.margin = '0';
                span.style.padding = '0';
              });

              // Style the value span
              const valueSpan = item.querySelector('span:last-child');
              if (valueSpan) {
                valueSpan.style.fontWeight = '500';
                valueSpan.style.color = '#4dd0e1';
              }
            });
          } else {
            console.error('Media.js: Settings menu not found for styling');
          }

          console.log('Media.js: Player styling complete');
        }

        // Connect the video element to our modern player
        if (window.modernPlayer && typeof window.modernPlayer.connectVideoElement === 'function') {
          console.log('Media.js: Connecting video element to modern player');

          // First refresh elements to ensure we have the latest references
          if (window.modernPlayer.refreshElements) {
            window.modernPlayer.refreshElements();
          }

          // Then connect the video element
          window.modernPlayer.connectVideoElement();

          // Style the player
          stylePlayer();
        } else {
          console.log('Media.js: Modern player connection function not available, loading player.js');

          // Try to load player.js dynamically
          const script = document.createElement('script');
          script.src = '/js/player.js';
          script.onload = () => {
            console.log('Media.js: player.js loaded dynamically');

            // Wait a bit for the script to initialize
            setTimeout(() => {
              if (window.modernPlayer && typeof window.modernPlayer.connectVideoElement === 'function') {
                console.log('Media.js: Connecting video element to modern player after dynamic load');

                // First refresh elements to ensure we have the latest references
                if (window.modernPlayer.refreshElements) {
                  window.modernPlayer.refreshElements();
                }

                // Then connect the video element
                window.modernPlayer.connectVideoElement();

                // Style the player
                stylePlayer();
              } else {
                console.error('Media.js: Modern player still not available after loading player.js');

                // Style the player anyway
                stylePlayer();
              }
            }, 100);
          };
          document.head.appendChild(script);

          // Style the player anyway in case the script fails to load
          setTimeout(stylePlayer, 200);
        }

        // Make sure the player is visible and properly styled
        if (player) {
          player.style.display = 'block';
          player.style.position = 'static';
          player.style.zIndex = 'auto';
          player.style.opacity = '1';
          player.style.visibility = 'visible';
          player.style.width = '100%';
          player.style.height = '100%';
          player.style.objectFit = 'contain';
        } else {
          console.error('Media.js: Player element not found for HLS playback');
          // Try to get the player again
          player = document.getElementById('player');
          if (!player) {
            console.error('Media.js: Still cannot find player element, creating a new one');
            // Create a new video element
            player = document.createElement('video');
            player.id = 'player';
            player.setAttribute('playsinline', '');
            player.style.display = 'block';
            player.style.width = '100%';
            player.style.height = '100%';
            player.style.objectFit = 'contain';
            player.style.position = 'static';
            player.style.zIndex = 'auto';
            player.style.opacity = '1';
            player.style.visibility = 'visible';

            // Add it to the player container
            if (playerWrapper) {
              playerWrapper.insertBefore(player, playerWrapper.firstChild);
            } else if (playerContainer) {
              playerContainer.insertBefore(player, playerContainer.firstChild);
            } else {
              console.error('Media.js: Cannot find container for player element');
              return; // Cannot proceed without a player element
            }
          }
        }

        // Create HLS instance with more robust configuration
        hlsInstance = new Hls({
          debug: false,
          // Add more aggressive fragment loading timeouts
          fragLoadingTimeOut: 60000,
          manifestLoadingTimeOut: 30000,
          // Increase the number of retry attempts
          fragLoadingMaxRetry: 6,
          manifestLoadingMaxRetry: 4,
          // Reduce the backoff time between retries
          fragLoadingRetryDelay: 500,
          manifestLoadingRetryDelay: 500,
          // Enable low latency mode for better streaming
          lowLatencyMode: true,
          // Adjust buffer settings
          maxBufferLength: 30,
          maxMaxBufferLength: 60
        });

        // Store the source URL and other info for later use
        const hlsSourceUrl = targetUrl;

        // Function to initialize HLS playback
        function initializeHlsPlayback() {
          console.log('Media.js: Initializing HLS playback');

          // Make sure we have the latest player reference
          player = document.getElementById('player');

          if (!player) {
            console.error('Media.js: Player element not found for HLS playback');
            return;
          }

          try {
            // Create a new HLS instance if it doesn't exist or if we're having issues
            if (!hlsInstance || !hlsInstance.media) {
              console.log('Media.js: Creating new HLS instance');

              // Destroy existing instance if it exists
              if (hlsInstance) {
                try {
                  hlsInstance.destroy();
                } catch (destroyError) {
                  console.warn('Media.js: Error destroying HLS instance:', destroyError);
                }
              }

              // Create a new instance
              hlsInstance = new Hls({
                debug: false,
                fragLoadingTimeOut: 60000,
                manifestLoadingTimeOut: 30000,
                fragLoadingMaxRetry: 6,
                manifestLoadingMaxRetry: 4,
                fragLoadingRetryDelay: 500,
                manifestLoadingRetryDelay: 500,
                lowLatencyMode: true,
                maxBufferLength: 30,
                maxMaxBufferLength: 60
              });

              // Set up HLS event handlers
              hlsInstance.on(Hls.Events.MANIFEST_PARSED, function() {
                console.log('Media.js: HLS manifest parsed successfully');

                // Get quality levels
                const levels = hlsInstance.levels;
                if (levels && levels.length > 0) {
                  console.log('Media.js: HLS loaded', levels.length, 'quality levels');
                }

                // Start playback
                console.log('Media.js: Starting HLS playback');
                player.play().catch(e => {
                  console.warn('Media.js: Auto-play prevented:', e);
                  // Show play button or play UI indicator here
                });
              });

              hlsInstance.on(Hls.Events.ERROR, function(event, data) {
                console.log('Media.js: HLS error:', data);
                if (data.fatal) {
                  switch(data.type) {
                    case Hls.ErrorTypes.NETWORK_ERROR:
                      // Try to recover network error
                      console.log('Media.js: Fatal network error, trying to recover');
                      hlsInstance.startLoad();
                      break;
                    case Hls.ErrorTypes.MEDIA_ERROR:
                      console.log('Media.js: Fatal media error, trying to recover');
                      hlsInstance.recoverMediaError();
                      break;
                    default:
                      // Cannot recover
                      console.error('Media.js: Fatal error, cannot recover');
                      hlsInstance.destroy();
                      break;
                  }
                }
              });
            } else {
              // Safely detach from any existing media element
              try {
                console.log('Media.js: Detaching HLS from existing media element');
                hlsInstance.detachMedia();
              } catch (detachError) {
                console.warn('Media.js: Error detaching HLS media:', detachError);
                // If detaching fails, create a new HLS instance
                hlsInstance.destroy();
                hlsInstance = new Hls({
                  debug: false,
                  fragLoadingTimeOut: 60000,
                  manifestLoadingTimeOut: 30000,
                  fragLoadingMaxRetry: 6,
                  manifestLoadingMaxRetry: 4,
                  fragLoadingRetryDelay: 500,
                  manifestLoadingRetryDelay: 500,
                  lowLatencyMode: true,
                  maxBufferLength: 30,
                  maxMaxBufferLength: 60
                });
              }
            }

            // Load the source
            console.log('Media.js: Loading HLS source:', hlsSourceUrl);
            hlsInstance.loadSource(hlsSourceUrl);

            // Attach to the new media element
            console.log('Media.js: Attaching HLS to media element');
            hlsInstance.attachMedia(player);

            // Make HLS instance available to the modern player
            if (window.modernPlayer) {
              console.log('Media.js: Setting HLS instance in modern player');
              window.modernPlayer.setHlsInstance(hlsInstance);
            }
          } catch (error) {
            console.error('Media.js: Error initializing HLS playback:', error);
          }
        }

        // Initialize HLS playback
        initializeHlsPlayback();

        // Handle HLS events
        hlsInstance.on(Hls.Events.MANIFEST_PARSED, () => {
            console.log('Media.js: HLS manifest parsed successfully');

            // Small delay to ensure everything is ready
            setTimeout(() => {
              // Make sure we preserve the current title with episode info
              if (window.modernPlayer && typeof window.modernPlayer.getCurrentMediaTitle === 'function') {
                const currentTitle = window.modernPlayer.getCurrentMediaTitle();

                // If we have a title with episode info, make sure it's set in the player
                if (currentTitle && currentTitle.includes('S') && currentTitle.includes('E')) {
                  console.log('Media.js: Preserving title with episode info after HLS manifest parsed:', currentTitle);

                  // Set the title in the player
                  if (window.modernPlayer && typeof window.modernPlayer.setTitle === 'function') {
                    window.modernPlayer.setTitle(currentTitle);
                  }
                }
              }

              if (player) {
                const savedTime = localStorage.getItem(`videoTime-${itemId}-${url}`);
                if (savedTime && itemType !== 'LIVETV') { // Do not restore time for LiveTV
                    const seek = () => {
                      player.currentTime = parseFloat(savedTime);
                      console.log('Media.js: Restored HLS time:', savedTime);
                    };

                    if (player.readyState >= 1) {
                      seek();
                    } else {
                      player.onloadedmetadata = () => {
                        seek();
                        player.onloadedmetadata = null;
                      };
                    }
                }

                console.log('Media.js: Starting HLS playback');
                player.play().catch(err => {
                  console.error('Media.js: HLS Play error:', err);
                });
              }
            }, 100);
        });

        // If we're using the modern player, set a callback to reinitialize HLS playback after the video element is connected
        if (window.modernPlayer) {
          window.modernPlayer.onVideoElementConnected = initializeHlsPlayback;
        }

        // Enhanced error handling for HLS
        hlsInstance.on(Hls.Events.ERROR, (_, data) => {
            console.error('Media.js: HLS error:', {
                type: data.type,
                details: data.details,
                fatal: data.fatal,
                url: data.url,
                response: data.response ? {
                    code: data.response.code,
                    text: data.response.text
                } : 'No response data'
            });

            // Check if this is a witv.skin URL that might be MP4 content with m3u8 extension
            const isWitvSkin = url.includes('witv.skin') || url.includes('play.witv');

            if (data.fatal) {
                let errorMessage = `Failed to play video stream (HLS Error: ${data.details})`;
                let shouldFallbackToDirectPlay = false;

                // Try to recover based on error type
                switch (data.type) {
                    case Hls.ErrorTypes.NETWORK_ERROR:
                        console.log('Media.js: Fatal network error, trying to recover');
                        if (data.response && data.response.code === 403) {
                            errorMessage = 'Access denied by video source (403). Try another source.';
                            hidePlayer();
                        } else {
                            // For witv.skin URLs, try direct playback if HLS fails
                            if (isWitvSkin && (data.details === Hls.ErrorDetails.MANIFEST_LOAD_ERROR ||
                                              data.details === Hls.ErrorDetails.MANIFEST_PARSING_ERROR)) {
                                console.log('Media.js: witv.skin URL with manifest error, trying direct playback');
                                shouldFallbackToDirectPlay = true;
                            } else {
                                hlsInstance.startLoad(); // Try to recover from network error
                                return; // Don't show error message yet
                            }
                        }
                        break;

                    case Hls.ErrorTypes.MEDIA_ERROR:
                        console.log('Media.js: Fatal media error, trying to recover');

                        // For witv.skin URLs, try direct playback if HLS fails with media error
                        if (isWitvSkin && (data.details === 'demuxerError' ||
                                          data.details === Hls.ErrorDetails.BUFFER_APPEND_ERROR)) {
                            console.log('Media.js: witv.skin URL with media error, trying direct playback');
                            shouldFallbackToDirectPlay = true;
                        } else {
                            hlsInstance.recoverMediaError(); // Try to recover from media error
                            return; // Don't show error message yet
                        }
                        break;

                    case Hls.ErrorTypes.MUX_ERROR:
                    case Hls.ErrorTypes.OTHER_ERROR:
                    default:
                        if (isWitvSkin) {
                            console.log('Media.js: witv.skin URL with other error, trying direct playback');
                            shouldFallbackToDirectPlay = true;
                        } else if (data.details === Hls.ErrorDetails.BUFFER_APPEND_ERROR) {
                            errorMessage = 'Error processing video data. Try another source.';
                        } else if (data.details === 'demuxerError') {
                            errorMessage = 'Error decoding video stream. Try another source.';
                        }
                        break;
                }

                // If we should try direct playback for witv.skin URLs
                if (shouldFallbackToDirectPlay && isWitvSkin) {
                    console.log('Media.js: Falling back to direct playback for witv.skin URL');

                    // Destroy HLS instance
                    if (hlsInstance) {
                        hlsInstance.destroy();
                        hlsInstance = null;
                    }

                    // Set up direct playback
                    if (player) {
                        player.style.display = 'block';
                        player.src = targetUrl;
                        player.play().catch(err => {
                            console.error('Media.js: Direct playback error:', err);
                            alert('Failed to play video directly. Please try another source.');
                            hidePlayer();
                        });
                    }
                    return;
                }

                alert(errorMessage);
                hidePlayer();
            }
        });

        // Add level loading event for better quality selection
        hlsInstance.on(Hls.Events.LEVEL_LOADED, function() {
            if (hlsInstance && hlsInstance.levels && hlsInstance.levels.length > 0) {
                console.log(`Media.js: HLS loaded ${hlsInstance.levels.length} quality levels`);
                // Start with lowest quality for faster initial loading
                hlsInstance.currentLevel = 0;
                // After 5 seconds, switch to auto level
                setTimeout(() => {
                    if (hlsInstance) hlsInstance.currentLevel = -1;
                }, 5000);
            }
        });

    } else if (isSourceStream && player) {
        console.log('Media.js: Playing direct video (MP4?) via HTML5 player');

        // Force the use of the modern player UI for MP4
        let playerWrapper = document.getElementById('player-wrapper');

        // If the wrapper doesn't exist, create it
        if (!playerWrapper) {
          console.log('Media.js: Creating player wrapper for MP4');

          // First, check if there's an existing wrapper and remove it
          const existingWrapper = document.getElementById('player-wrapper');
          if (existingWrapper) {
            // Remove the existing wrapper
            playerContainer.removeChild(existingWrapper);
          }

          // Create the wrapper
          playerWrapper = document.createElement('div');
          playerWrapper.id = 'player-wrapper';

          // Create the logo
          const playerLogo = document.createElement('div');
          playerLogo.id = 'player-logo';
          playerLogo.textContent = 'NetStream';

          // Create the controls
          const playerControls = document.createElement('div');
          playerControls.id = 'player-controls';
          playerControls.innerHTML = `
            <div id="player-title-bar">
              <div id="player-title">Now Playing</div>
            </div>

            <div id="player-progress-container">
              <div id="player-progress-buffer"></div>
              <div id="player-progress-bar"></div>
              <div id="player-time-tooltip">00:00</div>
            </div>

            <div id="player-buttons">
              <div class="player-button-group">
                <button id="player-play-pause" class="player-button" aria-label="Play/Pause">
                  <i class="fas fa-play"></i>
                </button>

                <div id="player-volume-container">
                  <button id="player-volume-toggle" class="player-button" aria-label="Mute/Unmute">
                    <i class="fas fa-volume-up"></i>
                  </button>
                  <div id="player-volume-slider">
                    <div id="player-volume-level"></div>
                  </div>
                </div>

                <div id="player-time-display">
                  <span id="player-current-time">00:00</span> / <span id="player-duration">00:00</span>
                </div>
              </div>

              <div class="player-button-group">
                <button id="player-settings" class="player-button" aria-label="Settings">
                  <i class="fas fa-cog"></i>
                </button>
                <button id="player-fullscreen" class="player-button" aria-label="Fullscreen">
                  <i class="fas fa-expand"></i>
                </button>
              </div>
            </div>
          `;

          // Create the settings menu
          const settingsMenu = document.createElement('div');
          settingsMenu.id = 'player-settings-menu';
          settingsMenu.innerHTML = `
            <div class="player-settings-item" data-setting="quality">
              <span>Quality</span>
              <span id="player-quality-value">Auto</span>
            </div>
            <div class="player-settings-item" data-setting="speed">
              <span>Speed</span>
              <span id="player-speed-value">Normal</span>
            </div>
          `;

          // Get the close button
          const closeButton = document.getElementById('close-player');

          // Get the video and iframe elements
          const videoElement = document.getElementById('player');
          const iframeElement = document.getElementById('player-iframe');

          // Remove them from their current parent if they exist
          if (videoElement && videoElement.parentNode) {
            videoElement.parentNode.removeChild(videoElement);
          }

          if (iframeElement && iframeElement.parentNode) {
            iframeElement.parentNode.removeChild(iframeElement);
          }

          // Add elements to the wrapper
          playerWrapper.appendChild(playerLogo);

          // Re-add the video and iframe to the wrapper
          if (videoElement) {
            playerWrapper.appendChild(videoElement);
          } else {
            // Create a new video element if it doesn't exist
            const newVideo = document.createElement('video');
            newVideo.id = 'player';
            newVideo.setAttribute('playsinline', '');
            playerWrapper.appendChild(newVideo);
            player = newVideo; // Update the player reference
          }

          if (iframeElement) {
            playerWrapper.appendChild(iframeElement);
          } else {
            // Create a new iframe element if it doesn't exist
            const newIframe = document.createElement('iframe');
            newIframe.id = 'player-iframe';
            newIframe.setAttribute('allowfullscreen', '');
            playerWrapper.appendChild(newIframe);
            playerIframe = newIframe; // Update the iframe reference
          }

          playerWrapper.appendChild(playerControls);
          playerWrapper.appendChild(settingsMenu);

          // Add the wrapper to the container before the close button
          if (closeButton) {
            playerContainer.insertBefore(playerWrapper, closeButton);
          } else {
            playerContainer.appendChild(playerWrapper);
          }

          console.log('Media.js: Player wrapper created successfully for MP4');
        } else {
          console.log('Media.js: Using existing player wrapper for MP4');
        }

        // Define the stylePlayer function for MP4
        function stylePlayer() {
          console.log('Media.js: Ensuring video is in the player wrapper for MP4');

          // Get the player wrapper
          let playerWrapper = document.getElementById('player-wrapper');

          // Get the existing video element
          let videoElement = document.getElementById('player');

          // Only create a new video element if one doesn't exist
          if (!videoElement) {
            console.log('Media.js: Video element not found for MP4, creating a new one');

            // Create a new video element
            videoElement = document.createElement('video');
            videoElement.id = 'player';
            videoElement.setAttribute('playsinline', '');

            // Add it to the wrapper
            if (playerWrapper) {
              // Insert as the first child
              if (playerWrapper.firstChild) {
                playerWrapper.insertBefore(videoElement, playerWrapper.firstChild);
              } else {
                playerWrapper.appendChild(videoElement);
              }
            } else {
              console.error('Media.js: Player wrapper not found for video element creation for MP4');
            }
          } else {
            console.log('Media.js: Using existing video element for MP4');
          }

          // Style the video element
          if (videoElement) {
            videoElement.style.display = 'block';
            videoElement.style.width = '100%';
            videoElement.style.height = '100%';
            videoElement.style.objectFit = 'contain';
            videoElement.style.position = 'static';
            videoElement.style.zIndex = 'auto';
            videoElement.style.opacity = '1';
            videoElement.style.visibility = 'visible';

            // Update the global player reference
            player = videoElement;
          }
          console.log('Media.js: Styling player elements for MP4');

          // Refresh the wrapper reference
          playerWrapper = document.getElementById('player-wrapper');
          if (playerWrapper) {
            console.log('Media.js: Styling player wrapper for MP4');
            playerWrapper.style.position = 'relative';
            playerWrapper.style.width = '90%';
            playerWrapper.style.height = '90%';
            playerWrapper.style.maxWidth = '1280px';
            playerWrapper.style.maxHeight = '720px';
            playerWrapper.style.borderRadius = '8px';
            playerWrapper.style.overflow = 'hidden';
            playerWrapper.style.background = '#000';
            playerWrapper.style.opacity = '1';
            playerWrapper.style.transform = 'scale(1)';
            playerWrapper.style.visibility = 'visible';
          } else {
            console.error('Media.js: Player wrapper not found for styling MP4');
          }

          // Style all the other elements (controls, buttons, etc.)
          // ... (same styling code as for HLS)

          // Make sure all controls are visible
          const playerControls = document.getElementById('player-controls');
          if (playerControls) {
            console.log('Media.js: Styling player controls for MP4');
            playerControls.style.position = 'absolute';
            playerControls.style.bottom = '0';
            playerControls.style.left = '0';
            playerControls.style.width = '100%';
            playerControls.style.padding = '20px 20px';
            playerControls.style.background = 'linear-gradient(to top, rgba(0, 0, 0, 0.7) 70%, rgba(0,0,0,0))';
            playerControls.style.display = 'flex';
            playerControls.style.flexDirection = 'column';
            playerControls.style.opacity = '1';
            playerControls.style.visibility = 'visible';
            playerControls.style.zIndex = '2';

            // Make sure all child elements are visible
            const allElements = playerControls.querySelectorAll('*');
            allElements.forEach(el => {
              el.style.opacity = '1';
              el.style.visibility = 'visible';
            });
          }

          // Style all other elements (title, progress bar, etc.)
          // ... (similar to HLS styling)
        }

        // Connect the video element to our modern player
        if (window.modernPlayer && typeof window.modernPlayer.connectVideoElement === 'function') {
          console.log('Media.js: Connecting video element to modern player for MP4');

          // First refresh elements to ensure we have the latest references
          if (window.modernPlayer.refreshElements) {
            window.modernPlayer.refreshElements();
          }

          // Then connect the video element
          window.modernPlayer.connectVideoElement();

          // Style the player
          stylePlayer();
        } else {
          console.log('Media.js: Modern player connection function not available for MP4, loading player.js');

          // Try to load player.js dynamically
          const script = document.createElement('script');
          script.src = '/js/player.js';
          script.onload = () => {
            console.log('Media.js: player.js loaded dynamically for MP4');

            // Wait a bit for the script to initialize
            setTimeout(() => {
              if (window.modernPlayer && typeof window.modernPlayer.connectVideoElement === 'function') {
                console.log('Media.js: Connecting video element to modern player after dynamic load for MP4');

                // First refresh elements to ensure we have the latest references
                if (window.modernPlayer.refreshElements) {
                  window.modernPlayer.refreshElements();
                }

                // Then connect the video element
                window.modernPlayer.connectVideoElement();

                // Style the player
                stylePlayer();
              } else {
                console.error('Media.js: Modern player still not available after loading player.js for MP4');

                // Style the player anyway
                stylePlayer();
              }
            }, 100);
          };
          document.head.appendChild(script);

          // Style the player anyway in case the script fails to load
          setTimeout(stylePlayer, 200);
        }

        // Make sure the player is visible and properly styled
        if (player) {
          player.style.display = 'block';
          player.style.position = 'static';
          player.style.zIndex = 'auto';
          player.style.opacity = '1';
          player.style.visibility = 'visible';
          player.style.width = '100%';
          player.style.height = '100%';
          player.style.objectFit = 'contain';
          player.src = targetUrl;
        } else {
          console.error('Media.js: Player element not found for MP4 playback');
          // Try to get the player again
          player = document.getElementById('player');
          if (!player) {
            console.error('Media.js: Still cannot find player element for MP4, creating a new one');
            // Create a new video element
            player = document.createElement('video');
            player.id = 'player';
            player.setAttribute('playsinline', '');
            player.style.display = 'block';
            player.style.width = '100%';
            player.style.height = '100%';
            player.style.objectFit = 'contain';
            player.style.position = 'static';
            player.style.zIndex = 'auto';
            player.style.opacity = '1';
            player.style.visibility = 'visible';

            // Add it to the player container
            if (playerWrapper) {
              playerWrapper.insertBefore(player, playerWrapper.firstChild);
            } else if (playerContainer) {
              playerContainer.insertBefore(player, playerContainer.firstChild);
            } else {
              console.error('Media.js: Cannot find container for MP4 player element');
              return; // Cannot proceed without a player element
            }

            // Set the source
            player.src = targetUrl;
          }
        }

        const savedTime = localStorage.getItem(`videoTime-${itemId}-${url}`);
        if (savedTime) {
          player.onloadedmetadata = () => {
            player.currentTime = parseFloat(savedTime);
            console.log('Media.js: Restored MP4 time:', savedTime);
            player.onloadedmetadata = null;
          };
        }

        player.play().catch(err => console.error('Media.js: MP4 Play error:', err));
    } else if ((!isSourceStream || method === 'iframe') && playerIframe) {
        const isIphone = /iPhone/.test(navigator.userAgent);
        if(isIphone) console.log("Detected iPhone. Loading in Iframe.");

        console.log('Media.js: Playing via iframe:', url);

        // Check if we have the modernPlayer with iframe support
        if (window.modernPlayer && typeof window.modernPlayer.loadIframeSource === 'function') {
          console.log('Media.js: Using modernPlayer for iframe content');

          // Get the current title with episode info if available
          const currentTitle = window.modernPlayer && typeof window.modernPlayer.getCurrentMediaTitle === 'function'
            ? window.modernPlayer.getCurrentMediaTitle()
            : '';

          // DEBUG: Log current title check for iframe
          if (window.netStreamDebug) {
            window.netStreamDebug.log('Iframe Title Check', {
              currentTitle: currentTitle,
              hasEpisodeInfo: currentTitle && (currentTitle.includes('S') && currentTitle.includes('E')),
              timestamp: new Date().toISOString()
            });
          }

          // If we already have a title with episode info, use that instead of a generic title
          let title;
          if (currentTitle && currentTitle.includes('S') && currentTitle.includes('E')) {
            // Use the existing title with episode info
            title = currentTitle;
            console.log('Media.js: Using existing title with episode info for iframe:', title);
          } else {
            // Use a generic title
            title = `${mediaData?.displayTitle || mediaData?.title || 'External Content'} (External Player)`;
            console.log('Media.js: Using generic title for iframe:', title);
          }

          // Set the title
          window.modernPlayer.setTitle(title);

          // Style the iframe directly to ensure it's visible
          if (playerIframe) {
            console.log('Media.js: Styling iframe directly');

            // Hide video element
            if (player) {
              player.style.display = 'none';
            }

            // Hide controls
            const playerControls = document.getElementById('player-controls');
            if (playerControls) {
              playerControls.style.display = 'none';
            }

            // Style and show iframe
            playerIframe.style.display = 'block';
            playerIframe.style.width = '100%';
            playerIframe.style.height = '100%';
            playerIframe.style.border = 'none';
            playerIframe.style.position = 'absolute';
            playerIframe.style.top = '0';
            playerIframe.style.left = '0';
            playerIframe.style.zIndex = '10'; // Higher z-index to ensure it's on top
            playerIframe.setAttribute('allowfullscreen', '');

            // Clear any existing src first
            playerIframe.src = 'about:blank';

            // Force a reflow
            void playerIframe.offsetWidth;

            // Set the new src
            playerIframe.src = url;

            console.log('Media.js: Iframe styled and source set to:', url);
          }

          // Let the player integration handle this
          return;
        }

        // Fallback to the old method if modernPlayer is not available
        playerIframe.style.display = 'block';
        playerIframe.style.width = '100%';
        playerIframe.style.height = '100%';
        playerIframe.style.border = 'none';
        playerIframe.style.position = 'absolute';
        playerIframe.style.top = '0';
        playerIframe.style.left = '0';
        playerIframe.style.zIndex = '10'; // Higher z-index to ensure it's on top
        playerIframe.setAttribute('allowfullscreen', '');
        playerIframe.src = url;

        // For iframes, we can't control the player directly, so we just show a message
        if (window.modernPlayer) {
          window.modernPlayer.setTitle(`${mediaData?.displayTitle || mediaData?.title || 'External Content'} (External Player)`);
        }
    } else {
        console.error("Media.js: Could not determine playback method or player element missing.");
        alert("Cannot play this video source.");
        hidePlayer();
    }
  }

  function hidePlayer() {
    if (!playerContainer || playerContainer.classList.contains('hidden')) return;

    console.log('Media.js: Hiding player');

    if (player && (player.src || player.currentSrc)) {
        const currentSrc = player.currentSrc || player.src;
        const originalUrl = currentSrc.includes('proxy-video') ? decodeURIComponent(currentSrc.split('url=')[1]?.split('&')[0] || '') : currentSrc;

        if (originalUrl && player.currentTime > 0 && !player.ended) {
          localStorage.setItem(`videoTime-${itemId}-${originalUrl}`, player.currentTime);

          // Update progress in recently watched
          if (mediaData) {
            // Calculate progress percentage
            const progress = Math.min(100, Math.round((player.currentTime / player.duration) * 100)) || 0;

            // Get episode info if available
            let episodeInfo = null;
            const episodeItem = document.querySelector('#episodes .grid-item[style*="border: 1px solid"]');
            if (episodeItem && episodeItem.dataset.ep) {
              const seasonSelect = document.getElementById('season-select');
              const currentSeason = seasonSelect ? seasonSelect.value : mediaData.season || '1';
              episodeInfo = `S${currentSeason}:E${episodeItem.dataset.ep}`;
            }

            // Create watched item object
            const watchedItem = {
              id: itemId,
              title: mediaData.displayTitle || mediaData.title,
              thumbnail: mediaData.thumbnail,
              image: mediaData.image,
              type: itemType === 'MOVIE' ? 'movies' : itemType, // Convert MOVIE to lowercase 'movies' for consistency
              episodeInfo: episodeInfo
            };

            // For anime, add additional data needed for proper image display
            if (itemType === 'ANIME') {
              // Add Jikan data if available
              if (mediaData.jikan && mediaData.jikan.mal_id) {
                watchedItem.jikan = {
                  mal_id: mediaData.jikan.mal_id
                };
              }

              // Add TMDB data if available
              if (mediaData.tmdb && mediaData.tmdb.id) {
                watchedItem.tmdb = {
                  id: mediaData.tmdb.id
                };
              }

              // Add anime-specific properties
              watchedItem.season = mediaData.season || '1';
              watchedItem.animeLanguage = mediaData.animeLanguage || 'VOSTFR';

              // Store the banner URL if we have one
              if (banner && banner.style && banner.style.backgroundImage) {
                const bgImage = banner.style.backgroundImage;
                if (bgImage && bgImage !== 'url(/default-banner.jpg)') {
                  watchedItem.itemBannerUrl = bgImage.replace('url("', '').replace('")', '');
                }
              }
            }

            // Dispatch custom event for recently watched tracking with progress
            document.dispatchEvent(new CustomEvent('media:watched', {
              detail: {
                item: watchedItem,
                progress: progress
              }
            }));
          }
        }

        player.pause();
        player.removeAttribute('src');
        player.load();
        player.style.display = 'none';
    }

    if (hlsInstance) {
      hlsInstance.destroy();
      hlsInstance = null;
    }

    if (playerIframe) {
      playerIframe.src = 'about:blank';
      playerIframe.style.display = 'none';
    }

    playerContainer.classList.add('hidden');
    playerContainer.style.display = 'none';

    if (history.state && history.state.playerOpen) {
      history.back();
    } else {
      if (typeof ensurePlayerVisibility === 'function') {
        setTimeout(ensurePlayerVisibility, 0);
      }
    }
  }

  playerContainer.addEventListener('click', (e) => { if (e.target === playerContainer) hidePlayer(); });

  window.addEventListener('popstate', (e) => {
    const playerVisible = playerContainer && !playerContainer.classList.contains('hidden');
    if ((!e.state || !e.state.playerOpen) && playerVisible) { hidePlayer(); }
    else if (e.state && e.state.playerOpen && !playerVisible) { if (typeof ensurePlayerVisibility === 'function') { setTimeout(ensurePlayerVisibility, 0); } }
  });

  if (player) {
    player.addEventListener('pause', () => {
        if (player.currentSrc && player.currentTime > 0 && !player.ended) {
             const originalUrl = player.currentSrc.includes('proxy-video') ? decodeURIComponent(player.currentSrc.split('url=')[1]?.split('&')[0] || '') : player.currentSrc;
             if(originalUrl){ localStorage.setItem(`videoTime-${itemId}-${originalUrl}`, player.currentTime); }
        }
    });
     player.addEventListener('ended', () => {
          if (player.currentSrc) {
             const originalUrl = player.currentSrc.includes('proxy-video') ? decodeURIComponent(player.currentSrc.split('url=')[1]?.split('&')[0] || '') : player.currentSrc;
             if(originalUrl){ localStorage.removeItem(`videoTime-${itemId}-${originalUrl}`); /* console.log(`Media.js: Cleared saved time for ${originalUrl}`); */ }
          }
     });
    player.addEventListener('error', () => {
        console.error('Media.js: HTML5 Video Player Error:', player.error);
        let errorMsg = 'Error playing video.';
        if (player.error) {
          // Map error codes to user-friendly messages
          switch (player.error.code) {
            case 1: errorMsg = 'Video loading aborted.'; break;
            case 2: errorMsg = 'Network error while loading video.'; break;
            case 3: errorMsg = 'Error decoding video.'; break;
            case 4: errorMsg = 'Video format not supported.'; break;
            default: errorMsg = 'Unknown video playback error.';
          }
        }
        alert(errorMsg + ' Try another source.');
        hidePlayer();
    });
  }


  // --- Data Fetching and Rendering ---
  function groupEpisodesBySeason(episodes) {
        const seasons = {}; (episodes || []).forEach((ep) => { const season = ep.season || '1'; if (!seasons[season]) seasons[season] = []; seasons[season].push(ep); });
        for (const season in seasons) { seasons[season].sort((a, b) => String(a.episodeNumber).localeCompare(String(b.episodeNumber), undefined, { numeric: true })); } return seasons;
  }
  function renderEpisodesForSeason(episodes) {
        if (!episodes || episodes.length === 0) { return '<p>No episodes available for this season.</p>'; }
        if (itemType === 'SERIES') { const episodesByNumber = episodes.reduce((acc, ep) => { const epNum = ep.episodeNumber; if (!acc[epNum]) { acc[epNum] = { episodeNumber: epNum, languages: new Set() }; } (ep.streamingUrls || []).forEach(s => acc[epNum].languages.add(s.language?.toUpperCase() || 'UNKNOWN')); return acc; }, {}); return Object.values(episodesByNumber) .sort((a, b) => String(a.episodeNumber).localeCompare(String(b.episodeNumber), undefined, { numeric: true })) .map(epGroup => `<div class="grid-item" data-ep="${epGroup.episodeNumber}"> Episode ${epGroup.episodeNumber} <span class="extra-info">${[...epGroup.languages].sort().join('/')}</span> </div>`).join(''); }
        else { return episodes .map(ep => `<div class="grid-item" data-ep="${ep.episodeNumber}" data-lang="${ep.language || 'unknown'}"> Episode ${ep.episodeNumber} ${ep.language && ep.language !== 'unknown' ? '('+ep.language+')' : ''} </div>`).join(''); }
  }

  // Main function to load media details
  async function loadMedia() {
    document.body.style.opacity = '0.5';

    try {
      console.log(`Media.js: Preparing item query with: itemId="${itemId}", itemType="${itemType}"`);

      // Define fragments for cleaner query construction
      const streamingUrlFields = `id url provider language sourceStreamUrl lastChecked method`;
      const episodeFields = `episodeNumber season language streamingUrls { ${streamingUrlFields} }`; // Added language here
      const metadataFields = `synopsis actors year genre origin creator duration`;
      const tmdbFields = `id release_date overview genres cast { name character } crew { name job } vote_average vote_count`;
      const tmdbSeasonFields = `air_date tmdb_season_id name overview poster_path season_number vote_average episodes { air_date episode_number tmdb_episode_id name overview still_path vote_average }`;
      const jikanFields = `mal_id year synopsis genres { name } images { jpg { large_image_url image_url small_image_url } } score type episodes status season studios { name } aired { string }`;
      const jikanSeasonFields = `mal_id title season_number episodes score year synopsis aired { string }`;

      const gqlQuery = `
        query GetItem($itemId: ID!, $itemType: ItemType!) {
          item(id: $itemId, type: $itemType) {
            id
            title
            displayTitle
            thumbnail
            image
            metadata { ${metadataFields} }
            tmdb { ${tmdbFields} }

            # --- Type Specific Fields ---
            ... on Movie {
              streamingUrls { ${streamingUrlFields} }
            }
            ... on Series {
              season
              episodes { ${episodeFields} }
              tmdbSeason { ${tmdbSeasonFields} }
              tmdbSeasons { ${tmdbSeasonFields} }
            }
            ... on Anime {
              season
              animeLanguage
              streamingUrls { ${streamingUrlFields} } # For films/OVAs
              episodes { ${episodeFields} }           # For series
              tmdbSeason { ${tmdbSeasonFields} }
              tmdbSeasons { ${tmdbSeasonFields} }
              jikan { ${jikanFields} }               # <<< MOVED Jikan here
              jikanSeasons { ${jikanSeasonFields} }    # Add Jikan seasons
            }
            ... on LiveTV {
               streamingUrls { ${streamingUrlFields} }
            }
          }
        }
      `;
      const variables = { itemId, itemType };
      const data = await fetchGraphQL(gqlQuery, variables);
      mediaData = data?.item;

      if (!mediaData) {
        throw new Error('Item data not found in GraphQL response.');
      }
      console.log(`Media.js: Loaded data for ${itemType}/${itemId}:`, mediaData);

      // Debug log for Jikan seasons
      if (itemType === 'ANIME') {
        console.log('Anime data loaded:', mediaData.title);
        console.log('Jikan data:', mediaData.jikan ? 'Present' : 'Not present');
        console.log('Jikan seasons:', mediaData.jikanSeasons ? `${mediaData.jikanSeasons.length} seasons` : 'Not present');
        if (mediaData.jikanSeasons && mediaData.jikanSeasons.length > 0) {
          console.log('Jikan seasons data:', mediaData.jikanSeasons);
        }
      }

      // Update DOM Elements
      mediaTitle.textContent = mediaData.displayTitle || 'Untitled';
      document.title = `NetStream - ${mediaData.displayTitle || 'Media'}`;

      // Banner Image
      let bannerUrl;

      // For anime, try to get a high-quality banner
      if (itemType === 'ANIME' && mediaData.jikan?.mal_id) {
        try {
          let jikanBanner = null;

          // First check if we have a cached version
          const cacheKey = `jikan_anime_banner_${mediaData.jikan.mal_id}`;
          const cached = localStorage.getItem(cacheKey);

          if (cached) {
            try {
              const data = JSON.parse(cached);
              if (data.timestamp && (Date.now() - data.timestamp < 24 * 60 * 60 * 1000)) {
                console.log(`Using cached banner for anime ${mediaData.jikan.mal_id}`);
                jikanBanner = data.banner;
              }
            } catch (e) {
              console.error('Error parsing cached banner data:', e);
            }
          }

          // If no cache or expired, try to get from JikanClient or direct API
          if (!jikanBanner) {
            if (window.jikanClient) {
              // Try to get a banner from Jikan using our rate-limited client
              jikanBanner = await window.jikanClient.getAnimeBanner(mediaData.jikan.mal_id);
            } else {
              // Try direct API call with timeout
              const controller = new AbortController();
              const timeoutId = setTimeout(() => controller.abort(), 3000);

              try {
                const url = `https://api.jikan.moe/v4/anime/${mediaData.jikan.mal_id}/pictures`;
                const res = await fetch(url, { signal: controller.signal });
                clearTimeout(timeoutId);

                if (res.ok) {
                  const data = await res.json();
                  const pictures = data.data || [];
                  jikanBanner = pictures[0]?.jpg?.large_image_url || pictures[0]?.jpg?.image_url;

                  // Cache the result
                  if (jikanBanner) {
                    localStorage.setItem(cacheKey, JSON.stringify({
                      banner: jikanBanner,
                      timestamp: Date.now()
                    }));
                  }
                }
              } catch (err) {
                clearTimeout(timeoutId);
                console.warn(`Failed to fetch banner for anime ${mediaData.jikan.mal_id}:`, err.message);
              }
            }
          }

          // Use the banner if we got one
          if (jikanBanner) {
            bannerUrl = `/proxy-image?url=${encodeURIComponent(jikanBanner)}`;
          }
        } catch (err) {
          console.warn('Failed to fetch Jikan banner, falling back to other sources:', err);
        }
      }

      // If we still don't have a banner, try other sources
      if (!bannerUrl) {
        if (itemType === 'ANIME' && mediaData.image) {
          // For anime, prioritize the direct image URL
          bannerUrl = `/proxy-image?url=${encodeURIComponent(mediaData.image)}`;
        } else if (mediaData.jikan?.images?.jpg?.large_image_url) {
          bannerUrl = `/proxy-image?url=${encodeURIComponent(mediaData.jikan.images.jpg.large_image_url)}`;
        } else if (mediaData.thumbnail && !mediaData.thumbnail.includes('/default-thumbnail.jpg')) {
          if (mediaData.thumbnail.includes('image.tmdb.org')) {
            bannerUrl = mediaData.thumbnail.replace('/w154/', '/original/');
          } else {
            bannerUrl = mediaData.thumbnail;
            if (bannerUrl.startsWith('http') && !bannerUrl.startsWith(window.location.origin + '/proxy-image')) {
              bannerUrl = `/proxy-image?url=${encodeURIComponent(bannerUrl)}`;
            }
          }
        }
      }
      banner.style.backgroundImage = bannerUrl ? `url(${bannerUrl})` : 'url(/default-banner.jpg)';
      banner.style.backgroundSize = 'cover';
      banner.style.backgroundPosition = 'center center';

      // Update Metadata Table
      const releaseDateElem = document.getElementById('media-release-date');
      const genreElem = document.getElementById('media-genre');
      const actorsElem = document.getElementById('media-actors');
      const directorElem = document.getElementById('media-director');

      // TMDB elements
      const tmdbRatingElem = document.getElementById('tmdb-rating');
      const tmdbIdElem = document.getElementById('tmdb-id');
      const tmdbReleaseDateElem = document.getElementById('tmdb-release-date');
      const tmdbCastElem = document.getElementById('tmdb-cast');
      const tmdbCrewElem = document.getElementById('tmdb-crew');
      const tmdbVoteCountElem = document.getElementById('tmdb-vote-count');
      const tmdbGenresElem = document.getElementById('tmdb-genres');
      const tmdbOverviewElem = document.getElementById('tmdb-overview');
      const tmdbSection = document.querySelector('.tmdb-section');
      const tmdbExpandable = document.querySelector('.tmdb-expandable');
      const tmdbExpandToggle = document.getElementById('tmdb-expand-toggle');

      // TMDB Seasons elements
      const tmdbSeasonsSection = document.querySelector('.tmdb-seasons-section');
      const tmdbSeasonSelect = document.getElementById('tmdb-season-select');
      const tmdbSeasonAirDateElem = document.getElementById('tmdb-season-air-date');
      const tmdbSeasonNumberElem = document.getElementById('tmdb-season-number');
      const tmdbSeasonRatingElem = document.getElementById('tmdb-season-rating');
      const tmdbSeasonIdElem = document.getElementById('tmdb-season-id');
      const tmdbSeasonOverviewElem = document.getElementById('tmdb-season-overview');
      const tmdbSeasonEpisodesList = document.getElementById('tmdb-season-episodes-list');

      // Jikan elements
      const jikanRatingElem = document.getElementById('jikan-rating');
      const jikanIdElem = document.getElementById('jikan-id');
      const jikanTypeElem = document.getElementById('jikan-type');
      const jikanEpisodesElem = document.getElementById('jikan-episodes');
      const jikanStatusElem = document.getElementById('jikan-status');
      const jikanStudiosElem = document.getElementById('jikan-studios');
      const jikanSeasonElem = document.getElementById('jikan-season');
      const jikanAiredElem = document.getElementById('jikan-aired');
      const jikanGenresElem = document.getElementById('jikan-genres');
      const jikanSynopsisElem = document.getElementById('jikan-synopsis');
      const jikanImageElem = document.getElementById('jikan-image');
      const jikanSection = document.querySelector('.jikan-section');
      const jikanExpandable = document.querySelector('.jikan-expandable');
      const jikanExpandToggle = document.getElementById('jikan-expand-toggle');

      // Jikan Seasons elements
      const jikanSeasonsSection = document.querySelector('.jikan-seasons-section');
      const jikanSeasonSelect = document.getElementById('jikan-season-select');
      const jikanSeasonAirDateElem = document.getElementById('jikan-season-air-date');
      const jikanSeasonNumberElem = document.getElementById('jikan-season-number');
      const jikanSeasonRatingElem = document.getElementById('jikan-season-rating');
      const jikanSeasonIdElem = document.getElementById('jikan-season-id');
      const jikanSeasonSynopsisElem = document.getElementById('jikan-season-synopsis');
      const jikanSeasonEpisodesList = document.getElementById('jikan-season-episodes-list');

      // Basic metadata
      releaseDateElem.textContent = mediaData.metadata?.year || mediaData.tmdb?.release_date?.split('-')[0] || mediaData.jikan?.year || 'N/A';
      genreElem.textContent = mediaData.metadata?.genre || mediaData.tmdb?.genres?.join(', ') || mediaData.jikan?.genres?.map(g => g.name).join(', ') || 'N/A';
      synopsisElem.textContent = mediaData.metadata?.synopsis || mediaData.tmdb?.overview || mediaData.jikan?.synopsis || 'N/A';
      actorsElem.textContent = mediaData.metadata?.actors?.join(', ') || mediaData.tmdb?.cast?.slice(0, 5).map(c => c.name).join(', ') || 'N/A';
      directorElem.textContent = mediaData.metadata?.creator || mediaData.tmdb?.crew?.find(c => c.job === 'Director')?.name || 'N/A';

      // TMDB data
      if (mediaData.tmdb) {
        tmdbSection.style.display = 'block';

        // Rating
        if (tmdbRatingElem) {
          if (mediaData.tmdb.vote_average) {
            tmdbRatingElem.textContent = `${mediaData.tmdb.vote_average}/10`;
          } else {
            tmdbRatingElem.textContent = 'N/A';
          }
        }

        // ID
        if (tmdbIdElem) tmdbIdElem.textContent = mediaData.tmdb.id || 'N/A';

        // Release Date
        if (tmdbReleaseDateElem) tmdbReleaseDateElem.textContent = mediaData.tmdb.release_date || 'N/A';

        // Vote Count
        if (tmdbVoteCountElem) tmdbVoteCountElem.textContent = mediaData.tmdb.vote_count || 'N/A';

        // Genres
        if (tmdbGenresElem && mediaData.tmdb.genres) {
          tmdbGenresElem.textContent = Array.isArray(mediaData.tmdb.genres)
            ? mediaData.tmdb.genres.join(', ')
            : 'N/A';
        }

        // Overview
        if (tmdbOverviewElem) tmdbOverviewElem.textContent = mediaData.tmdb.overview || 'N/A';

        // Cast with characters
        if (tmdbCastElem && mediaData.tmdb.cast) {
          const castWithRoles = mediaData.tmdb.cast
            .slice(0, 5)
            .map(actor => actor.character ? `${actor.name} (${actor.character})` : actor.name)
            .join(', ');
          tmdbCastElem.textContent = castWithRoles || 'N/A';
        }

        // Crew by department
        if (tmdbCrewElem && mediaData.tmdb.crew) {
          const directors = mediaData.tmdb.crew.filter(c => c.job === 'Director').map(c => c.name).join(', ');
          const writers = mediaData.tmdb.crew.filter(c => c.job === 'Writer' || c.job === 'Screenplay').map(c => c.name).join(', ');

          let crewText = '';
          if (directors) crewText += `Directors: ${directors}`;
          if (writers) crewText += crewText ? `, Writers: ${writers}` : `Writers: ${writers}`;

          tmdbCrewElem.textContent = crewText || 'N/A';
        }

        // TMDB Seasons data (for Series and Anime)
        if ((itemType === 'SERIES' || itemType === 'ANIME') && mediaData.tmdb && mediaData.tmdb.id) {
          // Check if we have TMDB seasons data
          if (mediaData.tmdbSeasons && mediaData.tmdbSeasons.length > 0) {
            // Select the appropriate TMDB seasons section based on item type
            const currentTmdbSeasonsSection = itemType === 'ANIME'
              ? document.querySelector('.anime-tmdb-seasons-section')
              : document.querySelector('.tmdb-seasons-section:not(.anime-tmdb-seasons-section)');

            if (currentTmdbSeasonsSection) {
              console.log(`Showing TMDB seasons section for ${itemType}`);
              currentTmdbSeasonsSection.style.display = 'block';

            // Sort seasons by season number
            const sortedSeasons = [...mediaData.tmdbSeasons].sort((a, b) => a.season_number - b.season_number);

            // Populate season select dropdown
            const tmdbSeasonSelect = currentTmdbSeasonsSection.querySelector('#tmdb-season-select');
            if (tmdbSeasonSelect) {
              tmdbSeasonSelect.innerHTML = sortedSeasons.map(season =>
                `<option value="${season.season_number}">${season.name || `Season ${season.season_number}`}</option>`
              ).join('');
            }

            // Function to display selected season details from TMDB data
            const displayTmdbSeasonDetails = (seasonNumber) => {
              console.log(`Displaying TMDB season details for ${itemType}, season:`, seasonNumber);

              // Find the selected season in TMDB data
              const selectedSeason = sortedSeasons.find(s => s.season_number === parseInt(seasonNumber));

              // Get references to the elements in this specific section
              const tmdbSeasonAirDateElem = currentTmdbSeasonsSection.querySelector('#tmdb-season-air-date');
              const tmdbSeasonNumberElem = currentTmdbSeasonsSection.querySelector('#tmdb-season-number');
              const tmdbSeasonRatingElem = currentTmdbSeasonsSection.querySelector('#tmdb-season-rating');
              const tmdbSeasonIdElem = currentTmdbSeasonsSection.querySelector('#tmdb-season-id');
              const tmdbSeasonOverviewElem = currentTmdbSeasonsSection.querySelector('#tmdb-season-overview');

              if (selectedSeason) {
                console.log('Selected TMDB season:', selectedSeason);

                // Update season details with TMDB information
                if (tmdbSeasonAirDateElem) {
                  tmdbSeasonAirDateElem.parentElement.style.display = 'block';
                  tmdbSeasonAirDateElem.textContent = selectedSeason.air_date || 'N/A';
                }

                if (tmdbSeasonNumberElem) {
                  tmdbSeasonNumberElem.textContent = selectedSeason.season_number;
                }

                if (tmdbSeasonRatingElem) {
                  tmdbSeasonRatingElem.parentElement.style.display = 'block';
                  tmdbSeasonRatingElem.textContent = selectedSeason.vote_average ? `${selectedSeason.vote_average}/10` : 'N/A';
                }

                if (tmdbSeasonIdElem) {
                  tmdbSeasonIdElem.parentElement.style.display = 'block';
                  tmdbSeasonIdElem.textContent = selectedSeason.tmdb_season_id || 'N/A';
                }

                if (tmdbSeasonOverviewElem) {
                  tmdbSeasonOverviewElem.parentElement.style.display = 'block';
                  tmdbSeasonOverviewElem.textContent = selectedSeason.overview || 'No overview available';
                }

                // Populate episodes list from TMDB data if available
                const tmdbSeasonEpisodesList = currentTmdbSeasonsSection.querySelector('#tmdb-season-episodes-list');

                if (tmdbSeasonEpisodesList && selectedSeason.episodes && selectedSeason.episodes.length > 0) {
                  console.log(`Found ${selectedSeason.episodes.length} TMDB episodes for season ${seasonNumber}`);

                  // Sort episodes by episode number
                  const sortedTmdbEpisodes = [...selectedSeason.episodes].sort((a, b) => a.episode_number - b.episode_number);

                  // Create episode cards with TMDB data
                  tmdbSeasonEpisodesList.innerHTML = sortedTmdbEpisodes.map(episode => {
                    // Find matching episode in our database
                    const matchingEpisode = mediaData.episodes?.find(ep =>
                      parseInt(ep.episodeNumber) === episode.episode_number &&
                      (ep.season || '1') === String(selectedSeason.season_number)
                    );

                    const hasStreams = matchingEpisode && matchingEpisode.streamingUrls && matchingEpisode.streamingUrls.length > 0;

                    return `
                      <div class="tmdb-episode-card" data-ep="${episode.episode_number}" data-season="${selectedSeason.season_number}">
                        <h5>${episode.name || `Episode ${episode.episode_number}`}</h5>
                        <div class="tmdb-episode-meta">
                          <span>Air Date: ${episode.air_date || 'N/A'}</span>
                          <span>Rating: ${episode.vote_average ? `${episode.vote_average}/10` : 'N/A'}</span>
                        </div>
                        <div class="tmdb-episode-overview">
                          <p>${episode.overview ? episode.overview.substring(0, 100) + (episode.overview.length > 100 ? '...' : '') : 'No overview available'}</p>
                          ${hasStreams ? `<button class="play-episode-btn">Play Episode</button>` : '<span class="no-streams">No streams available</span>'}
                        </div>
                      </div>
                    `;
                  }).join('');

                  // Add click event for episode cards with play buttons
                  currentTmdbSeasonsSection.querySelectorAll('.tmdb-episode-card .play-episode-btn').forEach(button => {
                    button.addEventListener('click', () => {
                      const card = button.closest('.tmdb-episode-card');
                      const epNum = card.dataset.ep;
                      const epSeason = card.dataset.season;

                      // Find the corresponding episode in the episodes list
                      const episodeItem = document.querySelector(`#episodes .grid-item[data-ep="${epNum}"]`);
                      if (episodeItem) {
                        // Simulate a click on the episode in the main episodes list
                        episodeItem.click();

                        // Scroll to the providers section
                        document.getElementById('providers').scrollIntoView({ behavior: 'smooth' });
                      }
                    });
                  });
                } else if (tmdbSeasonEpisodesList) {
                  tmdbSeasonEpisodesList.innerHTML = '<p>No TMDB episode data available</p>';
                }
              } else {
                console.log(`No TMDB data found for season ${seasonNumber}, falling back to database episodes`);

                // If no TMDB data for this season, fall back to our database episodes
                const seasonEpisodes = mediaData.episodes?.filter(ep => (ep.season || '1') === String(seasonNumber)) || [];

                // Get references to the elements in this specific section
                const tmdbSeasonAirDateElem = currentTmdbSeasonsSection.querySelector('#tmdb-season-air-date');
                const tmdbSeasonNumberElem = currentTmdbSeasonsSection.querySelector('#tmdb-season-number');
                const tmdbSeasonRatingElem = currentTmdbSeasonsSection.querySelector('#tmdb-season-rating');
                const tmdbSeasonIdElem = currentTmdbSeasonsSection.querySelector('#tmdb-season-id');
                const tmdbSeasonOverviewElem = currentTmdbSeasonsSection.querySelector('#tmdb-season-overview');
                const tmdbSeasonEpisodesList = currentTmdbSeasonsSection.querySelector('#tmdb-season-episodes-list');

                // Reset season details to N/A
                if (tmdbSeasonAirDateElem) tmdbSeasonAirDateElem.textContent = 'N/A';
                if (tmdbSeasonNumberElem) tmdbSeasonNumberElem.textContent = seasonNumber;
                if (tmdbSeasonRatingElem) tmdbSeasonRatingElem.textContent = 'N/A';
                if (tmdbSeasonIdElem) tmdbSeasonIdElem.textContent = 'N/A';
                if (tmdbSeasonOverviewElem) tmdbSeasonOverviewElem.textContent = 'Season overview not available';

                // Populate episodes list from our database
                if (tmdbSeasonEpisodesList && seasonEpisodes.length > 0) {
                  console.log(`Found ${seasonEpisodes.length} database episodes for season ${seasonNumber}`);

                  // Sort episodes by episode number
                  const sortedEpisodes = [...seasonEpisodes].sort((a, b) => {
                    const numA = parseInt(a.episodeNumber);
                    const numB = parseInt(b.episodeNumber);
                    return isNaN(numA) || isNaN(numB) ? a.episodeNumber.localeCompare(b.episodeNumber) : numA - numB;
                  });

                  tmdbSeasonEpisodesList.innerHTML = sortedEpisodes.map(episode => `
                    <div class="tmdb-episode-card" data-ep="${episode.episodeNumber}" data-season="${episode.season || '1'}">
                      <h5>Episode ${episode.episodeNumber}</h5>
                      <div class="tmdb-episode-meta">
                        <span>Language: ${episode.language || 'unknown'}</span>
                        <span>Streams: ${episode.streamingUrls?.length || 0}</span>
                      </div>
                      <div class="tmdb-episode-overview">
                        <button class="play-episode-btn">Play Episode</button>
                      </div>
                    </div>
                  `).join('');

                  // Add click event for episode cards
                  currentTmdbSeasonsSection.querySelectorAll('.tmdb-episode-card').forEach(card => {
                    card.addEventListener('click', () => {
                      const epNum = card.dataset.ep;
                      const epSeason = card.dataset.season;

                      // Find the corresponding episode in the episodes list
                      const episodeItem = document.querySelector(`#episodes .grid-item[data-ep="${epNum}"]`);
                      if (episodeItem) {
                        // Simulate a click on the episode in the main episodes list
                        episodeItem.click();

                        // Scroll to the providers section
                        document.getElementById('providers').scrollIntoView({ behavior: 'smooth' });
                      }
                    });
                  });
                } else if (tmdbSeasonEpisodesList) {
                  tmdbSeasonEpisodesList.innerHTML = '<p>No episode data available</p>';
                }
              }
            };

            // Display the first season by default
            if (sortedSeasons.length > 0) {
              displayTmdbSeasonDetails(sortedSeasons[0].season_number);
            }

            // Add event listener for season selection
            if (tmdbSeasonSelect) {
              tmdbSeasonSelect.addEventListener('change', (e) => {
                displayTmdbSeasonDetails(parseInt(e.target.value));
              });
            }
          }
          // Fallback to using episodes data if no TMDB seasons data
          else if (mediaData.episodes && mediaData.episodes.length > 0) {
            if (currentTmdbSeasonsSection) {
              currentTmdbSeasonsSection.style.display = 'block';

              // Extract unique seasons from episodes
              const seasons = [...new Set(mediaData.episodes.map(ep => ep.season || '1'))].sort();

              // Populate season select dropdown
              const tmdbSeasonSelect = currentTmdbSeasonsSection.querySelector('#tmdb-season-select');
              if (tmdbSeasonSelect) {
                tmdbSeasonSelect.innerHTML = seasons.map(season =>
                  `<option value="${season}">Season ${season}</option>`
                ).join('');
              }

            // Function to display selected season details
            const displaySeasonDetails = (seasonNumber) => {
              console.log(`Displaying database season details for ${itemType}, season:`, seasonNumber);

              // Filter episodes for the selected season
              const seasonEpisodes = mediaData.episodes.filter(ep => (ep.season || '1') === seasonNumber);

              // Get references to the elements in this specific section
              const tmdbSeasonAirDateElem = currentTmdbSeasonsSection.querySelector('#tmdb-season-air-date');
              const tmdbSeasonNumberElem = currentTmdbSeasonsSection.querySelector('#tmdb-season-number');
              const tmdbSeasonRatingElem = currentTmdbSeasonsSection.querySelector('#tmdb-season-rating');
              const tmdbSeasonIdElem = currentTmdbSeasonsSection.querySelector('#tmdb-season-id');
              const tmdbSeasonOverviewElem = currentTmdbSeasonsSection.querySelector('#tmdb-season-overview');

              // Update season details with available information
              if (tmdbSeasonAirDateElem) tmdbSeasonAirDateElem.parentElement.style.display = 'none';
              if (tmdbSeasonNumberElem) tmdbSeasonNumberElem.textContent = seasonNumber;
              if (tmdbSeasonRatingElem) tmdbSeasonRatingElem.parentElement.style.display = 'none';
              if (tmdbSeasonIdElem) tmdbSeasonIdElem.parentElement.style.display = 'none';
              if (tmdbSeasonOverviewElem) tmdbSeasonOverviewElem.parentElement.style.display = 'none';

              // Populate episodes list
              const tmdbSeasonEpisodesList = currentTmdbSeasonsSection.querySelector('#tmdb-season-episodes-list');

              if (tmdbSeasonEpisodesList && seasonEpisodes.length > 0) {
                console.log(`Found ${seasonEpisodes.length} database episodes for season ${seasonNumber}`);

                // Sort episodes by episode number
                const sortedEpisodes = [...seasonEpisodes].sort((a, b) => {
                  const numA = parseInt(a.episodeNumber);
                  const numB = parseInt(b.episodeNumber);
                  return isNaN(numA) || isNaN(numB) ? a.episodeNumber.localeCompare(b.episodeNumber) : numA - numB;
                });

                tmdbSeasonEpisodesList.innerHTML = sortedEpisodes.map(episode => `
                  <div class="tmdb-episode-card" data-ep="${episode.episodeNumber}" data-season="${episode.season || '1'}">
                    <h5>Episode ${episode.episodeNumber}</h5>
                    <div class="tmdb-episode-meta">
                      <span>Language: ${episode.language || 'unknown'}</span>
                      <span>Streams: ${episode.streamingUrls?.length || 0}</span>
                    </div>
                    <div class="tmdb-episode-overview">
                      <button class="play-episode-btn">Play Episode</button>
                    </div>
                  </div>
                `).join('');

                // Add click event for episode cards
                currentTmdbSeasonsSection.querySelectorAll('.tmdb-episode-card').forEach(card => {
                  card.addEventListener('click', () => {
                    const epNum = card.dataset.ep;
                    const epSeason = card.dataset.season;

                    // Find the corresponding episode in the episodes list
                    const episodeItem = document.querySelector(`#episodes .grid-item[data-ep="${epNum}"]`);
                    if (episodeItem) {
                      // Simulate a click on the episode in the main episodes list
                      episodeItem.click();

                      // Scroll to the providers section
                      document.getElementById('providers').scrollIntoView({ behavior: 'smooth' });
                    }
                  });
                });
              } else if (tmdbSeasonEpisodesList) {
                tmdbSeasonEpisodesList.innerHTML = '<p>No episode data available</p>';
              }
            };

            // Display the first season by default
            if (seasons.length > 0) {
              displaySeasonDetails(seasons[0]);
            }

            // Add event listener for season selection
            if (tmdbSeasonSelect) {
              tmdbSeasonSelect.addEventListener('change', (e) => {
                displaySeasonDetails(e.target.value);
              });
            }
          } else {
            if (currentTmdbSeasonsSection) {
              currentTmdbSeasonsSection.style.display = 'none';
            }
          }
        } else {
          if (currentTmdbSeasonsSection) {
            currentTmdbSeasonsSection.style.display = 'none';
          }
        }
      } else {
        // Hide both TMDB sections
        tmdbSection.style.display = 'none';

        // Hide both TMDB seasons sections
        const seriesTmdbSeasonsSection = document.querySelector('.tmdb-seasons-section:not(.anime-tmdb-seasons-section)');
        const animeTmdbSeasonsSection = document.querySelector('.anime-tmdb-seasons-section');

        if (seriesTmdbSeasonsSection) seriesTmdbSeasonsSection.style.display = 'none';
        if (animeTmdbSeasonsSection) animeTmdbSeasonsSection.style.display = 'none';
      }

      // Set up expand/collapse functionality for TMDB
      if (tmdbExpandToggle && tmdbExpandable) {
        tmdbExpandToggle.addEventListener('click', () => {
          tmdbExpandToggle.classList.toggle('expanded');
          tmdbExpandable.classList.toggle('expanded');
        });
      }

      // Jikan data
      if (mediaData.jikan) {
        jikanSection.style.display = 'block';

        // Rating
        if (jikanRatingElem) jikanRatingElem.textContent = mediaData.jikan.score ? `${mediaData.jikan.score}/10` : 'N/A';

        // ID
        if (jikanIdElem) jikanIdElem.textContent = mediaData.jikan.mal_id || 'N/A';

        // Type
        if (jikanTypeElem) jikanTypeElem.textContent = mediaData.jikan.type || 'N/A';

        // Episodes
        if (jikanEpisodesElem) jikanEpisodesElem.textContent = mediaData.jikan.episodes || 'N/A';

        // Status
        if (jikanStatusElem) jikanStatusElem.textContent = mediaData.jikan.status || 'N/A';

        // Studios
        if (jikanStudiosElem && mediaData.jikan.studios) {
          jikanStudiosElem.textContent = mediaData.jikan.studios.map(studio => studio.name).join(', ') || 'N/A';
        }

        // Season info
        if (jikanSeasonElem) {
          const season = mediaData.jikan.season ? mediaData.jikan.season.charAt(0).toUpperCase() + mediaData.jikan.season.slice(1) : '';
          const year = mediaData.jikan.year || '';
          jikanSeasonElem.textContent = season && year ? `${season} ${year}` : (season || year || 'N/A');
        }

        // Aired
        if (jikanAiredElem && mediaData.jikan.aired) {
          jikanAiredElem.textContent = mediaData.jikan.aired.string || 'N/A';
        }

        // Genres
        if (jikanGenresElem && mediaData.jikan.genres) {
          jikanGenresElem.textContent = mediaData.jikan.genres.map(g => g.name).join(', ') || 'N/A';
        }

        // Synopsis
        if (jikanSynopsisElem) jikanSynopsisElem.textContent = mediaData.jikan.synopsis || 'N/A';

        // Image URL
        if (jikanImageElem && mediaData.jikan.images && mediaData.jikan.images.jpg) {
          jikanImageElem.textContent = mediaData.jikan.images.jpg.image_url || 'N/A';
        }

        // Jikan Seasons data (for Anime)
        if (itemType === 'ANIME' && mediaData.jikanSeasons && mediaData.jikanSeasons.length > 0) {
          console.log('Found Jikan seasons data:', mediaData.jikanSeasons);

          const jikanSeasonsSection = document.querySelector('.jikan-seasons-section');
          if (jikanSeasonsSection) {
            console.log('Showing Jikan seasons section');
            jikanSeasonsSection.style.display = 'block';

            // Sort seasons by season number
            const sortedSeasons = [...mediaData.jikanSeasons].sort((a, b) => a.season_number - b.season_number);

            // Populate season select dropdown
            const jikanSeasonSelect = document.getElementById('jikan-season-select');
            if (jikanSeasonSelect) {
              jikanSeasonSelect.innerHTML = '';

              sortedSeasons.forEach(season => {
                const option = document.createElement('option');
                option.value = season.season_number;
                option.textContent = season.title || `Season ${season.season_number}`;
                jikanSeasonSelect.appendChild(option);
              });

              // Function to display selected season details from Jikan data
              const displayJikanSeasonDetails = (seasonNumber) => {
                console.log('Displaying Jikan season details for season:', seasonNumber);

                // Find the selected season in Jikan data
                const selectedSeason = sortedSeasons.find(s => s.season_number === parseInt(seasonNumber));

                if (selectedSeason) {
                  console.log('Selected season:', selectedSeason);

                  // Update season details with Jikan information
                  const jikanSeasonNumberElem = document.getElementById('jikan-season-number');
                  const jikanSeasonAirDateElem = document.getElementById('jikan-season-air-date');
                  const jikanSeasonRatingElem = document.getElementById('jikan-season-rating');
                  const jikanSeasonIdElem = document.getElementById('jikan-season-id');
                  const jikanSeasonSynopsisElem = document.getElementById('jikan-season-synopsis');
                  const jikanSeasonEpisodesList = document.getElementById('jikan-season-episodes-list');

                  if (jikanSeasonNumberElem) jikanSeasonNumberElem.textContent = selectedSeason.season_number;

                  if (jikanSeasonAirDateElem) {
                    jikanSeasonAirDateElem.textContent = selectedSeason.aired && selectedSeason.aired.string
                      ? selectedSeason.aired.string
                      : 'N/A';
                  }

                  if (jikanSeasonRatingElem) {
                    jikanSeasonRatingElem.textContent = selectedSeason.score
                      ? `${selectedSeason.score}/10`
                      : 'N/A';
                  }

                  if (jikanSeasonIdElem) jikanSeasonIdElem.textContent = selectedSeason.mal_id || 'N/A';

                  if (jikanSeasonSynopsisElem) {
                    jikanSeasonSynopsisElem.textContent = selectedSeason.synopsis || 'No synopsis available';
                  }

                  // Populate episodes list if available
                  if (jikanSeasonEpisodesList) {
                    // Find matching episodes in our database
                    const matchingEpisodes = mediaData.episodes?.filter(ep =>
                      (ep.season || '1') === String(selectedSeason.season_number)
                    ) || [];

                    console.log('Matching episodes for season', selectedSeason.season_number, ':', matchingEpisodes.length);

                    // Create episode cards
                    jikanSeasonEpisodesList.innerHTML = '';

                    // If we have a specific episode count from Jikan
                    if (selectedSeason.episodes && selectedSeason.episodes > 0) {
                      console.log('Creating episode cards based on Jikan episode count:', selectedSeason.episodes);

                      // Create cards for each episode
                      for (let i = 1; i <= selectedSeason.episodes; i++) {
                        // Find matching episode in our database
                        const matchingEpisode = matchingEpisodes.find(ep =>
                          parseInt(ep.episodeNumber) === i
                        );

                        const hasStreams = matchingEpisode && matchingEpisode.streamingUrls && matchingEpisode.streamingUrls.length > 0;

                        const episodeCard = document.createElement('div');
                        episodeCard.className = 'jikan-episode-card';
                        episodeCard.dataset.ep = i;
                        episodeCard.dataset.season = selectedSeason.season_number;

                        episodeCard.innerHTML = `
                          <h5>Episode ${i}</h5>
                          <div class="jikan-episode-meta">
                            <span>Season: ${selectedSeason.season_number}</span>
                            <span>${selectedSeason.year || ''}</span>
                          </div>
                          <div class="jikan-episode-overview">
                            ${hasStreams
                              ? `<button class="play-episode-btn">Play Episode</button>`
                              : '<span class="no-streams">No streams available</span>'}
                          </div>
                        `;

                        jikanSeasonEpisodesList.appendChild(episodeCard);
                      }

                      // Add click event for episode cards with play buttons
                      jikanSeasonEpisodesList.querySelectorAll('.jikan-episode-card .play-episode-btn').forEach(button => {
                        button.addEventListener('click', () => {
                          const card = button.closest('.jikan-episode-card');
                          const epNum = card.dataset.ep;

                          // Find the corresponding episode in the episodes list
                          const episodeItem = document.querySelector(`#episodes .grid-item[data-ep="${epNum}"]`);
                          if (episodeItem) {
                            // Simulate a click on the episode in the main episodes list
                            episodeItem.click();

                            // Scroll to the providers section
                            document.getElementById('providers').scrollIntoView({ behavior: 'smooth' });
                          }
                        });
                      });
                    } else if (matchingEpisodes.length > 0) {
                      console.log('Creating episode cards based on matching episodes:', matchingEpisodes.length);

                      // If we don't have episode count from Jikan but have episodes in our database
                      // Sort episodes by episode number
                      const sortedEpisodes = [...matchingEpisodes].sort((a, b) => {
                        const numA = parseInt(a.episodeNumber);
                        const numB = parseInt(b.episodeNumber);
                        return isNaN(numA) || isNaN(numB) ? a.episodeNumber.localeCompare(b.episodeNumber) : numA - numB;
                      });

                      // Create cards for each episode
                      sortedEpisodes.forEach(episode => {
                        const hasStreams = episode.streamingUrls && episode.streamingUrls.length > 0;

                        const episodeCard = document.createElement('div');
                        episodeCard.className = 'jikan-episode-card';
                        episodeCard.dataset.ep = episode.episodeNumber;
                        episodeCard.dataset.season = episode.season || '1';

                        episodeCard.innerHTML = `
                          <h5>Episode ${episode.episodeNumber}</h5>
                          <div class="jikan-episode-meta">
                            <span>Season: ${episode.season || '1'}</span>
                            <span>Language: ${episode.language || 'unknown'}</span>
                          </div>
                          <div class="jikan-episode-overview">
                            ${hasStreams
                              ? `<button class="play-episode-btn">Play Episode</button>`
                              : '<span class="no-streams">No streams available</span>'}
                          </div>
                        `;

                        jikanSeasonEpisodesList.appendChild(episodeCard);
                      });

                      // Add click event for episode cards
                      jikanSeasonEpisodesList.querySelectorAll('.jikan-episode-card').forEach(card => {
                        card.addEventListener('click', () => {
                          const epNum = card.dataset.ep;

                          // Find the corresponding episode in the episodes list
                          const episodeItem = document.querySelector(`#episodes .grid-item[data-ep="${epNum}"]`);
                          if (episodeItem) {
                            // Simulate a click on the episode in the main episodes list
                            episodeItem.click();

                            // Scroll to the providers section
                            document.getElementById('providers').scrollIntoView({ behavior: 'smooth' });
                          }
                        });
                      });
                    } else {
                      console.log('No episode data available for season', selectedSeason.season_number);
                      jikanSeasonEpisodesList.innerHTML = '<p>No episode data available</p>';
                    }
                  }
                }
              };

              // Display the first season by default
              if (sortedSeasons.length > 0) {
                displayJikanSeasonDetails(sortedSeasons[0].season_number);
              }

              // Add event listener for season selection
              jikanSeasonSelect.addEventListener('change', (e) => {
                displayJikanSeasonDetails(parseInt(e.target.value));
              });
            }
          }
        } else {
          console.log('No Jikan seasons data found or not an anime');
          const jikanSeasonsSection = document.querySelector('.jikan-seasons-section');
          if (jikanSeasonsSection) {
            jikanSeasonsSection.style.display = 'none';
          }
        }
      } else {
        jikanSection.style.display = 'none';
        if (jikanSeasonsSection) jikanSeasonsSection.style.display = 'none';
      }

      // Set up expand/collapse functionality for Jikan
      if (jikanExpandToggle && jikanExpandable) {
        jikanExpandToggle.addEventListener('click', () => {
          jikanExpandToggle.classList.toggle('expanded');
          jikanExpandable.classList.toggle('expanded');
        });
      }

      try {
      // Handle Seasons and Episodes
      const episodes = mediaData.episodes || [];
      const directStreams = mediaData.streamingUrls || [];
      seasonsContainer.innerHTML = '<h3>Seasons</h3>'; seasonsContainer.style.display = 'none';
      episodesDiv.innerHTML = ''; providersDiv.innerHTML = '';

      if (itemType === 'SERIES' || (itemType === 'ANIME' && episodes.length > 0)) {
        seasonsContainer.style.display = 'block';
        const groupedEpisodes = groupEpisodesBySeason(episodes);
        const seasonKeys = Object.keys(groupedEpisodes).sort((a, b) => parseInt(a) - parseInt(b));
        if (seasonKeys.length > 0) {
            const seasonSelect = document.createElement('select'); seasonSelect.id = 'season-select';
            seasonSelect.innerHTML = seasonKeys.map(season => `<option value="${season}">Season ${season}</option>`).join('');
            seasonsContainer.appendChild(seasonSelect);
            const defaultSeason = seasonKeys[0];
            episodesDiv.innerHTML = renderEpisodesForSeason(groupedEpisodes[defaultSeason]);
            seasonSelect.addEventListener('change', (e) => {
              const selectedSeason = e.target.value; episodesDiv.innerHTML = renderEpisodesForSeason(groupedEpisodes[selectedSeason]); providersDiv.innerHTML = '';
              document.querySelectorAll('#episodes .grid-item').forEach(el => el.style.border = '1px solid #444'); });
        } else { seasonsContainer.innerHTML += '<p>No seasons found.</p>'; episodesDiv.innerHTML = '<p>No episodes available.</p>'; }
      } else if (directStreams.length > 0) {
        episodesDiv.innerHTML = (itemType === 'ANIME') ? '<p>This is a film or OVA.</p>' : '';
         providersDiv.innerHTML = directStreams.map(p => `
          <div class="grid-item" data-url="${p.url}" data-stream-id="${p.id}">
            <div class="provider-name">${p.provider || 'Unknown'} (${p.language || 'N/A'})</div>
            <div class="source-stream-url">
              <button class="source-link" data-source-url="${p.sourceStreamUrl || ''}" data-stream-id="${p.id}" data-last-fetched="${p.lastChecked ? new Date(p.lastChecked).getTime() : 0}" data-method="${p.method || ''}">
                Source: ${p.sourceStreamUrl ? 'Play' : 'Fetch'}
              </button>
            </div>
          </div> `).join('');
      } else { episodesDiv.innerHTML = '<p>No episodes or streams available.</p>'; }
    } catch (err) {
      console.error(`Media.js: Load media error: ${err.message}`);
      if (mediaTitle) mediaTitle.textContent = 'Error Loading Media';
      if (synopsisElem) synopsisElem.textContent = `Failed to load details: ${err.message}`; // Show error message
      episodesDiv.innerHTML = ''; providersDiv.innerHTML = ''; seasonsContainer.innerHTML = '';
    } finally {
       document.body.style.opacity = '1';
    }
  }

  // --- Event Listeners ---
  try {
    episodesDiv.addEventListener('click', (e) => {
      const episodeItem = e.target.closest('.grid-item[data-ep]');
      if (!episodeItem || !mediaData || (itemType !== 'SERIES' && itemType !== 'ANIME')) return;
      const epNum = episodeItem.dataset.ep;
    const epLang = episodeItem.dataset.lang;
    const seasonSelect = document.getElementById('season-select');
    const currentSeason = seasonSelect ? seasonSelect.value : mediaData.season || '1';
    console.log(`Media.js: Episode ${epNum} (Season ${currentSeason}${epLang ? ', Lang: '+epLang : ''}) clicked.`);

    // DEBUG: Log episode click details
    if (window.netStreamDebug) {
      window.netStreamDebug.log('Episode Clicked', {
        episodeNumber: epNum,
        season: currentSeason,
        language: epLang || 'default',
        timestamp: new Date().toISOString()
      });
    }

    // Filter episodes first by season AND number, then get their stream URLs
    const relevantStreamingUrls = (mediaData.episodes || [])
        .filter(ep => String(ep.episodeNumber) === String(epNum) && (ep.season || '1') === currentSeason) // Match episode & season
        .flatMap(ep => ep.streamingUrls || []) // Get all streams for matching episodes
        .filter(url => !epLang || epLang === 'unknown' || url.language === epLang); // Further filter by language if needed (for Anime)


    if (relevantStreamingUrls.length > 0) {
        providersDiv.innerHTML = relevantStreamingUrls.map(p => `
            <div class="grid-item" data-url="${p.url}" data-stream-id="${p.id}">
              <div class="provider-name">${p.provider || 'Unknown'} (${p.language || 'N/A'})</div>
              <div class="source-stream-url">
                <button class="source-link" data-source-url="${p.sourceStreamUrl || ''}" data-stream-id="${p.id}" data-last-fetched="${p.lastChecked ? new Date(p.lastChecked).getTime() : 0}" data-method="${p.method || ''}">
                  Source: ${p.sourceStreamUrl ? 'Play' : 'Fetch'}
                </button>
              </div>
            </div>`).join('');
    } else { providersDiv.innerHTML = '<p>No providers found for this specific episode/language.</p>'; }

    document.querySelectorAll('#episodes .grid-item').forEach(el => el.style.border = '1px solid #444');
    episodeItem.style.border = '1px solid #00bcd4';

    // Update the player title with the correct episode information
    if (window.modernPlayer && typeof window.modernPlayer.setTitle === 'function') {
      let title = mediaData.displayTitle || mediaData.title || 'Now Playing';

      // Format the title for series/anime
      if (itemType === 'SERIES') {
        title = `Series: ${title} - S${currentSeason}:E${epNum}`;
      } else if (itemType === 'ANIME') {
        title = `Anime: ${title} - S${currentSeason}:E${epNum}`;
      } else {
        title += ` - S${currentSeason}:E${epNum}`;
      }

      // Get the current player title before updating
      const playerTitleElement = document.getElementById('player-title');
      const currentPlayerTitle = playerTitleElement ? playerTitleElement.textContent : 'Unknown';

      // DEBUG: Log title update details
      if (window.netStreamDebug) {
        window.netStreamDebug.log('Title Update (Before)', {
          currentPlayerTitle: currentPlayerTitle,
          currentMediaTitle: window.modernPlayer.getCurrentMediaTitle ? window.modernPlayer.getCurrentMediaTitle() : 'Unknown',
          newTitle: title,
          timestamp: new Date().toISOString()
        });
      }

      window.modernPlayer.setTitle(title);
      console.log('Media.js: Updated player title on episode click:', title);

      // DEBUG: Log title update details after update
      setTimeout(() => {
        if (window.netStreamDebug) {
          window.netStreamDebug.log('Title Update (After)', {
            playerTitle: document.getElementById('player-title') ? document.getElementById('player-title').textContent : 'Unknown',
            currentMediaTitle: window.modernPlayer.getCurrentMediaTitle ? window.modernPlayer.getCurrentMediaTitle() : 'Unknown',
            timestamp: new Date().toISOString()
          });
        }
      }, 100);
    }
  });

  providersDiv.addEventListener('click', async (e) => {
    const sourceLink = e.target.closest('button.source-link');
    const providerItem = e.target.closest('.grid-item[data-stream-id]');

    if (sourceLink) {
      e.stopPropagation();
      const streamId = sourceLink.dataset.streamId;
      let sourceUrl = sourceLink.dataset.sourceUrl;
      let method = sourceLink.dataset.method;
      const lastFetched = parseInt(sourceLink.dataset.lastFetched || '0');
      const now = Date.now(); const fiveMinutes = 5 * 60 * 1000;
      // console.log('Media.js: Source link clicked', { streamId, sourceUrl, lastFetched, method });
      if (!streamId) { console.error('Media.js: Missing streamId!'); sourceLink.textContent = 'Source: Error (No ID)'; return; }

      if (!sourceUrl || now - lastFetched > fiveMinutes) {
        sourceLink.textContent = 'Source: Fetching...'; sourceLink.disabled = true;
        try {
          // console.log(`Media.js: Fetching source URL via GraphQL stream query for stream ${streamId}`);
          const gqlQuery = `query GetStreamUrl($itemId: ID!, $itemType: ItemType!, $streamId: ID!) { stream(itemId: $itemId, type: $itemType, streamId: $streamId) { sourceStreamUrl size type method } }`;
          const variables = { itemId, itemType, streamId }; // Ensure itemId and itemType are correct
          const data = await fetchGraphQL(gqlQuery, variables);
          sourceUrl = data?.stream?.sourceStreamUrl; method = data?.stream?.method;
          // console.log('Media.js: GraphQL fetch result:', data?.stream);

          if (sourceUrl) {
            sourceLink.dataset.sourceUrl = sourceUrl; sourceLink.dataset.lastFetched = Date.now(); sourceLink.dataset.method = method || ''; sourceLink.textContent = 'Source: Play';
          } else {
             sourceLink.dataset.sourceUrl = ''; sourceLink.dataset.method = ''; sourceLink.textContent = 'Source: N/A'; console.warn(`Media.js: No sourceStreamUrl returned for stream ${streamId}`);
          }
        } catch (err) { console.error(`Media.js: Failed to fetch source URL for stream ${streamId}: ${err.message}`); sourceLink.dataset.sourceUrl = ''; sourceLink.dataset.method = ''; sourceLink.textContent = 'Source: Error';
        } finally { sourceLink.disabled = false; }
      }

      if (sourceUrl) {
        // DEBUG: Log source link click
        if (window.netStreamDebug) {
          window.netStreamDebug.log('Source Link Clicked', {
            sourceUrl: sourceUrl,
            method: method,
            currentMediaTitle: window.modernPlayer.getCurrentMediaTitle ? window.modernPlayer.getCurrentMediaTitle() : 'Unknown',
            playerTitle: document.getElementById('player-title') ? document.getElementById('player-title').textContent : 'Unknown',
            timestamp: new Date().toISOString()
          });
        }

        // Update the player title before playing the item
        if (window.modernPlayer && typeof window.modernPlayer.setTitle === 'function') {
          // Find the selected episode
          const episodeItem = document.querySelector('#episodes .grid-item[style*="border: 1px solid #00bcd4"]');

          // DEBUG: Log selected episode
          if (window.netStreamDebug) {
            window.netStreamDebug.log('Selected Episode', {
              episodeElement: episodeItem ? true : false,
              episodeNumber: episodeItem ? episodeItem.dataset.ep : 'None',
              episodeStyle: episodeItem ? episodeItem.getAttribute('style') : 'None',
              allHighlightedEpisodes: document.querySelectorAll('#episodes .grid-item[style*="border: 1px solid"]').length,
              timestamp: new Date().toISOString()
            });
          }

          if (episodeItem && episodeItem.dataset.ep) {
            const epNum = episodeItem.dataset.ep;
            const seasonSelect = document.getElementById('season-select');
            const currentSeason = seasonSelect ? seasonSelect.value : mediaData.season || '1';
            let title = mediaData.displayTitle || mediaData.title || 'Now Playing';

            // Format the title for series/anime
            if (itemType === 'SERIES') {
              title = `Series: ${title} - S${currentSeason}:E${epNum}`;
            } else if (itemType === 'ANIME') {
              title = `Anime: ${title} - S${currentSeason}:E${epNum}`;
            } else {
              title += ` - S${currentSeason}:E${epNum}`;
            }

            // DEBUG: Log title before update
            if (window.netStreamDebug) {
              window.netStreamDebug.log('Source Link Title Update (Before)', {
                currentMediaTitle: window.modernPlayer.getCurrentMediaTitle ? window.modernPlayer.getCurrentMediaTitle() : 'Unknown',
                newTitle: title,
                timestamp: new Date().toISOString()
              });
            }

            window.modernPlayer.setTitle(title);
            console.log('Media.js: Updated player title before playing source:', title);

            // DEBUG: Log title after update
            if (window.netStreamDebug) {
              setTimeout(() => {
                window.netStreamDebug.log('Source Link Title Update (After)', {
                  currentMediaTitle: window.modernPlayer.getCurrentMediaTitle ? window.modernPlayer.getCurrentMediaTitle() : 'Unknown',
                  timestamp: new Date().toISOString()
                });
              }, 100);
            }
          } else {
            console.warn('Media.js: Could not find selected episode element for title update');

            // DEBUG: Log all episode elements
            if (window.netStreamDebug) {
              const allEpisodes = document.querySelectorAll('#episodes .grid-item[data-ep]');
              window.netStreamDebug.log('All Episodes', {
                count: allEpisodes.length,
                episodeNumbers: Array.from(allEpisodes).map(el => el.dataset.ep),
                timestamp: new Date().toISOString()
              });
            }
          }
        }

        playItem(sourceUrl, true, method);
      }
      else { console.log('Media.js: No valid source URL to play.'); alert("Could not retrieve source URL."); }

    } else if (providerItem) {
        const embedUrl = providerItem.dataset.url;
        if (embedUrl) {
          // Check if this is a video provider URL that should be handled as an iframe
          const isVideoProviderUrl = embedUrl && (
            embedUrl.includes('waaw1.tv') ||
            embedUrl.includes('do7go.com') ||
            embedUrl.includes('streamtape.com') ||
            embedUrl.includes('doodstream.com') ||
            embedUrl.includes('vidoza.net') ||
            embedUrl.includes('voe.sx') ||
            embedUrl.includes('upstream.to') ||
            embedUrl.includes('mixdrop.co') ||
            embedUrl.includes('vudeo.net') ||
            embedUrl.includes('tipfly.xyz') ||
            embedUrl.includes('lulu.st') ||
            embedUrl.includes('/e/') // Common pattern for embed URLs
          );

          console.log('Media.js: Provider URL check:', {
            embedUrl,
            isVideoProviderUrl
          });

          // Force iframe method for video provider URLs
          if (isVideoProviderUrl) {
            console.log('Media.js: Detected video provider URL, using iframe method:', embedUrl);
            playItem(embedUrl, false, 'iframe');
          } else {
            playItem(embedUrl, false, 'iframe');
          }
        }
        else { console.warn("Media.js: Provider item clicked, but no embed URL found."); }
    }
  });

  // --- Wishlist Button ---
  const wishlistButton = document.getElementById('media-wishlist-button');
  if (wishlistButton) {
    wishlistButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();

      if (!window.wishlistManager) {
        console.error('Wishlist manager not available');
        return;
      }

      if (!mediaData) {
        console.error('Media data not available');
        return;
      }

      // Create item object
      const item = {
        id: itemId,
        title: mediaData.displayTitle || mediaData.title,
        thumbnail: mediaData.thumbnail,
        type: itemType.toLowerCase()
      };

      // Toggle wishlist status
      window.wishlistManager.toggleWishlistItem(item);

      // Update button state
      const isInWishlist = window.wishlistManager.isInWishlist(itemId, itemType.toLowerCase());
      wishlistButton.classList.toggle('active', isInWishlist);
      wishlistButton.querySelector('i').className = isInWishlist ? 'fas fa-heart' : 'far fa-heart';
      wishlistButton.title = isInWishlist ? 'Remove from Wish List' : 'Add to Wish List';
    });
  }

  // --- Initial Load ---
  await loadMedia(); // Load the media data when the page loads

  // Update wishlist button state after loading media
  if (wishlistButton && window.wishlistManager) {
    const isInWishlist = window.wishlistManager.isInWishlist(itemId, itemType.toLowerCase());
    wishlistButton.classList.toggle('active', isInWishlist);
    wishlistButton.querySelector('i').className = isInWishlist ? 'fas fa-heart' : 'far fa-heart';
    wishlistButton.title = isInWishlist ? 'Remove from Wish List' : 'Add to Wish List';
  }

  } catch (error) {
    console.error("Media.js: Unhandled error in DOMContentLoaded event:", error);
  }
}); // End DOMContentLoaded