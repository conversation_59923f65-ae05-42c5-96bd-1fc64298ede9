/**
 * Debug helper functions for NetStream
 */

// Create a global debug object
window.netStreamDebug = {
  // Store debug values
  values: {},
  
  // Log a debug value with a label
  log: function(label, value) {
    console.log(`DEBUG [${label}]:`, value);
    this.values[label] = value;
    
    // Also log to localStorage for persistence
    try {
      const debugData = JSON.parse(localStorage.getItem('netStreamDebug') || '{}');
      debugData[label] = typeof value === 'object' ? JSON.stringify(value) : value;
      localStorage.setItem('netStreamDebug', JSON.stringify(debugData));
    } catch (e) {
      console.error('Error storing debug data:', e);
    }
    
    return value;
  },
  
  // Get all debug values
  getAll: function() {
    return this.values;
  },
  
  // Clear all debug values
  clear: function() {
    this.values = {};
    localStorage.removeItem('netStreamDebug');
    console.log('DEBUG: All debug values cleared');
  },
  
  // Show a visual debug overlay
  showOverlay: function() {
    // Remove existing overlay if any
    this.hideOverlay();
    
    // Create overlay
    const overlay = document.createElement('div');
    overlay.id = 'debug-overlay';
    overlay.style.position = 'fixed';
    overlay.style.top = '10px';
    overlay.style.right = '10px';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    overlay.style.color = '#00ff00';
    overlay.style.padding = '10px';
    overlay.style.borderRadius = '5px';
    overlay.style.zIndex = '9999';
    overlay.style.maxWidth = '400px';
    overlay.style.maxHeight = '80vh';
    overlay.style.overflow = 'auto';
    overlay.style.fontFamily = 'monospace';
    overlay.style.fontSize = '12px';
    
    // Add close button
    const closeBtn = document.createElement('button');
    closeBtn.textContent = 'Close';
    closeBtn.style.position = 'absolute';
    closeBtn.style.top = '5px';
    closeBtn.style.right = '5px';
    closeBtn.style.backgroundColor = '#ff3333';
    closeBtn.style.color = 'white';
    closeBtn.style.border = 'none';
    closeBtn.style.borderRadius = '3px';
    closeBtn.style.padding = '2px 5px';
    closeBtn.style.cursor = 'pointer';
    closeBtn.onclick = this.hideOverlay;
    
    // Add content
    const content = document.createElement('div');
    content.id = 'debug-content';
    
    // Add values
    const values = this.getAll();
    for (const [key, value] of Object.entries(values)) {
      const item = document.createElement('div');
      item.style.marginBottom = '5px';
      item.style.borderBottom = '1px solid #333';
      item.style.paddingBottom = '5px';
      
      const label = document.createElement('strong');
      label.textContent = key + ': ';
      
      const valueEl = document.createElement('span');
      valueEl.textContent = typeof value === 'object' ? JSON.stringify(value) : value;
      
      item.appendChild(label);
      item.appendChild(valueEl);
      content.appendChild(item);
    }
    
    // Add to overlay
    overlay.appendChild(closeBtn);
    overlay.appendChild(content);
    
    // Add to body
    document.body.appendChild(overlay);
  },
  
  // Hide the debug overlay
  hideOverlay: function() {
    const overlay = document.getElementById('debug-overlay');
    if (overlay) {
      overlay.remove();
    }
  },
  
  // Update the overlay with current values
  updateOverlay: function() {
    const content = document.getElementById('debug-content');
    if (!content) return;
    
    // Clear content
    content.innerHTML = '';
    
    // Add values
    const values = this.getAll();
    for (const [key, value] of Object.entries(values)) {
      const item = document.createElement('div');
      item.style.marginBottom = '5px';
      item.style.borderBottom = '1px solid #333';
      item.style.paddingBottom = '5px';
      
      const label = document.createElement('strong');
      label.textContent = key + ': ';
      
      const valueEl = document.createElement('span');
      valueEl.textContent = typeof value === 'object' ? JSON.stringify(value) : value;
      
      item.appendChild(label);
      item.appendChild(valueEl);
      content.appendChild(item);
    }
  }
};

// Add keyboard shortcut to show debug overlay (Ctrl+Shift+D)
document.addEventListener('keydown', function(e) {
  if (e.ctrlKey && e.shiftKey && e.key === 'D') {
    e.preventDefault();
    window.netStreamDebug.showOverlay();
  }
});

console.log('Debug helper loaded. Press Ctrl+Shift+D to show debug overlay.');
