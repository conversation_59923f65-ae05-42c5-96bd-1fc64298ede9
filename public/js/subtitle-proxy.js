/**
 * Subtitle Proxy Service
 * Handles downloading and processing subtitle files
 */

class SubtitleProxyService {
  constructor() {
    this.proxyUrl = '/api/subtitles/proxy'; // This would be implemented on the server
    this.subtitleCache = new Map(); // Cache for downloaded subtitles
  }

  /**
   * Download subtitle through proxy
   * @param {string} url - Subtitle download URL
   * @returns {Promise<string>} Subtitle content
   */
  async downloadSubtitle(url) {
    try {
      // Check cache first
      if (this.subtitleCache.has(url)) {
        console.log('SubtitleProxy: Using cached subtitle');
        return this.subtitleCache.get(url);
      }

      // Try to use the server proxy if available
      try {
        const proxyUrl = `/proxy-subtitle?url=${encodeURIComponent(url)}`;
        console.log('SubtitleProxy: Trying to download through server proxy:', proxyUrl);

        const proxyResponse = await fetch(proxyUrl);

        if (proxyResponse.ok) {
          const subtitleContent = await proxyResponse.text();

          // Cache the subtitle content
          this.subtitleCache.set(url, subtitleContent);

          console.log('SubtitleProxy: Successfully downloaded through proxy');
          return subtitleContent;
        } else {
          console.warn('SubtitleProxy: Proxy request failed, falling back to direct download');
        }
      } catch (proxyError) {
        console.warn('SubtitleProxy: Error using proxy, falling back to direct download:', proxyError);
      }

      // Fallback to direct download
      console.log('SubtitleProxy: Trying direct download');
      const response = await fetch(url, {
        mode: 'cors', // Try with CORS
        headers: {
          'Accept': 'text/plain, application/octet-stream'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const contentType = response.headers.get('content-type');
      let subtitleContent;

      if (contentType && contentType.includes('application/x-gzip')) {
        console.error('SubtitleProxy: Gzipped content requires server-side proxy');
        throw new Error('Gzipped subtitle files are not supported');
      } else {
        // Plain text subtitle
        subtitleContent = await response.text();
      }

      // Cache the subtitle content
      this.subtitleCache.set(url, subtitleContent);

      return subtitleContent;
    } catch (error) {
      console.error('SubtitleProxy: Error downloading subtitle', error);
      throw error; // Re-throw to allow caller to handle the error
    }
  }

  /**
   * Parse SRT subtitle content
   * @param {string} content - SRT content
   * @returns {Array} Parsed subtitles
   */
  parseSRT(content) {
    if (!content || typeof content !== 'string') {
      console.error('SubtitleProxy: Invalid content for parsing', content);
      return [];
    }

    console.log(`SubtitleProxy: Parsing SRT content, length: ${content.length}`);

    // Log a sample of the content for debugging
    console.log('SubtitleProxy: Content sample:', content.substring(0, 200).replace(/\n/g, '\\n'));

    // Check if the content is HTML instead of an SRT file
    if (content.trim().toLowerCase().startsWith('<!doctype html') ||
        content.trim().toLowerCase().startsWith('<html') ||
        (content.includes('<head>') && content.includes('<body>'))) {
      console.error('SubtitleProxy: Received HTML content instead of SRT file');
      return [];
    }

    // Try to detect and handle BOM (Byte Order Mark)
    if (content.charCodeAt(0) === 0xFEFF) {
      console.log('SubtitleProxy: Removing BOM from content');
      content = content.slice(1);
    }

    // Try multiple splitting methods to handle different SRT formats
    let blocks = [];

    // Method 1: Split by double newline (most common)
    blocks = content.trim().split(/\r?\n\r?\n/);

    // If that didn't work well, try other methods
    if (blocks.length <= 1 && content.length > 50) {
      console.log('SubtitleProxy: First splitting method failed, trying alternative methods');

      // Method 2: Split by number + newline pattern
      blocks = content.trim().split(/\r?\n(?=\d+\r?\n)/);

      // Method 3: If still not working, try a more aggressive approach
      if (blocks.length <= 1) {
        const matches = content.match(/\d+\r?\n\d{2}:\d{2}:\d{2}[,\.]\d{3} --> \d{2}:\d{2}:\d{2}[,\.]\d{3}\r?\n[\s\S]+?(?=\r?\n\d+\r?\n|$)/g);
        if (matches && matches.length > 0) {
          blocks = matches;
        }
      }
    }

    console.log(`SubtitleProxy: Found ${blocks.length} potential subtitle blocks`);

    const subtitles = [];

    // Process each subtitle block
    for (const block of blocks) {
      try {
        const lines = block.split(/\r?\n/);

        // Need at least 2 lines
        if (lines.length < 2) continue;

        // Find the timing line (it should contain ' --> ')
        let timeLineIndex = -1;
        for (let i = 0; i < Math.min(lines.length, 5); i++) {
          if (lines[i].includes(' --> ')) {
            timeLineIndex = i;
            break;
          }
        }

        if (timeLineIndex === -1) continue;

        // Parse time codes
        const timeLine = lines[timeLineIndex];
        const timeCodes = timeLine.split(' --> ');

        if (timeCodes.length !== 2) continue;

        const startTime = this.timeToSeconds(timeCodes[0]);
        const endTime = this.timeToSeconds(timeCodes[1]);

        // Get text (could be multiple lines)
        const text = lines.slice(timeLineIndex + 1).join('\n').trim();

        // Skip empty text
        if (!text) continue;

        // Try to get the index from the first line
        let index = 0;
        if (timeLineIndex > 0) {
          const possibleIndex = parseInt(lines[0].trim(), 10);
          if (!isNaN(possibleIndex)) {
            index = possibleIndex;
          }
        }

        subtitles.push({
          index,
          startTime,
          endTime,
          text
        });
      } catch (error) {
        console.error('SubtitleProxy: Error parsing subtitle block:', error);
      }
    }

    console.log(`SubtitleProxy: Successfully parsed ${subtitles.length} subtitles`);
    return subtitles;
  }

  /**
   * Convert SRT time format to seconds
   * @param {string} timeString - Time in format "00:00:00,000" or "00:00:00.000"
   * @returns {number} Time in seconds
   */
  timeToSeconds(timeString) {
    try {
      // Handle both comma and period as decimal separators
      const normalizedTime = timeString.replace(',', '.');

      // Handle different formats
      let hours = 0, minutes = 0, seconds = 0, milliseconds = 0;

      // Format: 00:00:00.000
      const timeMatch = normalizedTime.match(/(\d+):(\d+):(\d+)\.(\d+)/);

      if (timeMatch) {
        hours = parseInt(timeMatch[1], 10);
        minutes = parseInt(timeMatch[2], 10);
        seconds = parseInt(timeMatch[3], 10);
        milliseconds = parseInt(timeMatch[4], 10);
      } else {
        // Alternative format: 00:00:00
        const simpleMatch = normalizedTime.match(/(\d+):(\d+):(\d+)/);
        if (simpleMatch) {
          hours = parseInt(simpleMatch[1], 10);
          minutes = parseInt(simpleMatch[2], 10);
          seconds = parseInt(simpleMatch[3], 10);
        } else {
          console.warn('SubtitleProxy: Unrecognized time format:', timeString);
          return 0;
        }
      }

      return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000;
    } catch (error) {
      console.error('SubtitleProxy: Error converting time to seconds:', error, timeString);
      return 0;
    }
  }
}

// Create a singleton instance
const subtitleProxyService = new SubtitleProxyService();

// Export the service
window.subtitleProxyService = subtitleProxyService;
