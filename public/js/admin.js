/**
 * Admin functionality for NetStream
 * Handles admin login, authentication, and item deletion
 */

console.log('Admin.js script loaded');

class AdminManager {
  constructor() {
    console.log('AdminManager constructor called');
    this.token = localStorage.getItem('adminToken') || null;
    this.isAdmin = false;

    // Initialize admin functionality
    this.init();
  }

  /**
   * Initialize admin functionality
   */
  async init() {
    console.log('AdminManager init called');

    // Add admin login button to sidebar
    this.addAdminButton();

    // Check if we have a token and validate it
    if (this.token) {
      console.log('Found admin token, validating...');
      const isValid = await this.validateToken(this.token);
      console.log('Token validation result:', isValid);

      if (isValid) {
        console.log('Token is valid, enabling admin mode');
        this.isAdmin = true;
        this.enableAdminMode();
      } else {
        // Token is invalid, remove it
        console.log('Token is invalid, logging out');
        this.logout();
      }
    } else {
      console.log('No admin token found');
    }

    // Add a small delay and try to add admin controls again
    // This helps with pages that load content dynamically
    setTimeout(() => {
      console.log('Delayed check for admin controls');
      if (this.isAdmin) {
        this.addMediaDetailAdminControls();
      }
    }, 1000);
  }

  /**
   * Add admin login button to sidebar
   */
  addAdminButton() {
    const sidebar = document.querySelector('.sidebar ul');
    if (!sidebar) return;

    // Create admin button
    const adminButton = document.createElement('li');
    adminButton.innerHTML = `<a href="#" id="admin-login-button" aria-label="Admin"><i class="fas fa-lock"></i> Admin</a>`;

    // Add button to sidebar
    sidebar.appendChild(adminButton);

    // Add event listener
    document.getElementById('admin-login-button').addEventListener('click', (e) => {
      e.preventDefault();
      if (this.isAdmin) {
        this.showAdminPanel();
      } else {
        this.showLoginModal();
      }
    });
  }

  /**
   * Show comprehensive admin panel
   */
  showAdminPanel() {
    // Create admin panel if it doesn't exist
    if (!document.getElementById('admin-panel-modal')) {
      const modal = document.createElement('div');
      modal.id = 'admin-panel-modal';
      modal.className = 'modal admin-panel-modal';
      modal.innerHTML = this.createAdminPanelHTML();
      document.body.appendChild(modal);

      // Add event listeners
      this.setupAdminPanelEventListeners();
    }

    // Show modal and initialize
    document.getElementById('admin-panel-modal').style.display = 'block';
    this.initializeAdminPanel();
  }

  /**
   * Create admin panel HTML structure
   */
  createAdminPanelHTML() {
    return `
      <div class="modal-content admin-panel-content">
        <div class="admin-panel-header">
          <h2><i class="fas fa-cogs"></i> NetStream Admin Panel</h2>
          <div class="admin-panel-actions">
            <button id="admin-logout" class="button secondary">
              <i class="fas fa-sign-out-alt"></i> Logout
            </button>
            <span class="close admin-panel-close">&times;</span>
          </div>
        </div>

        <div class="admin-panel-body">
          <div class="admin-panel-tabs">
            <button class="tab-button active" data-tab="dashboard">
              <i class="fas fa-tachometer-alt"></i> Dashboard
            </button>
            <button class="tab-button" data-tab="content">
              <i class="fas fa-film"></i> Content Management
            </button>
            <button class="tab-button" data-tab="performance">
              <i class="fas fa-chart-line"></i> Performance
            </button>
            <button class="tab-button" data-tab="scraping">
              <i class="fas fa-spider"></i> Scraping
            </button>
            <button class="tab-button" data-tab="users">
              <i class="fas fa-users"></i> User Analytics
            </button>
            <button class="tab-button" data-tab="system">
              <i class="fas fa-server"></i> System
            </button>
            <button class="tab-button" data-tab="config">
              <i class="fas fa-cog"></i> Configuration
            </button>
            <button class="tab-button" data-tab="logs">
              <i class="fas fa-file-alt"></i> Logs
            </button>
          </div>

          <div class="tab-content-container">
            ${this.createDashboardTab()}
            ${this.createContentTab()}
            ${this.createPerformanceTab()}
            ${this.createScrapingTab()}
            ${this.createUsersTab()}
            ${this.createSystemTab()}
            ${this.createConfigTab()}
            ${this.createLogsTab()}
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Create Dashboard Tab
   */
  createDashboardTab() {
    return `
      <div class="tab-content active" id="dashboard-tab">
        <div class="dashboard-grid">
          <div class="dashboard-card">
            <div class="card-header">
              <h3><i class="fas fa-database"></i> Database Statistics</h3>
            </div>
            <div class="card-body" id="db-stats">
              <div class="loading">Loading...</div>
            </div>
          </div>

          <div class="dashboard-card">
            <div class="card-header">
              <h3><i class="fas fa-chart-bar"></i> Content Overview</h3>
            </div>
            <div class="card-body" id="content-overview">
              <div class="loading">Loading...</div>
            </div>
          </div>

          <div class="dashboard-card">
            <div class="card-header">
              <h3><i class="fas fa-tachometer-alt"></i> Performance Metrics</h3>
            </div>
            <div class="card-body" id="performance-overview">
              <div class="loading">Loading...</div>
            </div>
          </div>

          <div class="dashboard-card">
            <div class="card-header">
              <h3><i class="fas fa-spider"></i> Scraping Status</h3>
            </div>
            <div class="card-body" id="scraping-overview">
              <div class="loading">Loading...</div>
            </div>
          </div>

          <div class="dashboard-card">
            <div class="card-header">
              <h3><i class="fas fa-server"></i> System Health</h3>
            </div>
            <div class="card-body" id="system-health">
              <div class="loading">Loading...</div>
            </div>
          </div>

          <div class="dashboard-card">
            <div class="card-header">
              <h3><i class="fas fa-clock"></i> Recent Activity</h3>
            </div>
            <div class="card-body" id="recent-activity">
              <div class="loading">Loading...</div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Create Content Management Tab
   */
  createContentTab() {
    return `
      <div class="tab-content" id="content-tab">
        <div class="content-management">
          <div class="content-actions">
            <div class="action-group">
              <h3><i class="fas fa-plus"></i> Add Content</h3>
              <button class="button primary" id="add-movie-btn">
                <i class="fas fa-film"></i> Add Movie
              </button>
              <button class="button primary" id="add-series-btn">
                <i class="fas fa-tv"></i> Add Series
              </button>
              <button class="button primary" id="add-anime-btn">
                <i class="fas fa-dragon"></i> Add Anime
              </button>
              <button class="button primary" id="add-livetv-btn">
                <i class="fas fa-broadcast-tower"></i> Add Live TV
              </button>
            </div>

            <div class="action-group">
              <h3><i class="fas fa-search"></i> Content Search</h3>
              <div class="search-controls">
                <input type="text" id="content-search" placeholder="Search content...">
                <select id="content-type-filter">
                  <option value="">All Types</option>
                  <option value="movie">Movies</option>
                  <option value="series">Series</option>
                  <option value="anime">Anime</option>
                  <option value="livetv">Live TV</option>
                </select>
                <button class="button secondary" id="search-content-btn">
                  <i class="fas fa-search"></i> Search
                </button>
              </div>
            </div>

            <div class="action-group">
              <h3><i class="fas fa-tools"></i> Bulk Operations</h3>
              <button class="button warning" id="bulk-update-metadata-btn">
                <i class="fas fa-sync"></i> Update All Metadata
              </button>
              <button class="button warning" id="bulk-clean-duplicates-btn">
                <i class="fas fa-copy"></i> Remove Duplicates
              </button>
              <button class="button danger" id="bulk-delete-old-btn">
                <i class="fas fa-trash"></i> Delete Old Content
              </button>
            </div>
          </div>

          <div class="content-results" id="content-results">
            <div class="loading">Search for content to see results...</div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Create Performance Tab
   */
  createPerformanceTab() {
    return `
      <div class="tab-content" id="performance-tab">
        <div class="performance-monitoring">
          <div class="performance-controls">
            <button class="button primary" id="refresh-performance-btn">
              <i class="fas fa-sync"></i> Refresh Data
            </button>
            <button class="button secondary" id="clear-cache-btn">
              <i class="fas fa-trash"></i> Clear Cache
            </button>
            <button class="button warning" id="optimize-db-btn">
              <i class="fas fa-database"></i> Optimize Database
            </button>
          </div>

          <div class="performance-grid">
            <div class="performance-card">
              <h3><i class="fas fa-memory"></i> Memory Usage</h3>
              <div id="memory-stats" class="stats-container">
                <div class="loading">Loading...</div>
              </div>
            </div>

            <div class="performance-card">
              <h3><i class="fas fa-hdd"></i> Cache Statistics</h3>
              <div id="cache-stats" class="stats-container">
                <div class="loading">Loading...</div>
              </div>
            </div>

            <div class="performance-card">
              <h3><i class="fas fa-network-wired"></i> API Rate Limiters</h3>
              <div id="rate-limiter-stats" class="stats-container">
                <div class="loading">Loading...</div>
              </div>
            </div>

            <div class="performance-card">
              <h3><i class="fas fa-database"></i> Database Performance</h3>
              <div id="db-performance" class="stats-container">
                <div class="loading">Loading...</div>
              </div>
            </div>
          </div>

          <div class="performance-charts">
            <div class="chart-container">
              <h3>Performance Trends</h3>
              <canvas id="performance-chart" width="800" height="400"></canvas>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Create Scraping Tab
   */
  createScrapingTab() {
    return `
      <div class="tab-content" id="scraping-tab">
        <div class="scraping-management">
          <div class="scraping-controls">
            <div class="control-group">
              <h3><i class="fas fa-play"></i> Start Scraping</h3>
              <div class="scraping-options">
                <select id="scraping-mode">
                  <option value="latest">Latest Content</option>
                  <option value="full">Full Scrape</option>
                  <option value="update">Update Existing</option>
                </select>
                <select id="scraping-type">
                  <option value="all">All Types</option>
                  <option value="movies">Movies Only</option>
                  <option value="series">Series Only</option>
                  <option value="anime">Anime Only</option>
                  <option value="livetv">Live TV Only</option>
                </select>
                <button class="button primary" id="start-scraping-btn">
                  <i class="fas fa-spider"></i> Start Scraping
                </button>
              </div>
            </div>

            <div class="control-group">
              <h3><i class="fas fa-cog"></i> Scraping Configuration</h3>
              <div class="config-options">
                <label>
                  <input type="number" id="scraping-pages" value="5" min="1" max="50">
                  Pages to scrape
                </label>
                <label>
                  <input type="checkbox" id="enable-enrichment" checked>
                  Enable metadata enrichment
                </label>
                <label>
                  <input type="checkbox" id="enable-gemini" checked>
                  Use Gemini AI
                </label>
                <button class="button secondary" id="save-scraping-config-btn">
                  <i class="fas fa-save"></i> Save Config
                </button>
              </div>
            </div>
          </div>

          <div class="scraping-status">
            <div class="status-card">
              <h3><i class="fas fa-info-circle"></i> Current Status</h3>
              <div id="scraping-current-status">
                <div class="status-item">
                  <span class="status-label">Status:</span>
                  <span id="scraping-status-text" class="status-value">Idle</span>
                </div>
                <div class="status-item">
                  <span class="status-label">Progress:</span>
                  <div class="progress-bar">
                    <div id="scraping-progress" class="progress-fill" style="width: 0%"></div>
                  </div>
                </div>
              </div>
            </div>

            <div class="status-card">
              <h3><i class="fas fa-chart-bar"></i> Statistics</h3>
              <div id="scraping-stats">
                <div class="loading">Loading...</div>
              </div>
            </div>
          </div>

          <div class="scraping-logs">
            <h3><i class="fas fa-terminal"></i> Live Logs</h3>
            <div id="scraping-log-container" class="log-container">
              <div class="log-controls">
                <button class="button secondary" id="clear-logs-btn">
                  <i class="fas fa-trash"></i> Clear Logs
                </button>
                <button class="button secondary" id="auto-scroll-btn" data-enabled="true">
                  <i class="fas fa-arrow-down"></i> Auto Scroll
                </button>
              </div>
              <div id="scraping-logs" class="logs"></div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Create Users Analytics Tab
   */
  createUsersTab() {
    return `
      <div class="tab-content" id="users-tab">
        <div class="users-analytics">
          <div class="analytics-controls">
            <button class="button primary" id="refresh-analytics-btn">
              <i class="fas fa-sync"></i> Refresh Data
            </button>
            <select id="analytics-timeframe">
              <option value="24h">Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
            </select>
          </div>

          <div class="analytics-grid">
            <div class="analytics-card">
              <h3><i class="fas fa-users"></i> User Activity</h3>
              <div id="user-activity-stats" class="stats-container">
                <div class="loading">Loading...</div>
              </div>
            </div>

            <div class="analytics-card">
              <h3><i class="fas fa-play"></i> Content Views</h3>
              <div id="content-views-stats" class="stats-container">
                <div class="loading">Loading...</div>
              </div>
            </div>

            <div class="analytics-card">
              <h3><i class="fas fa-heart"></i> Popular Content</h3>
              <div id="popular-content" class="stats-container">
                <div class="loading">Loading...</div>
              </div>
            </div>

            <div class="analytics-card">
              <h3><i class="fas fa-search"></i> Search Analytics</h3>
              <div id="search-analytics" class="stats-container">
                <div class="loading">Loading...</div>
              </div>
            </div>
          </div>

          <div class="analytics-charts">
            <div class="chart-container">
              <h3>User Activity Trends</h3>
              <canvas id="user-activity-chart" width="800" height="400"></canvas>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Create System Tab
   */
  createSystemTab() {
    return `
      <div class="tab-content" id="system-tab">
        <div class="system-management">
          <div class="system-controls">
            <button class="button primary" id="restart-server-btn">
              <i class="fas fa-redo"></i> Restart Server
            </button>
            <button class="button warning" id="backup-database-btn">
              <i class="fas fa-download"></i> Backup Database
            </button>
            <button class="button danger" id="maintenance-mode-btn">
              <i class="fas fa-tools"></i> Maintenance Mode
            </button>
          </div>

          <div class="system-grid">
            <div class="system-card">
              <div class="system-card-header">
                <h3><i class="fas fa-microchip"></i> Server Resources</h3>
              </div>
              <div class="system-card-body">
                <div id="server-resources" class="compact-stats-grid">
                  <div class="loading">Loading...</div>
                </div>
              </div>
            </div>

            <div class="system-card">
              <div class="system-card-header">
                <h3><i class="fas fa-hdd"></i> Storage Usage</h3>
              </div>
              <div class="system-card-body">
                <div id="storage-usage" class="compact-stats-grid">
                  <div class="loading">Loading...</div>
                </div>
              </div>
            </div>

            <div class="system-card">
              <div class="system-card-header">
                <h3><i class="fas fa-network-wired"></i> Network Status</h3>
              </div>
              <div class="system-card-body">
                <div id="network-status" class="compact-stats-grid">
                  <div class="loading">Loading...</div>
                </div>
              </div>
            </div>

            <div class="system-card">
              <div class="system-card-header">
                <h3><i class="fas fa-shield-alt"></i> Security Status</h3>
              </div>
              <div class="system-card-body">
                <div id="security-status" class="compact-stats-grid">
                  <div class="loading">Loading...</div>
                </div>
              </div>
            </div>

            <div class="system-card">
              <div class="system-card-header">
                <h3><i class="fas fa-tasks"></i> Running Processes</h3>
              </div>
              <div class="system-card-body">
                <div id="running-processes" class="processes-container">
                  <div class="loading">Loading...</div>
                </div>
              </div>
            </div>

            <div class="system-card">
              <div class="system-card-header">
                <h3><i class="fas fa-exclamation-triangle"></i> System Errors</h3>
              </div>
              <div class="system-card-body">
                <div id="system-errors" class="error-list">
                  <div class="loading">Loading...</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Create Configuration Tab
   */
  createConfigTab() {
    return `
      <div class="tab-content" id="config-tab">
        <div class="configuration-management">
          <div class="config-grid">
            <div class="config-card">
              <div class="config-card-header">
                <h3><i class="fas fa-globe"></i> Base URLs</h3>
              </div>
              <div class="config-card-body">
                <div class="config-item">
                  <label for="wiflix-base">Wiflix Base URL</label>
                  <div class="input-group">
                    <input type="text" id="wiflix-base" placeholder="e.g., wiflix-max.cam">
                    <button class="button primary compact" id="update-wiflix">Update</button>
                  </div>
                </div>
                <div class="config-item">
                  <label for="french-anime-base">French Anime Base URL</label>
                  <div class="input-group">
                    <input type="text" id="french-anime-base" placeholder="e.g., french-anime.com">
                    <button class="button primary compact" id="update-french-anime">Update</button>
                  </div>
                </div>
                <div class="config-item">
                  <label for="witv-base">WiTV Base URL</label>
                  <div class="input-group">
                    <input type="text" id="witv-base" placeholder="e.g., witv.skin">
                    <button class="button primary compact" id="update-witv">Update</button>
                  </div>
                </div>
              </div>
            </div>

            <div class="config-card">
              <div class="config-card-header">
                <h3><i class="fas fa-key"></i> API Keys</h3>
              </div>
              <div class="config-card-body">
                <div class="config-item">
                  <label for="tmdb-api-key">TMDB API Key</label>
                  <div class="input-group">
                    <input type="password" id="tmdb-api-key" placeholder="Enter TMDB API key">
                    <button class="button primary compact" id="update-tmdb-key">Update</button>
                  </div>
                </div>
                <div class="config-item">
                  <label for="gemini-api-key">Gemini API Key</label>
                  <div class="input-group">
                    <input type="password" id="gemini-api-key" placeholder="Enter Gemini API key">
                    <button class="button primary compact" id="update-gemini-key">Update</button>
                  </div>
                </div>
              </div>
            </div>

            <div class="config-card">
              <div class="config-card-header">
                <h3><i class="fas fa-cogs"></i> System Settings</h3>
              </div>
              <div class="config-card-body">
                <div class="config-item">
                  <label>
                    <input type="checkbox" id="enable-caching"> Enable Caching
                  </label>
                </div>
                <div class="config-item">
                  <label>
                    <input type="checkbox" id="enable-enrichment"> Enable Metadata Enrichment
                  </label>
                </div>
                <div class="config-item">
                  <label>
                    <input type="checkbox" id="enable-gemini-ai"> Enable Gemini AI
                  </label>
                </div>
                <div class="config-item">
                  <label for="max-concurrent-pages">Max Concurrent Pages</label>
                  <div class="input-group">
                    <input type="number" id="max-concurrent-pages" min="0" max="10" value="1">
                    <button class="button primary compact" id="save-system-settings">Save</button>
                  </div>
                </div>
              </div>
            </div>

            <div class="config-card">
              <div class="config-card-header">
                <h3><i class="fas fa-link"></i> Manual Scraping</h3>
              </div>
              <div class="config-card-body">
                <div class="config-item">
                  <label for="manual-scrape-url">URL to Scrape</label>
                  <input type="text" id="manual-scrape-url" placeholder="Enter URL to scrape manually">
                </div>
                <div class="config-item">
                  <label for="manual-scrape-type">Content Type</label>
                  <select id="manual-scrape-type">
                    <option value="MOVIE">Movie</option>
                    <option value="SERIES">Series</option>
                    <option value="ANIME">Anime</option>
                    <option value="LIVETV">Live TV</option>
                  </select>
                </div>
                <div class="config-item">
                  <button class="button primary full-width" id="start-manual-scrape">Start Manual Scrape</button>
                  <div id="manual-scrape-message"></div>
                </div>
              </div>
            </div>

            <div class="config-card">
              <div class="config-card-header">
                <h3><i class="fas fa-eye"></i> Display Settings</h3>
              </div>
              <div class="config-card-body">
                <div class="config-item">
                  <label>
                    <input type="checkbox" id="grid-items-toggle"> Enable Grid Items Display
                  </label>
                  <div id="grid-status-indicator" class="status-indicator"></div>
                </div>
                <div id="display-settings-message"></div>
              </div>
            </div>

            <div class="config-card">
              <div class="config-card-header">
                <h3><i class="fas fa-tachometer-alt"></i> Performance</h3>
              </div>
              <div class="config-card-body">
                <div class="config-item">
                  <label for="cache-ttl">Cache TTL (minutes)</label>
                  <div class="input-group">
                    <input type="number" id="cache-ttl" placeholder="5-60" min="5" max="60" value="30">
                    <button class="button primary compact" id="update-cache-ttl">Update</button>
                  </div>
                </div>
                <div class="config-item">
                  <label for="request-timeout">Request Timeout (ms)</label>
                  <div class="input-group">
                    <input type="number" id="request-timeout" placeholder="5000-30000" min="5000" max="30000" value="10000">
                    <button class="button primary compact" id="update-request-timeout">Update</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div id="config-message"></div>
        </div>
      </div>
    `;
  }

  /**
   * Create Logs Tab
   */
  createLogsTab() {
    return `
      <div class="tab-content" id="logs-tab">
        <div class="logs-management">
          <div class="logs-controls">
            <select id="log-level-filter">
              <option value="">All Levels</option>
              <option value="error">Error</option>
              <option value="warn">Warning</option>
              <option value="info">Info</option>
              <option value="debug">Debug</option>
            </select>
            <select id="log-source-filter">
              <option value="">All Sources</option>
              <option value="scraping">Scraping</option>
              <option value="enrichment">Enrichment</option>
              <option value="api">API</option>
              <option value="database">Database</option>
              <option value="system">System</option>
            </select>
            <button class="button primary" id="refresh-logs-btn">
              <i class="fas fa-sync"></i> Refresh
            </button>
            <button class="button secondary" id="download-logs-btn">
              <i class="fas fa-download"></i> Download
            </button>
            <button class="button danger" id="clear-logs-btn">
              <i class="fas fa-trash"></i> Clear Logs
            </button>
          </div>

          <div class="logs-container">
            <div id="logs-display" class="logs-display">
              <div class="loading">Loading logs...</div>
            </div>
          </div>

          <div class="logs-pagination">
            <button class="button secondary" id="logs-prev-btn" disabled>
              <i class="fas fa-chevron-left"></i> Previous
            </button>
            <span id="logs-page-info">Page 1 of 1</span>
            <button class="button secondary" id="logs-next-btn" disabled>
              Next <i class="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Setup admin panel event listeners
   */
  setupAdminPanelEventListeners() {
    // Tab switching
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('tab-button')) {
        this.switchTab(e.target.dataset.tab);
      }
    });

    // Close panel
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('admin-panel-close')) {
        document.getElementById('admin-panel-modal').style.display = 'none';
      }
    });

    // Logout
    document.addEventListener('click', (e) => {
      if (e.target.id === 'admin-logout') {
        this.logout();
        document.getElementById('admin-panel-modal').style.display = 'none';
      }
    });
  }

  /**
   * Switch admin panel tab
   */
  switchTab(tabName) {
    // Remove active class from all tabs and content
    document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

    // Add active class to selected tab and content
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    document.getElementById(`${tabName}-tab`).classList.add('active');

    // Load tab-specific data
    this.loadTabData(tabName);
  }

  /**
   * Initialize admin panel
   */
  async initializeAdminPanel() {
    // Load dashboard data by default
    this.loadTabData('dashboard');

    // Setup real-time updates
    this.setupRealTimeUpdates();
  }

  /**
   * Load tab-specific data
   */
  async loadTabData(tabName) {
    switch (tabName) {
      case 'dashboard':
        await this.loadDashboardData();
        break;
      case 'content':
        await this.loadContentData();
        break;
      case 'performance':
        await this.loadPerformanceData();
        break;
      case 'scraping':
        await this.loadScrapingData();
        break;
      case 'users':
        await this.loadUsersData();
        break;
      case 'system':
        await this.loadSystemData();
        break;
      case 'config':
        await this.loadConfigData();
        break;
      case 'logs':
        await this.loadLogsData();
        break;
    }
  }

  /**
   * Load dashboard data
   */
  async loadDashboardData() {
    try {
      console.log('Loading dashboard data...');

      // Load database statistics
      console.log('Fetching database stats...');
      try {
        const dbStats = await this.fetchDatabaseStats();
        console.log('Database stats received:', dbStats);
        this.updateDashboardCard('db-stats', this.formatDatabaseStats(dbStats));
      } catch (error) {
        console.error('Error loading database stats:', error);
        this.updateDashboardCard('db-stats', '<div class="stat-item"><span class="stat-label error">Error loading database stats</span></div>');
      }

      // Load content overview
      console.log('Fetching content overview...');
      try {
        const contentOverview = await this.fetchContentOverview();
        console.log('Content overview received:', contentOverview);
        this.updateDashboardCard('content-overview', this.formatContentOverview(contentOverview));
      } catch (error) {
        console.error('Error loading content overview:', error);
        this.updateDashboardCard('content-overview', '<div class="stat-item"><span class="stat-label error">Error loading content overview</span></div>');
      }

      // Load performance metrics
      console.log('Fetching performance data...');
      try {
        const response = await fetch('/api/performance');
        if (!response.ok) {
          throw new Error(`Performance API returned ${response.status}`);
        }
        const performanceData = await response.json();
        console.log('Performance data received:', performanceData);
        this.updateDashboardCard('performance-overview', this.formatPerformanceOverview(performanceData));
      } catch (error) {
        console.error('Error loading performance data:', error);
        this.updateDashboardCard('performance-overview', '<div class="stat-item"><span class="stat-label error">Performance API unavailable</span></div>');
      }

      // Load scraping status
      console.log('Fetching scraping status...');
      try {
        const scrapingStatus = await this.fetchScrapingStatus();
        console.log('Scraping status received:', scrapingStatus);
        this.updateDashboardCard('scraping-overview', this.formatScrapingStatus(scrapingStatus));
      } catch (error) {
        console.error('Error loading scraping status:', error);
        this.updateDashboardCard('scraping-overview', '<div class="stat-item"><span class="stat-label error">Error loading scraping status</span></div>');
      }

      // Load system health
      console.log('Fetching system health...');
      try {
        const systemHealth = await this.fetchSystemHealth();
        console.log('System health received:', systemHealth);
        this.updateDashboardCard('system-health', this.formatSystemHealth(systemHealth));
      } catch (error) {
        console.error('Error loading system health:', error);
        this.updateDashboardCard('system-health', '<div class="stat-item"><span class="stat-label error">Error loading system health</span></div>');
      }

      // Load recent activity
      console.log('Fetching recent activity...');
      try {
        const recentActivity = await this.fetchRecentActivity();
        console.log('Recent activity received:', recentActivity);
        this.updateDashboardCard('recent-activity', this.formatRecentActivity(recentActivity));
      } catch (error) {
        console.error('Error loading recent activity:', error);
        this.updateDashboardCard('recent-activity', '<div class="stat-item"><span class="stat-label error">Error loading recent activity</span></div>');
      }

      console.log('Dashboard data loading complete');
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  }

  /**
   * Update dashboard card
   */
  updateDashboardCard(cardId, content) {
    const card = document.getElementById(cardId);
    if (card) {
      card.innerHTML = content;
    }
  }

  /**
   * Fetch database statistics
   */
  async fetchDatabaseStats() {
    try {
      const response = await fetch('/graphql', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: `
            query GetDatabaseStats {
              databaseStats {
                movies
                series
                anime
                livetv
                totalItems
              }
            }
          `
        })
      });

      const result = await response.json();
      return result.data?.databaseStats || {
        movies: 0, series: 0, anime: 0, livetv: 0, totalItems: 0
      };
    } catch (error) {
      console.error('Error fetching database stats:', error);
      return { movies: 0, series: 0, anime: 0, livetv: 0, totalItems: 0 };
    }
  }

  /**
   * Fetch content overview
   */
  async fetchContentOverview() {
    try {
      const response = await fetch('/graphql', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: `
            query GetContentOverview {
              contentOverview {
                recentlyAdded
                trending
                mostWatched
                totalViews
              }
            }
          `
        })
      });

      const result = await response.json();
      return result.data?.contentOverview || {
        recentlyAdded: 0, trending: 0, mostWatched: 0, totalViews: 0
      };
    } catch (error) {
      console.error('Error fetching content overview:', error);
      return { recentlyAdded: 0, trending: 0, mostWatched: 0, totalViews: 0 };
    }
  }

  /**
   * Fetch scraping status
   */
  async fetchScrapingStatus() {
    try {
      // For now, return mock data since scraping status endpoint doesn't exist
      return {
        status: 'idle',
        lastRun: new Date().toISOString(),
        itemsScraped: 0,
        errors: 0,
        successRate: 100
      };
    } catch (error) {
      console.error('Error fetching scraping status:', error);
      return { status: 'error', lastRun: null, itemsScraped: 0, errors: 0, successRate: 0 };
    }
  }

  /**
   * Fetch system health
   */
  async fetchSystemHealth() {
    try {
      const response = await fetch('/api/performance');
      const data = await response.json();

      return {
        uptime: data.uptime || 0,
        memory: data.memory || {},
        status: 'healthy'
      };
    } catch (error) {
      console.error('Error fetching system health:', error);
      return { uptime: 0, memory: {}, status: 'error' };
    }
  }

  /**
   * Fetch recent activity
   */
  async fetchRecentActivity() {
    try {
      // For now, return mock data since activity endpoint doesn't exist
      return [
        { type: 'content_added', message: 'New movie added', timestamp: new Date().toISOString() },
        { type: 'scraping', message: 'Scraping completed', timestamp: new Date().toISOString() },
        { type: 'user_action', message: 'Admin login', timestamp: new Date().toISOString() }
      ];
    } catch (error) {
      console.error('Error fetching recent activity:', error);
      return [];
    }
  }

  /**
   * Format database stats for display
   */
  formatDatabaseStats(stats) {
    return `
      <div class="stat-item">
        <span class="stat-label">Movies</span>
        <span class="stat-value">${stats.movies.toLocaleString()}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Series</span>
        <span class="stat-value">${stats.series.toLocaleString()}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Anime</span>
        <span class="stat-value">${stats.anime.toLocaleString()}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Live TV</span>
        <span class="stat-value">${stats.livetv.toLocaleString()}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Total Items</span>
        <span class="stat-value success">${stats.totalItems.toLocaleString()}</span>
      </div>
    `;
  }

  /**
   * Format content overview for display
   */
  formatContentOverview(overview) {
    return `
      <div class="stat-item">
        <span class="stat-label">Recently Added</span>
        <span class="stat-value">${overview.recentlyAdded}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Trending</span>
        <span class="stat-value">${overview.trending}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Most Watched</span>
        <span class="stat-value">${overview.mostWatched}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Total Views</span>
        <span class="stat-value success">${overview.totalViews.toLocaleString()}</span>
      </div>
    `;
  }

  /**
   * Format performance overview for display
   */
  formatPerformanceOverview(data) {
    const memoryUsage = data.memory ? Math.round((data.memory.heapUsed / data.memory.heapTotal) * 100) : 0;
    const uptime = data.uptime ? Math.round(data.uptime / 3600) : 0;

    return `
      <div class="stat-item">
        <span class="stat-label">Memory Usage</span>
        <span class="stat-value ${memoryUsage > 80 ? 'warning' : 'success'}">${memoryUsage}%</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Uptime</span>
        <span class="stat-value">${uptime}h</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Cache Hit Rate</span>
        <span class="stat-value success">${data.cache?.hitRate || 0}%</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Status</span>
        <span class="stat-value success">Healthy</span>
      </div>
    `;
  }

  /**
   * Format scraping status for display
   */
  formatScrapingStatus(status) {
    return `
      <div class="stat-item">
        <span class="stat-label">Status</span>
        <span class="stat-value ${status.status === 'idle' ? 'success' : 'warning'}">${status.status}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Last Run</span>
        <span class="stat-value">${status.lastRun ? new Date(status.lastRun).toLocaleString() : 'Never'}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Items Scraped</span>
        <span class="stat-value">${status.itemsScraped}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Success Rate</span>
        <span class="stat-value success">${status.successRate}%</span>
      </div>
    `;
  }

  /**
   * Format system health for display
   */
  formatSystemHealth(health) {
    const uptime = Math.round(health.uptime / 3600);
    const memoryMB = health.memory.heapUsed ? Math.round(health.memory.heapUsed / 1024 / 1024) : 0;

    return `
      <div class="stat-item">
        <span class="stat-label">Status</span>
        <span class="stat-value ${health.status === 'healthy' ? 'success' : 'error'}">${health.status}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Uptime</span>
        <span class="stat-value">${uptime}h</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Memory</span>
        <span class="stat-value">${memoryMB}MB</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Load</span>
        <span class="stat-value success">Normal</span>
      </div>
    `;
  }

  /**
   * Format recent activity for display
   */
  formatRecentActivity(activities) {
    if (!activities.length) {
      return '<div class="stat-item"><span class="stat-label">No recent activity</span></div>';
    }

    return activities.slice(0, 5).map(activity => `
      <div class="stat-item">
        <span class="stat-label">${activity.message}</span>
        <span class="stat-value">${new Date(activity.timestamp).toLocaleTimeString()}</span>
      </div>
    `).join('');
  }

  /**
   * Load content data
   */
  async loadContentData() {
    try {
      console.log('Loading content management data...');

      // Setup content management event listeners
      this.setupContentManagementListeners();

      // Show initial message
      const contentResults = document.getElementById('content-results');
      if (contentResults) {
        contentResults.innerHTML = `
          <div class="content-message">
            <p>Use the search above to find content, or use the buttons to add new content.</p>
            <p>Search functionality and content management tools are now active.</p>
          </div>
        `;
      }
    } catch (error) {
      console.error('Error loading content data:', error);
    }
  }

  /**
   * Setup content management event listeners
   */
  setupContentManagementListeners() {
    // Search content button
    const searchBtn = document.getElementById('search-content-btn');
    if (searchBtn) {
      searchBtn.addEventListener('click', () => this.searchContent());
    }

    // Add content buttons
    const addMovieBtn = document.getElementById('add-movie-btn');
    if (addMovieBtn) {
      addMovieBtn.addEventListener('click', () => this.showAddContentModal('movie'));
    }

    const addSeriesBtn = document.getElementById('add-series-btn');
    if (addSeriesBtn) {
      addSeriesBtn.addEventListener('click', () => this.showAddContentModal('series'));
    }

    const addAnimeBtn = document.getElementById('add-anime-btn');
    if (addAnimeBtn) {
      addAnimeBtn.addEventListener('click', () => this.showAddContentModal('anime'));
    }

    const addLiveTvBtn = document.getElementById('add-livetv-btn');
    if (addLiveTvBtn) {
      addLiveTvBtn.addEventListener('click', () => this.showAddContentModal('livetv'));
    }

    // Bulk operation buttons
    const bulkUpdateBtn = document.getElementById('bulk-update-metadata-btn');
    if (bulkUpdateBtn) {
      bulkUpdateBtn.addEventListener('click', () => this.bulkUpdateMetadata());
    }

    const bulkCleanBtn = document.getElementById('bulk-clean-duplicates-btn');
    if (bulkCleanBtn) {
      bulkCleanBtn.addEventListener('click', () => this.bulkCleanDuplicates());
    }

    const bulkDeleteBtn = document.getElementById('bulk-delete-old-btn');
    if (bulkDeleteBtn) {
      bulkDeleteBtn.addEventListener('click', () => this.bulkDeleteOld());
    }
  }

  /**
   * Search content
   */
  async searchContent() {
    const searchInput = document.getElementById('content-search');
    const typeFilter = document.getElementById('content-type-filter');
    const resultsDiv = document.getElementById('content-results');

    if (!searchInput || !typeFilter || !resultsDiv) return;

    const query = searchInput.value.trim();
    const type = typeFilter.value;

    if (!query) {
      resultsDiv.innerHTML = '<div class="content-message">Please enter a search term.</div>';
      return;
    }

    resultsDiv.innerHTML = '<div class="loading">Searching...</div>';

    try {
      // Use the correct search query structure for admin panel
      const searchQuery = `
        query SearchContent($query: String!) {
          search(query: $query, limit: 20) {
            items {
              ... on Movie {
                id
                title
                thumbnail
              }
              ... on Series {
                id
                title
                thumbnail
              }
              ... on Anime {
                id
                title
                thumbnail
              }
              ... on LiveTV {
                id
                title
                thumbnail
              }
            }
          }
        }
      `;

      const response = await fetch('/graphql', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query: searchQuery, variables: { query } })
      });

      const result = await response.json();

      if (result.errors) {
        throw new Error(result.errors[0].message);
      }

      const items = result.data.search?.items || [];

      if (items.length === 0) {
        resultsDiv.innerHTML = '<div class="content-message">No content found matching your search.</div>';
        return;
      }

      // Items already have __typename from GraphQL fragments, filter if specified
      console.log('Search items received:', items);
      console.log('Filter type:', type);

      const filteredItems = type && type !== 'all' ?
        items.filter(item => {
          const itemType = item.__typename?.toLowerCase();
          console.log(`Item type: ${itemType}, Filter: ${type}, Match: ${itemType === type}`);
          return itemType === type;
        }) : items;

      console.log('Filtered items:', filteredItems);

      // Debug each item before rendering
      filteredItems.forEach((item, index) => {
        console.log(`Item ${index} FULL OBJECT:`, item);
        console.log(`Item ${index} details:`, {
          id: item.id,
          __typename: item.__typename,
          title: item.title,
          thumbnail: item.thumbnail,
          allKeys: Object.keys(item)
        });
      });

      resultsDiv.innerHTML = `
        <div class="search-results">
          <div class="search-results-header">
            <h3><i class="fas fa-search"></i> Search Results</h3>
            <span class="results-count">${filteredItems.length} items found</span>
          </div>
          <div class="content-grid-container">
            ${filteredItems.map((item, index) => {
              const itemId = item.id || 'unknown';
              // Try to determine type from the item structure or use __typename
              let itemType = item.__typename;

              // If __typename is missing, try to infer from other properties
              if (!itemType || itemType === 'unknown') {
                if (item.season !== undefined || item.episodes !== undefined) {
                  if (item.animeLanguage !== undefined || item.jikan !== undefined) {
                    itemType = 'Anime';
                  } else {
                    itemType = 'Series';
                  }
                } else if (item.metadata?.duration || item.tmdb?.runtime) {
                  itemType = 'Movie';
                } else if (item.streamUrl || item.category) {
                  itemType = 'LiveTV';
                } else {
                  itemType = 'Movie'; // Default fallback
                }
              }

              const itemTitle = item.title || 'Unknown Title';
              const itemThumbnail = item.thumbnail || '/default-thumbnail.jpg';

              console.log(`Rendering item ${index}:`, { itemId, itemType, itemTitle, originalTypename: item.__typename });

              return `
                <div class="grid-item" data-id="${itemId}" data-type="${itemType}">
                  <div class="grid-item-image">
                    <img src="${itemThumbnail}" alt="${itemTitle}" loading="lazy">
                    <div class="grid-item-overlay">
                      <div class="grid-item-actions">
                        <button class="action-btn edit-btn" data-id="${itemId}" data-type="${itemType}" title="Edit ${itemType}">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete-btn" data-id="${itemId}" data-type="${itemType}" title="Delete ${itemType}">
                          <i class="fas fa-trash"></i>
                        </button>
                        <button class="action-btn view-btn" data-id="${itemId}" data-type="${itemType}" title="View Details">
                          <i class="fas fa-eye"></i>
                        </button>
                      </div>
                    </div>
                    <div class="grid-item-type">
                      <span class="type-badge ${itemType?.toLowerCase()}">${itemType}</span>
                    </div>
                  </div>
                  <div class="grid-item-info">
                    <h4 class="grid-item-title" title="${itemTitle}">${itemTitle}</h4>
                    <div class="grid-item-meta">
                      <span class="item-id">ID: ${itemId.slice(-8)}</span>
                    </div>
                  </div>
                </div>
              `;
            }).join('')}
          </div>
        </div>
      `;

      // Add event listeners to action buttons
      this.setupContentActionListeners();

    } catch (error) {
      console.error('Error searching content:', error);
      resultsDiv.innerHTML = `<div class="content-message error">Error searching content: ${error.message}</div>`;
    }
  }

  /**
   * Setup content action listeners
   */
  setupContentActionListeners() {
    console.log('Setting up content action listeners...');

    // Use event delegation instead of direct event listeners
    const contentContainer = document.getElementById('content-results');
    if (!contentContainer) {
      console.error('Content results container not found');
      return;
    }

    // Remove any existing listeners
    contentContainer.removeEventListener('click', this.handleContentAction);

    // Add single delegated event listener
    contentContainer.addEventListener('click', (e) => this.handleContentAction(e));

    console.log('Content action listeners set up successfully');
  }

  /**
   * Handle content action clicks
   */
  handleContentAction(e) {
    const target = e.target;
    const button = target.closest('.action-btn');

    if (!button) return;

    e.stopPropagation();
    e.preventDefault();

    const id = button.getAttribute('data-id');
    const type = button.getAttribute('data-type');

    console.log('Action button clicked:', {
      target: target,
      button: button,
      id: id,
      type: type,
      classList: button.classList.toString(),
      allAttributes: Array.from(button.attributes).map(attr => `${attr.name}="${attr.value}"`).join(', ')
    });

    if (!id || !type || type === 'undefined' || id === 'undefined') {
      console.error('Invalid action data:', { id, type, button });
      alert('Error: Invalid content data. Please refresh and try again.');
      return;
    }

    // Determine action type
    if (button.classList.contains('edit-btn')) {
      console.log('Executing edit action for:', { id, type });
      this.editContent(id, type);
    } else if (button.classList.contains('delete-btn')) {
      console.log('Executing delete action for:', { id, type });
      this.deleteContent(id, type);
    } else if (button.classList.contains('view-btn')) {
      console.log('Executing view action for:', { id, type });
      this.viewContent(id, type);
    } else {
      console.error('Unknown action button type:', button.classList.toString());
    }
  }

  /**
   * Load performance data
   */
  async loadPerformanceData() {
    console.log('Loading performance data...');

    // Setup performance event listeners
    this.setupPerformanceListeners();

    // Show loading state first
    const loadingHtml = `
      <div class="stat-item">
        <span class="stat-label">Status</span>
        <span class="stat-value">Loading...</span>
      </div>
    `;

    this.updatePerformanceCard('memory-stats', loadingHtml);
    this.updatePerformanceCard('cache-stats', loadingHtml);
    this.updatePerformanceCard('rate-limiter-stats', loadingHtml);
    this.updatePerformanceCard('db-performance', loadingHtml);

    try {
      console.log('Fetching performance data from /api/performance...');

      // Add a timeout to the fetch request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch('/api/performance', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      console.log('Performance API response status:', response.status);
      console.log('Performance API response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Performance API error response:', errorText);
        throw new Error(`Performance API returned ${response.status}: ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();
      console.log('Performance API full response:', result);

      if (!result.success) {
        throw new Error(result.error || 'Performance API returned unsuccessful response');
      }

      const performanceData = result.data;
      console.log('Performance data extracted:', performanceData);

      // Update memory stats
      console.log('Updating memory stats with:', performanceData.memory);
      this.updatePerformanceCard('memory-stats', this.formatMemoryStats(performanceData.memory));

      // Update cache stats
      console.log('Updating cache stats with:', performanceData.cache);
      this.updatePerformanceCard('cache-stats', this.formatCacheStats(performanceData.cache));

      // Update rate limiter stats
      console.log('Updating rate limiter stats with:', performanceData.rateLimiters);
      this.updatePerformanceCard('rate-limiter-stats', this.formatRateLimiterStats(performanceData.rateLimiters));

      // Update database performance
      console.log('Updating database performance with:', performanceData.database);
      this.updatePerformanceCard('db-performance', this.formatDbPerformance(performanceData.database));

      console.log('All performance cards updated successfully');

    } catch (error) {
      console.error('Error loading performance data:', error);

      // Show specific error messages for each card
      const errorHtml = (cardName, errorMsg) => `
        <div class="stat-item">
          <span class="stat-label">${cardName} Status</span>
          <span class="stat-value error">API Error</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Error Details</span>
          <span class="stat-value error">${errorMsg}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Endpoint</span>
          <span class="stat-value">/api/performance</span>
        </div>
      `;

      this.updatePerformanceCard('memory-stats', errorHtml('Memory', error.message));
      this.updatePerformanceCard('cache-stats', errorHtml('Cache', error.message));
      this.updatePerformanceCard('rate-limiter-stats', errorHtml('Rate Limiters', error.message));
      this.updatePerformanceCard('db-performance', errorHtml('Database', error.message));
    }
  }

  /**
   * Setup performance event listeners
   */
  setupPerformanceListeners() {
    // Refresh performance button
    const refreshBtn = document.getElementById('refresh-performance-btn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        console.log('Refreshing performance data...');
        this.loadPerformanceData();
      });
    }

    // Clear cache button
    const clearCacheBtn = document.getElementById('clear-cache-btn');
    if (clearCacheBtn) {
      clearCacheBtn.addEventListener('click', () => this.clearCache());
    }

    // Optimize database button
    const optimizeDbBtn = document.getElementById('optimize-db-btn');
    if (optimizeDbBtn) {
      optimizeDbBtn.addEventListener('click', () => this.optimizeDatabase());
    }
  }

  /**
   * Clear cache
   */
  async clearCache() {
    try {
      // Show closeable progress modal
      const progressModal = this.createProgressModal(
        'Cache Management',
        'Clearing application cache...',
        'This will remove all cached data to free up memory and ensure fresh data loading.'
      );

      document.body.appendChild(progressModal);
      progressModal.style.display = 'block';

      // Start the cache clearing process
      console.log('Starting cache clearing...');

      // Call the cache clearing API endpoint
      const response = await fetch('/api/cache/clear', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken') || 'admin'}`
        }
      });

      if (!response.ok) {
        throw new Error(`Cache API returned ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        // Update progress modal with success message
        this.updateProgressModal(progressModal, 'success', 'Cache cleared successfully!', result.message || 'All cached data has been removed.');

        // Auto-close after 2 seconds
        setTimeout(() => {
          if (progressModal.parentNode) {
            progressModal.remove();
          }
        }, 2000);

        // Refresh performance data
        this.loadPerformanceData();
      } else {
        throw new Error(result.message || 'Failed to clear cache');
      }

    } catch (error) {
      console.error('Error clearing cache:', error);

      // Show error in modal if it exists, otherwise create error modal
      const existingModal = document.querySelector('.progress-modal');
      if (existingModal) {
        this.updateProgressModal(existingModal, 'error', 'Cache clearing failed', error.message);
      } else {
        const errorModal = this.createProgressModal(
          'Cache Management Error',
          'Failed to clear cache',
          error.message
        );
        document.body.appendChild(errorModal);
        errorModal.style.display = 'block';

        // Auto-close error modal after 4 seconds
        setTimeout(() => {
          if (errorModal.parentNode) {
            errorModal.remove();
          }
        }, 4000);
      }
    }
  }

  /**
   * Optimize database
   */
  async optimizeDatabase() {
    try {
      // Show closeable progress modal
      const progressModal = this.createProgressModal(
        'Database Optimization',
        'Optimizing database indexes and cleaning up data...',
        'This process may take several minutes. You can close this dialog and the optimization will continue in the background.'
      );

      document.body.appendChild(progressModal);
      progressModal.style.display = 'block';

      // Start the optimization process
      console.log('Starting database optimization...');

      // Call the database optimization API endpoint
      const response = await fetch('/api/admin/optimize-database', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken') || 'admin'}`
        }
      });

      if (!response.ok) {
        throw new Error(`Optimization API returned ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      // Update progress modal with success message
      this.updateProgressModal(progressModal, 'success', 'Database optimization completed successfully!', result.message || 'All database operations completed.');

      // Auto-close after 3 seconds
      setTimeout(() => {
        if (progressModal.parentNode) {
          progressModal.remove();
        }
      }, 3000);

      // Refresh performance data
      this.loadPerformanceData();

    } catch (error) {
      console.error('Error optimizing database:', error);

      // Show error in modal if it exists, otherwise create error modal
      const existingModal = document.querySelector('.progress-modal');
      if (existingModal) {
        this.updateProgressModal(existingModal, 'error', 'Database optimization failed', error.message);
      } else {
        const errorModal = this.createProgressModal(
          'Database Optimization Error',
          'Failed to optimize database',
          error.message
        );
        document.body.appendChild(errorModal);
        errorModal.style.display = 'block';

        // Auto-close error modal after 5 seconds
        setTimeout(() => {
          if (errorModal.parentNode) {
            errorModal.remove();
          }
        }, 5000);
      }
    }
  }

  /**
   * Create progress modal
   */
  createProgressModal(title, message, description) {
    const modal = document.createElement('div');
    modal.className = 'modal progress-modal';
    modal.innerHTML = `
      <div class="modal-content progress-modal-content">
        <div class="progress-modal-header">
          <h3><i class="fas fa-cogs"></i> ${title}</h3>
          <span class="close progress-modal-close">&times;</span>
        </div>
        <div class="progress-modal-body">
          <div class="progress-status">
            <div class="progress-icon">
              <i class="fas fa-spinner fa-spin"></i>
            </div>
            <div class="progress-text">
              <h4 class="progress-message">${message}</h4>
              <p class="progress-description">${description}</p>
            </div>
          </div>
          <div class="progress-bar-container">
            <div class="progress-bar">
              <div class="progress-bar-fill"></div>
            </div>
          </div>
        </div>
        <div class="progress-modal-footer">
          <button class="button secondary close-progress-btn">Close</button>
        </div>
      </div>
    `;

    // Setup close functionality
    const closeBtn = modal.querySelector('.progress-modal-close');
    const closeButton = modal.querySelector('.close-progress-btn');

    const closeModal = () => {
      modal.remove();
    };

    closeBtn.addEventListener('click', closeModal);
    closeButton.addEventListener('click', closeModal);

    // Close on background click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        closeModal();
      }
    });

    return modal;
  }

  /**
   * Update progress modal
   */
  updateProgressModal(modal, status, message, description) {
    const progressIcon = modal.querySelector('.progress-icon i');
    const progressMessage = modal.querySelector('.progress-message');
    const progressDescription = modal.querySelector('.progress-description');
    const progressBarFill = modal.querySelector('.progress-bar-fill');

    // Update icon based on status
    progressIcon.className = status === 'success' ? 'fas fa-check-circle' :
                             status === 'error' ? 'fas fa-exclamation-circle' :
                             'fas fa-spinner fa-spin';

    // Update colors based on status
    if (status === 'success') {
      progressIcon.style.color = '#4caf50';
      progressBarFill.style.background = '#4caf50';
      progressBarFill.style.width = '100%';
    } else if (status === 'error') {
      progressIcon.style.color = '#f44336';
      progressBarFill.style.background = '#f44336';
      progressBarFill.style.width = '100%';
    }

    // Update text
    progressMessage.textContent = message;
    progressDescription.textContent = description;
  }

  /**
   * Load scraping data
   */
  async loadScrapingData() {
    console.log('Loading scraping data...');

    // Setup scraping event listeners
    this.setupScrapingListeners();

    try {
      // Show initial scraping data
      const scrapingStats = document.getElementById('scraping-stats');
      if (scrapingStats) {
        scrapingStats.innerHTML = `
          <div class="stat-item">
            <span class="stat-label">Status</span>
            <span class="stat-value success">Idle</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Last Run</span>
            <span class="stat-value">Never</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Items Scraped</span>
            <span class="stat-value">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Success Rate</span>
            <span class="stat-value success">100%</span>
          </div>
        `;
      }

      // Update scraping status
      this.updateScrapingStatus('idle', 0);

    } catch (error) {
      console.error('Error loading scraping data:', error);
    }
  }

  /**
   * Setup scraping event listeners
   */
  setupScrapingListeners() {
    // Start scraping button
    const startScrapingBtn = document.getElementById('start-scraping-btn');
    if (startScrapingBtn) {
      startScrapingBtn.addEventListener('click', () => this.startScraping());
    }

    // Save scraping config button
    const saveConfigBtn = document.getElementById('save-scraping-config-btn');
    if (saveConfigBtn) {
      saveConfigBtn.addEventListener('click', () => this.saveScrapingConfig());
    }

    // Clear logs button
    const clearLogsBtn = document.getElementById('clear-logs-btn');
    if (clearLogsBtn) {
      clearLogsBtn.addEventListener('click', () => this.clearScrapingLogs());
    }

    // Auto scroll button
    const autoScrollBtn = document.getElementById('auto-scroll-btn');
    if (autoScrollBtn) {
      autoScrollBtn.addEventListener('click', () => this.toggleAutoScroll());
    }
  }

  /**
   * Start scraping
   */
  async startScraping() {
    const scrapingMode = document.getElementById('scraping-mode')?.value || 'latest';
    const scrapingType = document.getElementById('scraping-type')?.value || 'all';
    const scrapingPages = document.getElementById('scraping-pages')?.value || 5;
    const enableEnrichment = document.getElementById('enable-enrichment')?.checked || false;
    const enableGemini = document.getElementById('enable-gemini')?.checked || false;

    console.log('Starting scraping with config:', {
      mode: scrapingMode,
      type: scrapingType,
      pages: scrapingPages,
      enrichment: enableEnrichment,
      gemini: enableGemini
    });

    try {
      // Update UI to show scraping started
      this.updateScrapingStatus('running', 0);
      this.addScrapingLog('info', 'Starting scraping operation...');

      // Disable start button
      const startBtn = document.getElementById('start-scraping-btn');
      if (startBtn) {
        startBtn.disabled = true;
        startBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Scraping...';
      }

      // Call the existing scraping endpoint
      const response = await fetch('/api/scrape', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mode: scrapingMode,
          type: scrapingType,
          pages: parseInt(scrapingPages),
          enrichment: enableEnrichment,
          gemini: enableGemini
        })
      });

      if (!response.ok) {
        throw new Error(`Scraping API returned ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Scraping response:', result);

      if (result.success) {
        this.addScrapingLog('success', result.message || 'Scraping started successfully');
        this.updateScrapingStatus('running', 0);

        // Start monitoring scraping progress
        this.monitorScrapingProgress();
      } else {
        throw new Error(result.message || 'Failed to start scraping');
      }

    } catch (error) {
      console.error('Error starting scraping:', error);
      this.addScrapingLog('error', `Failed to start scraping: ${error.message}`);
      this.updateScrapingStatus('error', 0);

      // Re-enable start button
      const startBtn = document.getElementById('start-scraping-btn');
      if (startBtn) {
        startBtn.disabled = false;
        startBtn.innerHTML = '<i class="fas fa-spider"></i> Start Scraping';
      }
    }
  }

  /**
   * Update scraping status
   */
  updateScrapingStatus(status, progress) {
    const statusText = document.getElementById('scraping-status-text');
    const progressBar = document.getElementById('scraping-progress');

    if (statusText) {
      statusText.textContent = status.charAt(0).toUpperCase() + status.slice(1);
      statusText.className = `status-value ${status === 'error' ? 'error' : status === 'running' ? 'warning' : 'success'}`;
    }

    if (progressBar) {
      progressBar.style.width = `${progress}%`;
    }
  }

  /**
   * Add scraping log entry
   */
  addScrapingLog(level, message) {
    const logsContainer = document.getElementById('scraping-logs');
    if (!logsContainer) return;

    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${level}`;
    logEntry.innerHTML = `<span>[${timestamp}] ${level.toUpperCase()}: ${message}</span>`;

    logsContainer.appendChild(logEntry);

    // Auto scroll if enabled
    const autoScrollBtn = document.getElementById('auto-scroll-btn');
    if (autoScrollBtn && autoScrollBtn.dataset.enabled === 'true') {
      logsContainer.scrollTop = logsContainer.scrollHeight;
    }

    // Limit log entries to prevent memory issues
    const logEntries = logsContainer.querySelectorAll('.log-entry');
    if (logEntries.length > 100) {
      logEntries[0].remove();
    }
  }

  /**
   * Clear scraping logs
   */
  clearScrapingLogs() {
    const logsContainer = document.getElementById('scraping-logs');
    if (logsContainer) {
      logsContainer.innerHTML = '';
      this.addScrapingLog('info', 'Logs cleared');
    }
  }

  /**
   * Toggle auto scroll
   */
  toggleAutoScroll() {
    const autoScrollBtn = document.getElementById('auto-scroll-btn');
    if (!autoScrollBtn) return;

    const enabled = autoScrollBtn.dataset.enabled === 'true';
    autoScrollBtn.dataset.enabled = (!enabled).toString();
    autoScrollBtn.innerHTML = `<i class="fas fa-arrow-down"></i> Auto Scroll ${!enabled ? 'ON' : 'OFF'}`;
    autoScrollBtn.className = `button ${!enabled ? 'primary' : 'secondary'}`;
  }

  /**
   * Monitor scraping progress
   */
  monitorScrapingProgress() {
    // This would typically connect to WebSocket for real-time updates
    // For now, simulate progress updates
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 10;
      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);
        this.updateScrapingStatus('completed', 100);
        this.addScrapingLog('success', 'Scraping completed successfully');

        // Re-enable start button
        const startBtn = document.getElementById('start-scraping-btn');
        if (startBtn) {
          startBtn.disabled = false;
          startBtn.innerHTML = '<i class="fas fa-spider"></i> Start Scraping';
        }
      } else {
        this.updateScrapingStatus('running', Math.round(progress));
        this.addScrapingLog('info', `Scraping progress: ${Math.round(progress)}%`);
      }
    }, 2000);
  }

  /**
   * Save scraping config
   */
  saveScrapingConfig() {
    const config = {
      pages: document.getElementById('scraping-pages')?.value || 5,
      enrichment: document.getElementById('enable-enrichment')?.checked || false,
      gemini: document.getElementById('enable-gemini')?.checked || false
    };

    console.log('Saving scraping config:', config);
    localStorage.setItem('scrapingConfig', JSON.stringify(config));
    this.addScrapingLog('info', 'Scraping configuration saved');
  }

  /**
   * Load users data
   */
  async loadUsersData() {
    console.log('Loading users analytics data...');

    // Setup users analytics event listeners
    this.setupUsersAnalyticsListeners();

    try {
      // Show user activity stats - compact format
      const userActivityStats = document.getElementById('user-activity-stats');
      if (userActivityStats) {
        userActivityStats.innerHTML = `
          <div class="compact-stats-grid">
            <div class="compact-stat">
              <div class="compact-stat-value">0</div>
              <div class="compact-stat-label">Active Users</div>
            </div>
            <div class="compact-stat">
              <div class="compact-stat-value">0</div>
              <div class="compact-stat-label">Sessions</div>
            </div>
            <div class="compact-stat">
              <div class="compact-stat-value">0</div>
              <div class="compact-stat-label">Page Views</div>
            </div>
            <div class="compact-stat">
              <div class="compact-stat-value">0 min</div>
              <div class="compact-stat-label">Avg. Duration</div>
            </div>
          </div>
        `;
      }

      // Show content views stats - compact format
      const contentViewsStats = document.getElementById('content-views-stats');
      if (contentViewsStats) {
        contentViewsStats.innerHTML = `
          <div class="compact-stats-grid">
            <div class="compact-stat">
              <div class="compact-stat-value">0</div>
              <div class="compact-stat-label">Movies</div>
            </div>
            <div class="compact-stat">
              <div class="compact-stat-value">0</div>
              <div class="compact-stat-label">Series</div>
            </div>
            <div class="compact-stat">
              <div class="compact-stat-value">0</div>
              <div class="compact-stat-label">Anime</div>
            </div>
            <div class="compact-stat">
              <div class="compact-stat-value">0</div>
              <div class="compact-stat-label">Live TV</div>
            </div>
          </div>
        `;
      }

      // Show popular content
      const popularContent = document.getElementById('popular-content');
      if (popularContent) {
        popularContent.innerHTML = `
          <div class="stat-item">
            <span class="stat-label">Most Popular</span>
            <span class="stat-value">No data available</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Trending Today</span>
            <span class="stat-value">No data available</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Top Genre</span>
            <span class="stat-value">No data available</span>
          </div>
        `;
      }

      // Show search analytics - compact format
      const searchAnalytics = document.getElementById('search-analytics');
      if (searchAnalytics) {
        searchAnalytics.innerHTML = `
          <div class="compact-stats-grid">
            <div class="compact-stat">
              <div class="compact-stat-value">0</div>
              <div class="compact-stat-label">Total Searches</div>
            </div>
            <div class="compact-stat">
              <div class="compact-stat-value">0%</div>
              <div class="compact-stat-label">Success Rate</div>
            </div>
            <div class="compact-stat">
              <div class="compact-stat-value">-</div>
              <div class="compact-stat-label">Top Term</div>
            </div>
            <div class="compact-stat">
              <div class="compact-stat-value">0</div>
              <div class="compact-stat-label">No Results</div>
            </div>
          </div>
        `;
      }

    } catch (error) {
      console.error('Error loading users data:', error);
    }
  }

  /**
   * Setup users analytics event listeners
   */
  setupUsersAnalyticsListeners() {
    // Refresh analytics button
    const refreshBtn = document.getElementById('refresh-analytics-btn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        console.log('Refreshing analytics data...');
        this.loadUsersData();
      });
    }

    // Analytics timeframe selector
    const timeframeSelect = document.getElementById('analytics-timeframe');
    if (timeframeSelect) {
      timeframeSelect.addEventListener('change', (e) => {
        console.log('Analytics timeframe changed to:', e.target.value);
        this.loadUsersData();
      });
    }
  }

  /**
   * Load system data
   */
  async loadSystemData() {
    console.log('Loading system data...');

    // Setup system management event listeners
    this.setupSystemManagementListeners();

    try {
      console.log('Fetching system performance data...');

      // Try to load system data from performance API
      let performanceData = null;
      try {
        const response = await fetch('/api/performance');
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            performanceData = result.data;
            console.log('System performance data received:', performanceData);
          } else {
            throw new Error(result.error || 'Performance API returned unsuccessful response');
          }
        } else {
          throw new Error(`Performance API returned ${response.status}`);
        }
      } catch (apiError) {
        console.warn('Performance API unavailable, using fallback data:', apiError.message);
      }

      // Try to load storage data from storage API
      let storageData = null;
      try {
        const response = await fetch('/api/system/storage');
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            storageData = result.data;
            console.log('Storage data received:', storageData);
          }
        }
      } catch (storageError) {
        console.warn('Storage API unavailable:', storageError.message);
      }

      // Update server resources
      this.updateSystemCard('server-resources', this.formatServerResourcesCompact(performanceData));

      // Update storage usage
      this.updateSystemCard('storage-usage', this.formatStorageUsageCompact(storageData));

      // Update network status
      this.updateSystemCard('network-status', this.formatNetworkStatusCompact());

      // Update security status
      this.updateSystemCard('security-status', this.formatSecurityStatusCompact());

      // Update running processes
      this.updateRunningProcesses();

      // Update system errors
      this.updateSystemErrors();

    } catch (error) {
      console.error('Error loading system data:', error);

      // Show error state for all cards
      ['server-resources', 'storage-usage', 'network-status', 'security-status', 'running-processes', 'system-errors'].forEach(cardId => {
        this.updateSystemCard(cardId, `
          <div class="compact-stat">
            <div class="compact-stat-value error">Error</div>
            <div class="compact-stat-label">System Error</div>
          </div>
        `);
      });
    }
  }

  /**
   * Setup system management event listeners
   */
  setupSystemManagementListeners() {
    // Restart server button
    const restartBtn = document.getElementById('restart-server-btn');
    if (restartBtn) {
      restartBtn.addEventListener('click', () => this.restartServer());
    }

    // Backup database button
    const backupBtn = document.getElementById('backup-database-btn');
    if (backupBtn) {
      backupBtn.addEventListener('click', () => this.backupDatabase());
    }

    // Maintenance mode button
    const maintenanceBtn = document.getElementById('maintenance-mode-btn');
    if (maintenanceBtn) {
      maintenanceBtn.addEventListener('click', () => this.toggleMaintenanceMode());
    }
  }

  /**
   * Update running processes
   */
  updateRunningProcesses() {
    const processesContainer = document.getElementById('running-processes');
    if (processesContainer) {
      processesContainer.innerHTML = `
        <div class="processes-list">
          <div class="process-item">
            <span class="process-name">Node.js Server</span>
            <span class="process-status success">Running</span>
            <span class="process-memory">~150MB</span>
          </div>
          <div class="process-item">
            <span class="process-name">MongoDB</span>
            <span class="process-status success">Connected</span>
            <span class="process-memory">~50MB</span>
          </div>
          <div class="process-item">
            <span class="process-name">GraphQL Server</span>
            <span class="process-status success">Active</span>
            <span class="process-memory">~20MB</span>
          </div>
          <div class="process-item">
            <span class="process-name">WebSocket Server</span>
            <span class="process-status success">Listening</span>
            <span class="process-memory">~10MB</span>
          </div>
        </div>
      `;
    }
  }

  /**
   * Restart server
   */
  async restartServer() {
    try {
      // Show confirmation modal
      const confirmed = await this.showConfirmationModal(
        'Restart Server',
        'Are you sure you want to restart the server?',
        'This will temporarily interrupt service for all users. The restart process typically takes 30-60 seconds.'
      );

      if (!confirmed) return;

      // Show progress modal
      const progressModal = this.createProgressModal(
        'Server Restart',
        'Initiating server restart...',
        'The server is being restarted. This page will automatically refresh when the server is back online.'
      );

      document.body.appendChild(progressModal);
      progressModal.style.display = 'block';

      // Call the server restart API endpoint
      console.log('Calling server restart API...');
      const response = await fetch('/api/admin/restart-server', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken') || 'admin'}`
        }
      });

      if (!response.ok) {
        throw new Error(`Restart API returned ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        // Update progress modal
        this.updateProgressModal(progressModal, 'success', 'Server restart initiated successfully!', 'The server is restarting. This page will refresh automatically...');

        // Wait for server to restart and then refresh page
        setTimeout(() => {
          this.waitForServerAndRefresh();
        }, 3000);
      } else {
        throw new Error(result.message || 'Server restart failed');
      }

    } catch (error) {
      console.error('Error restarting server:', error);

      // Show error in modal if it exists, otherwise create error modal
      const existingModal = document.querySelector('.progress-modal');
      if (existingModal) {
        this.updateProgressModal(existingModal, 'error', 'Server restart failed', error.message);
      } else {
        const errorModal = this.createProgressModal(
          'Server Restart Error',
          'Failed to restart server',
          error.message
        );
        document.body.appendChild(errorModal);
        errorModal.style.display = 'flex';

        // Auto-close error modal after 5 seconds
        setTimeout(() => {
          if (errorModal.parentNode) {
            errorModal.remove();
          }
        }, 5000);
      }
    }
  }

  /**
   * Backup database
   */
  async backupDatabase() {
    try {
      // Show confirmation modal
      const confirmed = await this.showConfirmationModal(
        'Database Backup',
        'Create a backup of the database?',
        'This will create a complete backup of all collections (movies, series, anime, live TV) and download it to your computer. The process may take several minutes depending on database size.'
      );

      if (!confirmed) return;

      // Show progress modal
      const progressModal = this.createProgressModal(
        'Database Backup',
        'Creating database backup...',
        'Please wait while the database backup is being created. This may take several minutes for large databases.'
      );

      document.body.appendChild(progressModal);
      progressModal.style.display = 'flex';

      // Call the database backup API endpoint
      console.log('Calling database backup API...');
      const response = await fetch('/api/admin/backup-database', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken') || 'admin'}`
        }
      });

      if (!response.ok) {
        throw new Error(`Backup API returned ${response.status}: ${response.statusText}`);
      }

      // Check if response is JSON or file download
      const contentType = response.headers.get('content-type');

      if (contentType && contentType.includes('application/json')) {
        // JSON response - likely an error or status update
        const result = await response.json();

        if (result.success) {
          // Update progress modal
          this.updateProgressModal(progressModal, 'success', 'Database backup completed!', result.message || 'Backup file has been created and downloaded.');

          // Auto-close after 3 seconds
          setTimeout(() => {
            if (progressModal.parentNode) {
              progressModal.remove();
            }
          }, 3000);
        } else {
          throw new Error(result.message || 'Database backup failed');
        }
      } else {
        // File download response
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;

        // Get filename from response headers or create default
        const disposition = response.headers.get('content-disposition');
        let filename = 'netstream_backup.json';
        if (disposition && disposition.includes('filename=')) {
          filename = disposition.split('filename=')[1].replace(/"/g, '');
        }

        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        // Update progress modal
        this.updateProgressModal(progressModal, 'success', 'Database backup downloaded!', `Backup file "${filename}" has been downloaded to your computer.`);

        // Auto-close after 3 seconds
        setTimeout(() => {
          if (progressModal.parentNode) {
            progressModal.remove();
          }
        }, 3000);
      }

    } catch (error) {
      console.error('Error backing up database:', error);

      // Show error in modal if it exists, otherwise create error modal
      const existingModal = document.querySelector('.progress-modal');
      if (existingModal) {
        this.updateProgressModal(existingModal, 'error', 'Database backup failed', error.message);
      } else {
        const errorModal = this.createProgressModal(
          'Database Backup Error',
          'Failed to create database backup',
          error.message
        );
        document.body.appendChild(errorModal);
        errorModal.style.display = 'flex';

        // Auto-close error modal after 5 seconds
        setTimeout(() => {
          if (errorModal.parentNode) {
            errorModal.remove();
          }
        }, 5000);
      }
    }
  }

  /**
   * Toggle maintenance mode
   */
  async toggleMaintenanceMode() {
    try {
      const isMaintenanceMode = localStorage.getItem('maintenanceMode') === 'true';
      const newMode = !isMaintenanceMode;

      // Show confirmation modal
      const confirmed = await this.showConfirmationModal(
        `${newMode ? 'Enable' : 'Disable'} Maintenance Mode`,
        `Are you sure you want to ${newMode ? 'enable' : 'disable'} maintenance mode?`,
        newMode
          ? 'This will show a maintenance page to all regular users. Only admin users will be able to access the application.'
          : 'This will restore normal access for all users.'
      );

      if (!confirmed) return;

      // Show progress modal
      const progressModal = this.createProgressModal(
        'Maintenance Mode',
        `${newMode ? 'Enabling' : 'Disabling'} maintenance mode...`,
        'Please wait while the maintenance mode setting is being updated.'
      );

      document.body.appendChild(progressModal);
      progressModal.style.display = 'flex';

      // Call the maintenance mode API endpoint
      console.log('Calling maintenance mode API...');
      const response = await fetch('/api/admin/maintenance-mode', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken') || 'admin'}`
        },
        body: JSON.stringify({ enabled: newMode })
      });

      if (!response.ok) {
        throw new Error(`Maintenance API returned ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        // Update local storage
        localStorage.setItem('maintenanceMode', newMode.toString());

        // Update button
        const btn = document.getElementById('maintenance-mode-btn');
        if (btn) {
          btn.innerHTML = `<i class="fas fa-tools"></i> ${newMode ? 'Disable' : 'Enable'} Maintenance Mode`;
          btn.className = `button ${newMode ? 'warning' : 'danger'}`;
        }

        // Update progress modal
        this.updateProgressModal(
          progressModal,
          'success',
          `Maintenance mode ${newMode ? 'enabled' : 'disabled'}!`,
          result.message || `Maintenance mode has been ${newMode ? 'enabled' : 'disabled'} successfully.`
        );

        // Auto-close after 3 seconds
        setTimeout(() => {
          if (progressModal.parentNode) {
            progressModal.remove();
          }
        }, 3000);
      } else {
        throw new Error(result.message || 'Failed to toggle maintenance mode');
      }

    } catch (error) {
      console.error('Error toggling maintenance mode:', error);

      // Show error in modal if it exists, otherwise create error modal
      const existingModal = document.querySelector('.progress-modal');
      if (existingModal) {
        this.updateProgressModal(existingModal, 'error', 'Maintenance mode toggle failed', error.message);
      } else {
        const errorModal = this.createProgressModal(
          'Maintenance Mode Error',
          'Failed to toggle maintenance mode',
          error.message
        );
        document.body.appendChild(errorModal);
        errorModal.style.display = 'flex';

        // Auto-close error modal after 5 seconds
        setTimeout(() => {
          if (errorModal.parentNode) {
            errorModal.remove();
          }
        }, 5000);
      }
    }
  }

  /**
   * Show confirmation modal
   */
  async showConfirmationModal(title, message, details) {
    return new Promise((resolve) => {
      const modal = document.createElement('div');
      modal.className = 'confirmation-modal';
      modal.innerHTML = `
        <div class="confirmation-modal-content">
          <div class="confirmation-modal-header">
            <h3>${title}</h3>
          </div>
          <div class="confirmation-modal-body">
            <p class="confirmation-message">${message}</p>
            ${details ? `<p class="confirmation-details">${details}</p>` : ''}
          </div>
          <div class="confirmation-modal-footer">
            <button class="button secondary" id="confirmation-cancel">Cancel</button>
            <button class="button primary" id="confirmation-confirm">Confirm</button>
          </div>
        </div>
      `;

      document.body.appendChild(modal);
      modal.style.display = 'flex';

      // Add event listeners
      document.getElementById('confirmation-cancel').addEventListener('click', () => {
        modal.remove();
        resolve(false);
      });

      document.getElementById('confirmation-confirm').addEventListener('click', () => {
        modal.remove();
        resolve(true);
      });

      // Close on background click
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          modal.remove();
          resolve(false);
        }
      });
    });
  }

  /**
   * Create progress modal
   */
  createProgressModal(title, message, details) {
    const modal = document.createElement('div');
    modal.className = 'progress-modal';
    modal.innerHTML = `
      <div class="progress-modal-content">
        <div class="progress-modal-header">
          <h3>${title}</h3>
        </div>
        <div class="progress-modal-body">
          <div class="progress-spinner">
            <i class="fas fa-spinner fa-spin"></i>
          </div>
          <p class="progress-message">${message}</p>
          ${details ? `<p class="progress-details">${details}</p>` : ''}
        </div>
      </div>
    `;

    return modal;
  }

  /**
   * Update progress modal
   */
  updateProgressModal(modal, status, message, details) {
    const spinner = modal.querySelector('.progress-spinner i');
    const messageEl = modal.querySelector('.progress-message');
    const detailsEl = modal.querySelector('.progress-details');

    if (status === 'success') {
      spinner.className = 'fas fa-check-circle success';
    } else if (status === 'error') {
      spinner.className = 'fas fa-exclamation-circle error';
    }

    messageEl.textContent = message;
    if (detailsEl && details) {
      detailsEl.textContent = details;
    }
  }

  /**
   * Wait for server and refresh
   */
  async waitForServerAndRefresh() {
    const maxAttempts = 30; // 30 attempts = 60 seconds
    let attempts = 0;

    const checkServer = async () => {
      try {
        const response = await fetch('/api/performance', {
          method: 'GET',
          timeout: 5000
        });

        if (response.ok) {
          console.log('Server is back online, refreshing page...');
          window.location.reload();
          return;
        }
      } catch (error) {
        console.log(`Server check attempt ${attempts + 1} failed:`, error.message);
      }

      attempts++;
      if (attempts < maxAttempts) {
        setTimeout(checkServer, 2000); // Check every 2 seconds
      } else {
        console.log('Server restart timeout, manual refresh required');
        alert('Server restart is taking longer than expected. Please refresh the page manually.');
      }
    };

    // Start checking after initial delay
    setTimeout(checkServer, 5000);
  }

  /**
   * Show add content modal
   */
  showAddContentModal(type) {
    const modal = this.createAddContentModal(type);
    document.body.appendChild(modal);
    modal.style.display = 'block';

    // Setup modal event listeners
    this.setupAddContentModalListeners(modal, type);
  }

  /**
   * Create add content modal
   */
  createAddContentModal(type) {
    const modal = document.createElement('div');
    modal.className = 'modal add-content-modal';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h2><i class="fas fa-plus"></i> Add New ${type.charAt(0).toUpperCase() + type.slice(1)}</h2>
          <span class="close">&times;</span>
        </div>
        <div class="modal-body">
          ${this.getAddContentForm(type)}
        </div>
        <div class="modal-footer">
          <button class="button secondary cancel-btn">Cancel</button>
          <button class="button primary save-btn">Save ${type.charAt(0).toUpperCase() + type.slice(1)}</button>
        </div>
      </div>
    `;
    return modal;
  }

  /**
   * Get add content form based on type
   */
  getAddContentForm(type) {
    const commonFields = `
      <div class="form-group">
        <label for="add-title">Title *</label>
        <input type="text" id="add-title" required>
      </div>
      <div class="form-group">
        <label for="add-description">Description</label>
        <textarea id="add-description" rows="3"></textarea>
      </div>
      <div class="form-group">
        <label for="add-thumbnail">Thumbnail URL</label>
        <input type="url" id="add-thumbnail">
      </div>
    `;

    switch (type) {
      case 'movie':
        return `
          ${commonFields}
          <div class="form-group">
            <label for="add-year">Year</label>
            <input type="number" id="add-year" min="1900" max="2030">
          </div>
          <div class="form-group">
            <label for="add-duration">Duration (minutes)</label>
            <input type="number" id="add-duration" min="1">
          </div>
          <div class="form-group">
            <label for="add-genre">Genre</label>
            <input type="text" id="add-genre" placeholder="Action, Drama, Comedy">
          </div>
          <div class="form-group">
            <label for="add-rating">Rating</label>
            <input type="number" id="add-rating" min="0" max="10" step="0.1">
          </div>
          <div class="form-group">
            <label for="add-streaming-url">Streaming URL *</label>
            <input type="url" id="add-streaming-url" required>
          </div>
        `;

      case 'series':
        return `
          ${commonFields}
          <div class="form-group">
            <label for="add-year">Year</label>
            <input type="number" id="add-year" min="1900" max="2030">
          </div>
          <div class="form-group">
            <label for="add-seasons">Number of Seasons</label>
            <input type="number" id="add-seasons" min="1">
          </div>
          <div class="form-group">
            <label for="add-episodes">Total Episodes</label>
            <input type="number" id="add-episodes" min="1">
          </div>
          <div class="form-group">
            <label for="add-genre">Genre</label>
            <input type="text" id="add-genre" placeholder="Drama, Thriller, Comedy">
          </div>
          <div class="form-group">
            <label for="add-rating">Rating</label>
            <input type="number" id="add-rating" min="0" max="10" step="0.1">
          </div>
          <div class="form-group">
            <label for="add-status">Status</label>
            <select id="add-status">
              <option value="ongoing">Ongoing</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
        `;

      case 'anime':
        return `
          ${commonFields}
          <div class="form-group">
            <label for="add-year">Year</label>
            <input type="number" id="add-year" min="1900" max="2030">
          </div>
          <div class="form-group">
            <label for="add-episodes">Episodes</label>
            <input type="number" id="add-episodes" min="1">
          </div>
          <div class="form-group">
            <label for="add-studio">Studio</label>
            <input type="text" id="add-studio">
          </div>
          <div class="form-group">
            <label for="add-genre">Genre</label>
            <input type="text" id="add-genre" placeholder="Action, Romance, Supernatural">
          </div>
          <div class="form-group">
            <label for="add-rating">Rating</label>
            <input type="number" id="add-rating" min="0" max="10" step="0.1">
          </div>
          <div class="form-group">
            <label for="add-status">Status</label>
            <select id="add-status">
              <option value="airing">Currently Airing</option>
              <option value="finished">Finished Airing</option>
              <option value="upcoming">Not Yet Aired</option>
            </select>
          </div>
        `;

      case 'livetv':
        return `
          ${commonFields}
          <div class="form-group">
            <label for="add-category">Category</label>
            <input type="text" id="add-category" placeholder="News, Sports, Entertainment">
          </div>
          <div class="form-group">
            <label for="add-language">Language</label>
            <input type="text" id="add-language" placeholder="English, French, Spanish">
          </div>
          <div class="form-group">
            <label for="add-country">Country</label>
            <input type="text" id="add-country">
          </div>
          <div class="form-group">
            <label for="add-streaming-url">Streaming URL *</label>
            <input type="url" id="add-streaming-url" required>
          </div>
          <div class="form-group">
            <label for="add-quality">Quality</label>
            <select id="add-quality">
              <option value="HD">HD</option>
              <option value="SD">SD</option>
              <option value="4K">4K</option>
            </select>
          </div>
        `;

      default:
        return commonFields;
    }
  }

  /**
   * Setup add content modal listeners
   */
  setupAddContentModalListeners(modal, type) {
    // Close modal
    modal.querySelector('.close').addEventListener('click', () => {
      modal.remove();
    });

    modal.querySelector('.cancel-btn').addEventListener('click', () => {
      modal.remove();
    });

    // Save content
    modal.querySelector('.save-btn').addEventListener('click', () => {
      this.saveNewContent(modal, type);
    });

    // Close on outside click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });
  }

  /**
   * Save new content
   */
  async saveNewContent(modal, type) {
    try {
      const formData = this.extractFormData(modal, type);

      if (!this.validateFormData(formData, type)) {
        return;
      }

      // Show loading state
      const saveBtn = modal.querySelector('.save-btn');
      saveBtn.disabled = true;
      saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

      // Create the content via GraphQL mutation
      const result = await this.createContent(formData, type);

      if (result.success) {
        alert(`${type.charAt(0).toUpperCase() + type.slice(1)} created successfully!`);
        modal.remove();

        // Refresh search results if search was performed
        const searchInput = document.getElementById('content-search');
        if (searchInput && searchInput.value.trim()) {
          this.searchContent();
        }
      } else {
        throw new Error(result.message || 'Failed to create content');
      }

    } catch (error) {
      console.error('Error saving content:', error);
      alert('Error saving content: ' + error.message);

      // Reset button state
      const saveBtn = modal.querySelector('.save-btn');
      saveBtn.disabled = false;
      saveBtn.innerHTML = `Save ${type.charAt(0).toUpperCase() + type.slice(1)}`;
    }
  }

  /**
   * Extract form data
   */
  extractFormData(modal, type) {
    const data = {
      title: modal.querySelector('#add-title').value.trim(),
      description: modal.querySelector('#add-description').value.trim(),
      thumbnail: modal.querySelector('#add-thumbnail').value.trim(),
      type: type.toUpperCase()
    };

    // Add type-specific fields
    const yearInput = modal.querySelector('#add-year');
    if (yearInput) data.year = parseInt(yearInput.value) || null;

    const ratingInput = modal.querySelector('#add-rating');
    if (ratingInput) data.rating = parseFloat(ratingInput.value) || null;

    const genreInput = modal.querySelector('#add-genre');
    if (genreInput) data.genre = genreInput.value.trim();

    // Type-specific fields
    switch (type) {
      case 'movie':
        const durationInput = modal.querySelector('#add-duration');
        const streamingUrlInput = modal.querySelector('#add-streaming-url');
        if (durationInput) data.duration = parseInt(durationInput.value) || null;
        if (streamingUrlInput) data.streamingUrl = streamingUrlInput.value.trim();
        break;

      case 'series':
        const seasonsInput = modal.querySelector('#add-seasons');
        const episodesInput = modal.querySelector('#add-episodes');
        const statusInput = modal.querySelector('#add-status');
        if (seasonsInput) data.seasons = parseInt(seasonsInput.value) || null;
        if (episodesInput) data.episodes = parseInt(episodesInput.value) || null;
        if (statusInput) data.status = statusInput.value;
        break;

      case 'anime':
        const animeEpisodesInput = modal.querySelector('#add-episodes');
        const studioInput = modal.querySelector('#add-studio');
        const animeStatusInput = modal.querySelector('#add-status');
        if (animeEpisodesInput) data.episodes = parseInt(animeEpisodesInput.value) || null;
        if (studioInput) data.studio = studioInput.value.trim();
        if (animeStatusInput) data.status = animeStatusInput.value;
        break;

      case 'livetv':
        const categoryInput = modal.querySelector('#add-category');
        const languageInput = modal.querySelector('#add-language');
        const countryInput = modal.querySelector('#add-country');
        const liveStreamingUrlInput = modal.querySelector('#add-streaming-url');
        const qualityInput = modal.querySelector('#add-quality');
        if (categoryInput) data.category = categoryInput.value.trim();
        if (languageInput) data.language = languageInput.value.trim();
        if (countryInput) data.country = countryInput.value.trim();
        if (liveStreamingUrlInput) data.streamingUrl = liveStreamingUrlInput.value.trim();
        if (qualityInput) data.quality = qualityInput.value;
        break;
    }

    return data;
  }

  /**
   * Validate form data
   */
  validateFormData(data, type) {
    if (!data.title) {
      alert('Title is required');
      return false;
    }

    if ((type === 'movie' || type === 'livetv') && !data.streamingUrl) {
      alert('Streaming URL is required');
      return false;
    }

    return true;
  }

  /**
   * Create content via GraphQL
   */
  async createContent(data, type) {
    // For now, return a mock success response
    // This would be replaced with actual GraphQL mutations
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ success: true, message: `${type} created successfully` });
      }, 1000);
    });
  }

  /**
   * Edit content
   */
  async editContent(id, type) {
    console.log(`Opening edit modal for ${type} ID: ${id}`);

    try {
      // First, fetch the current content data
      const contentData = await this.fetchContentById(id, type);

      if (!contentData) {
        alert(`Failed to fetch ${type} data for editing.`);
        return;
      }

      // Create and show edit modal
      const modal = this.createEditContentModal(contentData, type);
      document.body.appendChild(modal);
      modal.style.display = 'block';

      // Setup modal event listeners
      this.setupEditContentModalListeners(modal, id, type);

    } catch (error) {
      console.error('Error opening edit modal:', error);
      alert(`Error opening edit modal: ${error.message}`);
    }
  }

  /**
   * Fetch content by ID directly from database
   */
  async fetchContentById(id, type) {
    try {
      console.log('Fetching content by ID from database:', { id, type });

      // Use a direct database fetch API endpoint
      const response = await fetch(`/api/admin/content/${id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken') || 'admin'}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.content) {
          console.log('Successfully fetched content from database:', result.content);
          // Return the content with proper structure
          return result.content;
        } else {
          console.warn('Database API returned no content, falling back to GraphQL search');
        }
      } else {
        console.warn('Database API not available, falling back to GraphQL search');
      }

    } catch (error) {
      console.warn('Database fetch failed, falling back to GraphQL search:', error);
    }

    // Fallback to GraphQL search with comprehensive data fetching
    try {
      console.log('Using GraphQL search fallback for ID:', id);

      const searchQuery = `
        query SearchAllContent($query: String!) {
          search(query: $query, limit: 200) {
            items {
              ... on Movie {
                id
                title
                thumbnail
                image
                detailUrl
                detailUrlPath
                streamingUrls {
                  id
                  url
                  provider
                  language
                  type
                  method
                }
                metadata {
                  synopsis
                  year
                  genre
                  duration
                  creator
                  actors
                  origin
                }
                tmdb {
                  id
                  title
                  overview
                  release_date
                  vote_average
                  vote_count
                  genres
                  poster_path
                  cast {
                    id
                    name
                    character
                  }
                  crew {
                    id
                    name
                    job
                  }
                }
              }
              ... on Series {
                id
                title
                thumbnail
                image
                detailUrl
                detailUrlPath
                season
                episodes {
                  episodeNumber
                  season
                  language
                  streamingUrls {
                    id
                    url
                    provider
                    language
                    type
                    method
                  }
                }
                metadata {
                  synopsis
                  year
                  genre
                  creator
                  actors
                  origin
                  duration
                }
                tmdb {
                  id
                  title
                  overview
                  release_date
                  vote_average
                  vote_count
                  genres
                  poster_path
                  cast {
                    id
                    name
                    character
                  }
                  crew {
                    id
                    name
                    job
                  }
                }
              }
              ... on Anime {
                id
                title
                thumbnail
                image
                detailUrl
                detailUrlPath
                season
                animeLanguage
                episodes {
                  episodeNumber
                  season
                  language
                  streamingUrls {
                    id
                    url
                    provider
                    language
                    type
                    method
                  }
                }
                streamingUrls {
                  id
                  url
                  provider
                  language
                  type
                  method
                }
                metadata {
                  synopsis
                  year
                  genre
                  creator
                  actors
                  origin
                  duration
                }
                jikan {
                  mal_id
                  title {
                    default
                    english
                    japanese
                    synonyms
                  }
                  synopsis
                  score
                  scored_by
                  rank
                  popularity
                  episodes
                  status
                  aired {
                    from
                    to
                    string
                  }
                  season
                  year
                  genres {
                    mal_id
                    name
                    type
                  }
                  studios {
                    mal_id
                    name
                  }
                  source
                  rating
                  duration
                  type
                }
              }
              ... on LiveTV {
                id
                title
                thumbnail
                image
                detailUrl
                detailUrlPath
                streamingUrls {
                  id
                  url
                  provider
                  language
                  type
                  method
                }
                metadata {
                  synopsis
                  genre
                  creator
                  actors
                  origin
                  duration
                }
              }
            }
          }
        }
      `;

      // Try multiple search strategies to find the content
      const searchStrategies = [
        '*',           // Search for everything first (most likely to work)
        'a',           // Search for common letter
        'the',         // Search for common word
        id,            // Search by ID directly
        id.slice(-8),  // Search by last 8 characters of ID
        id.slice(-12)  // Search by last 12 characters of ID
      ];

      let foundItem = null;

      for (const searchTerm of searchStrategies) {
        console.log(`Trying search strategy: "${searchTerm}"`);

        try {
          const response = await fetch('/graphql', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              query: searchQuery,
              variables: { query: searchTerm }
            })
          });

          const result = await response.json();

          if (result.errors) {
            console.warn(`Search strategy "${searchTerm}" failed:`, result.errors[0].message);
            continue;
          }

          const items = result.data.search?.items || [];
          console.log(`Search strategy "${searchTerm}" returned ${items.length} items`);

          // Look for exact ID match
          foundItem = items.find(item => item.id === id);

          if (foundItem) {
            console.log(`Found exact match with strategy "${searchTerm}":`, foundItem);
            break;
          }

          // If we got items but no exact match, log them for debugging
          if (items.length > 0 && searchTerm === '*') {
            console.log('Available items (first 10):', items.slice(0, 10).map(item => ({
              id: item.id,
              title: item.title,
              __typename: item.__typename
            })));
          }

        } catch (searchError) {
          console.warn(`Search strategy "${searchTerm}" error:`, searchError);
          continue;
        }
      }

      if (!foundItem) {
        console.warn(`Content with ID ${id} not found after all search strategies`);

        // Try to get the item data from the current search results if available
        const currentSearchResults = document.querySelectorAll('.grid-item');
        for (const gridItem of currentSearchResults) {
          if (gridItem.dataset.id === id) {
            const title = gridItem.querySelector('.grid-item-title')?.textContent || 'Unknown Title';
            const thumbnail = gridItem.querySelector('img')?.src || '';
            const itemType = gridItem.dataset.type || type;

            console.log('Found item data from current search results:', { id, title, thumbnail, itemType });

            return {
              id: id,
              title: title,
              thumbnail: thumbnail,
              imagePath: thumbnail,
              detailUrl: '',
              __typename: itemType,
              metadata: {
                synopsis: '',
                year: '',
                genre: ''
              }
            };
          }
        }

        // Final fallback - create a basic item with the ID and type
        return {
          id: id,
          title: `${type} Content`,
          thumbnail: '',
          imagePath: '',
          detailUrl: '',
          __typename: type,
          metadata: {
            synopsis: '',
            year: '',
            genre: ''
          }
        };
      }

      console.log('Successfully found content via GraphQL:', foundItem);
      return foundItem;

    } catch (error) {
      console.error('Error fetching content:', error);

      // Return a basic item to allow editing
      return {
        id: id,
        title: `${type} Content`,
        thumbnail: '',
        imagePath: '',
        detailUrl: '',
        __typename: type,
        metadata: {
          synopsis: '',
          year: '',
          genre: ''
        }
      };
    }
  }

  /**
   * Create edit content modal
   */
  createEditContentModal(contentData, type) {
    const modal = document.createElement('div');
    modal.className = 'modal edit-content-modal';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h2><i class="fas fa-edit"></i> Edit ${type.charAt(0).toUpperCase() + type.slice(1)}</h2>
          <span class="close">&times;</span>
        </div>
        <div class="modal-body">
          ${this.getEditContentForm(contentData, type)}
        </div>
        <div class="modal-footer">
          <button class="button secondary cancel-btn">Cancel</button>
          <button class="button primary save-btn">Save Changes</button>
        </div>
      </div>
    `;
    return modal;
  }

  /**
   * Get edit content form based on type and data
   */
  getEditContentForm(contentData, type) {
    console.log('Creating edit form for:', { contentData, type });

    // Extract data with better fallbacks using actual GraphQL schema
    const title = contentData.title || 'Unknown Title';
    const thumbnail = contentData.thumbnail || contentData.image || '';
    const detailUrl = contentData.detailUrl || contentData.detailUrlPath || '';

    // Extract metadata fields (using actual schema fields)
    const synopsis = contentData.metadata?.synopsis || contentData.tmdb?.overview || contentData.jikan?.synopsis || '';
    const year = contentData.metadata?.year ||
                 (contentData.tmdb?.release_date ? contentData.tmdb.release_date.split('-')[0] : '') ||
                 contentData.jikan?.year || '';
    const genre = contentData.metadata?.genre ||
                  (contentData.tmdb?.genres ? contentData.tmdb.genres.join(', ') : '') ||
                  (contentData.jikan?.genres ? contentData.jikan.genres.map(g => g.name).join(', ') : '') || '';

    // Extract streaming URLs (using actual schema fields)
    const streamingUrls = contentData.streamingUrls || [];
    const streamingUrlsText = streamingUrls.map(url => `${url.type || 'Unknown'}: ${url.url || ''} (${url.provider || 'Unknown Provider'})`).join('\n');

    const commonFields = `
      <div class="form-group">
        <label for="edit-title">Title *</label>
        <input type="text" id="edit-title" value="${title.replace(/"/g, '&quot;')}" required>
      </div>
      <div class="form-group">
        <label for="edit-synopsis">Synopsis</label>
        <textarea id="edit-synopsis" rows="4">${synopsis.replace(/"/g, '&quot;')}</textarea>
      </div>
      <div class="form-group">
        <label for="edit-thumbnail">Thumbnail URL</label>
        <input type="url" id="edit-thumbnail" value="${thumbnail.replace(/"/g, '&quot;')}">
      </div>
      <div class="form-group">
        <label for="edit-detail-url">Detail URL</label>
        <input type="url" id="edit-detail-url" value="${detailUrl.replace(/"/g, '&quot;')}">
      </div>
      <div class="form-group">
        <label for="edit-year">Year</label>
        <input type="number" id="edit-year" value="${year}" min="1900" max="2030">
      </div>
      <div class="form-group">
        <label for="edit-genre">Genre</label>
        <input type="text" id="edit-genre" value="${genre.replace(/"/g, '&quot;')}">
      </div>
      <div class="form-group">
        <label for="edit-streaming-urls">Streaming URLs (Quality: URL format)</label>
        <textarea id="edit-streaming-urls" rows="3" placeholder="HD: https://example.com/video.m3u8">${streamingUrlsText}</textarea>
      </div>
      <div class="form-group">
        <label for="edit-id">Content ID (Read-only)</label>
        <input type="text" id="edit-id" value="${contentData.id}" readonly style="background: #2a2a4a; color: #888;">
      </div>
    `;

    switch (type.toLowerCase()) {
      case 'movie':
        const duration = contentData.metadata?.duration || '';
        const creator = contentData.metadata?.creator || '';
        const actors = contentData.metadata?.actors ? contentData.metadata.actors.join(', ') : '';
        const origin = contentData.metadata?.origin || '';
        const rating = contentData.tmdb?.vote_average || '';
        const tmdbId = contentData.tmdb?.id || '';
        const voteCount = contentData.tmdb?.vote_count || '';
        const posterPath = contentData.tmdb?.poster_path || '';
        const tmdbCast = contentData.tmdb?.cast ? contentData.tmdb.cast.map(c => `${c.name} (${c.character})`).join(', ') : '';
        const tmdbCrew = contentData.tmdb?.crew ? contentData.tmdb.crew.filter(c => c.job === 'Director').map(c => c.name).join(', ') : '';
        return `
          ${commonFields}
          <div class="form-group">
            <label for="edit-duration">Duration</label>
            <input type="text" id="edit-duration" value="${duration}" placeholder="e.g., 120 min">
          </div>
          <div class="form-group">
            <label for="edit-creator">Creator/Director</label>
            <input type="text" id="edit-creator" value="${creator.replace(/"/g, '&quot;')}">
          </div>
          <div class="form-group">
            <label for="edit-actors">Actors</label>
            <input type="text" id="edit-actors" value="${actors.replace(/"/g, '&quot;')}">
          </div>
          <div class="form-group">
            <label for="edit-origin">Origin/Country</label>
            <input type="text" id="edit-origin" value="${origin.replace(/"/g, '&quot;')}">
          </div>
          <div class="form-group">
            <label for="edit-rating">TMDB Rating</label>
            <input type="number" id="edit-rating" value="${rating}" min="0" max="10" step="0.1">
          </div>
          <div class="form-group">
            <label for="edit-tmdb-id">TMDB ID</label>
            <input type="number" id="edit-tmdb-id" value="${tmdbId}">
          </div>
          <div class="form-group">
            <label for="edit-vote-count">Vote Count</label>
            <input type="number" id="edit-vote-count" value="${voteCount}">
          </div>
          <div class="form-group">
            <label for="edit-poster-path">TMDB Poster Path</label>
            <input type="text" id="edit-poster-path" value="${posterPath.replace(/"/g, '&quot;')}">
          </div>
          <div class="form-group">
            <label for="edit-tmdb-cast">TMDB Cast</label>
            <textarea id="edit-tmdb-cast" rows="3" readonly style="background: #2a2a4a; color: #888;">${tmdbCast}</textarea>
          </div>
          <div class="form-group">
            <label for="edit-tmdb-crew">TMDB Directors</label>
            <textarea id="edit-tmdb-crew" rows="2" readonly style="background: #2a2a4a; color: #888;">${tmdbCrew}</textarea>
          </div>
        `;

      case 'series':
        const season = contentData.season || '';
        const seriesCreator = contentData.metadata?.creator || '';
        const seriesActors = contentData.metadata?.actors ? contentData.metadata.actors.join(', ') : '';
        const seriesOrigin = contentData.metadata?.origin || '';
        const seriesDuration = contentData.metadata?.duration || '';
        const seriesRating = contentData.tmdb?.vote_average || '';
        const seriesTmdbId = contentData.tmdb?.id || '';
        const seriesVoteCount = contentData.tmdb?.vote_count || '';
        const seriesPosterPath = contentData.tmdb?.poster_path || '';
        const episodes = contentData.episodes || [];
        const episodesText = episodes.map(ep => `${ep.episodeNumber || ''} (Season ${ep.season || ''}): ${ep.language || 'Unknown'}`).join('\n');
        const seriesTmdbCast = contentData.tmdb?.cast ? contentData.tmdb.cast.map(c => `${c.name} (${c.character})`).join(', ') : '';
        return `
          ${commonFields}
          <div class="form-group">
            <label for="edit-season">Current Season</label>
            <input type="text" id="edit-season" value="${season}">
          </div>
          <div class="form-group">
            <label for="edit-creator">Creator</label>
            <input type="text" id="edit-creator" value="${seriesCreator.replace(/"/g, '&quot;')}">
          </div>
          <div class="form-group">
            <label for="edit-actors">Actors</label>
            <input type="text" id="edit-actors" value="${seriesActors.replace(/"/g, '&quot;')}">
          </div>
          <div class="form-group">
            <label for="edit-origin">Origin/Country</label>
            <input type="text" id="edit-origin" value="${seriesOrigin.replace(/"/g, '&quot;')}">
          </div>
          <div class="form-group">
            <label for="edit-duration">Episode Duration</label>
            <input type="text" id="edit-duration" value="${seriesDuration}" placeholder="e.g., 45 min">
          </div>
          <div class="form-group">
            <label for="edit-rating">TMDB Rating</label>
            <input type="number" id="edit-rating" value="${seriesRating}" min="0" max="10" step="0.1">
          </div>
          <div class="form-group">
            <label for="edit-tmdb-id">TMDB ID</label>
            <input type="number" id="edit-tmdb-id" value="${seriesTmdbId}">
          </div>
          <div class="form-group">
            <label for="edit-vote-count">Vote Count</label>
            <input type="number" id="edit-vote-count" value="${seriesVoteCount}">
          </div>
          <div class="form-group">
            <label for="edit-poster-path">TMDB Poster Path</label>
            <input type="text" id="edit-poster-path" value="${seriesPosterPath.replace(/"/g, '&quot;')}">
          </div>
          <div class="form-group">
            <label for="edit-episodes">Episodes (Episode Number (Season): Language)</label>
            <textarea id="edit-episodes" rows="5" readonly style="background: #2a2a4a; color: #888;">${episodesText}</textarea>
          </div>
          <div class="form-group">
            <label for="edit-tmdb-cast">TMDB Cast</label>
            <textarea id="edit-tmdb-cast" rows="3" readonly style="background: #2a2a4a; color: #888;">${seriesTmdbCast}</textarea>
          </div>
        `;

      case 'anime':
        const animeSeason = contentData.season || '';
        const animeLanguage = contentData.animeLanguage || '';
        const animeScore = contentData.jikan?.score || '';
        const animeEpisodes = contentData.jikan?.episodes || '';
        const animeStatus = contentData.jikan?.status || '';
        const malId = contentData.jikan?.mal_id || '';
        const studio = contentData.metadata?.studio || '';
        const source = contentData.metadata?.source || contentData.jikan?.source || '';
        const animeEpisodesData = contentData.episodes || [];
        const animeEpisodesText = animeEpisodesData.map(ep => `${ep.number || ''}: ${ep.title || ''} - ${ep.url || ''}`).join('\n');
        return `
          ${commonFields}
          <div class="form-group">
            <label for="edit-season">Season</label>
            <input type="text" id="edit-season" value="${animeSeason}">
          </div>
          <div class="form-group">
            <label for="edit-language">Language</label>
            <select id="edit-language">
              <option value="VF" ${animeLanguage === 'VF' ? 'selected' : ''}>VF (French Dub)</option>
              <option value="VOSTFR" ${animeLanguage === 'VOSTFR' ? 'selected' : ''}>VOSTFR (French Sub)</option>
              <option value="unknown" ${animeLanguage === 'unknown' ? 'selected' : ''}>Unknown</option>
            </select>
          </div>
          <div class="form-group">
            <label for="edit-episodes-count">Episodes Count</label>
            <input type="number" id="edit-episodes-count" value="${animeEpisodes}" min="1">
          </div>
          <div class="form-group">
            <label for="edit-score">MAL Score</label>
            <input type="number" id="edit-score" value="${animeScore}" min="0" max="10" step="0.1">
          </div>
          <div class="form-group">
            <label for="edit-status">Status</label>
            <input type="text" id="edit-status" value="${animeStatus}" placeholder="Airing, Finished, etc.">
          </div>
          <div class="form-group">
            <label for="edit-mal-id">MyAnimeList ID</label>
            <input type="number" id="edit-mal-id" value="${malId}">
          </div>
          <div class="form-group">
            <label for="edit-studio">Studio</label>
            <input type="text" id="edit-studio" value="${studio.replace(/"/g, '&quot;')}">
          </div>
          <div class="form-group">
            <label for="edit-source">Source</label>
            <input type="text" id="edit-source" value="${source.replace(/"/g, '&quot;')}" placeholder="Manga, Light Novel, etc.">
          </div>
          <div class="form-group">
            <label for="edit-episodes">Episodes (Number: Title - URL format)</label>
            <textarea id="edit-episodes" rows="5" placeholder="1: Episode Title - https://example.com/episode1.m3u8">${animeEpisodesText}</textarea>
          </div>
        `;

      case 'livetv':
        const category = contentData.category || '';
        const country = contentData.country || '';
        const language = contentData.language || '';
        const streamUrl = contentData.streamUrl || '';
        const schedule = contentData.metadata?.schedule || '';
        const description = contentData.metadata?.description || '';
        return `
          ${commonFields}
          <div class="form-group">
            <label for="edit-category">Category</label>
            <input type="text" id="edit-category" value="${category.replace(/"/g, '&quot;')}" placeholder="News, Sports, Entertainment">
          </div>
          <div class="form-group">
            <label for="edit-country">Country</label>
            <input type="text" id="edit-country" value="${country.replace(/"/g, '&quot;')}">
          </div>
          <div class="form-group">
            <label for="edit-language">Language</label>
            <input type="text" id="edit-language" value="${language.replace(/"/g, '&quot;')}">
          </div>
          <div class="form-group">
            <label for="edit-stream-url">Stream URL</label>
            <input type="url" id="edit-stream-url" value="${streamUrl.replace(/"/g, '&quot;')}">
          </div>
          <div class="form-group">
            <label for="edit-schedule">Schedule</label>
            <input type="text" id="edit-schedule" value="${schedule.replace(/"/g, '&quot;')}" placeholder="24/7, Prime Time, etc.">
          </div>
          <div class="form-group">
            <label for="edit-description">Description</label>
            <textarea id="edit-description" rows="3">${description.replace(/"/g, '&quot;')}</textarea>
          </div>
        `;

      default:
        return commonFields;
    }
  }

  /**
   * Setup edit content modal listeners
   */
  setupEditContentModalListeners(modal, id, type) {
    // Close modal
    modal.querySelector('.close').addEventListener('click', () => {
      modal.remove();
    });

    modal.querySelector('.cancel-btn').addEventListener('click', () => {
      modal.remove();
    });

    // Save changes
    modal.querySelector('.save-btn').addEventListener('click', () => {
      this.saveContentChanges(modal, id, type);
    });

    // Close on outside click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });
  }

  /**
   * Save content changes
   */
  async saveContentChanges(modal, id, type) {
    try {
      const formData = this.extractEditFormData(modal, type);

      if (!this.validateEditFormData(formData, type)) {
        return;
      }

      // Show loading state
      const saveBtn = modal.querySelector('.save-btn');
      saveBtn.disabled = true;
      saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';

      // Update the content via GraphQL mutation (placeholder for now)
      const result = await this.updateContent(id, formData, type);

      if (result.success) {
        alert(`${type.charAt(0).toUpperCase() + type.slice(1)} updated successfully!`);
        modal.remove();

        // Refresh search results if search was performed
        const searchInput = document.getElementById('content-search');
        if (searchInput && searchInput.value.trim()) {
          this.searchContent();
        }
      } else {
        throw new Error(result.message || 'Failed to update content');
      }

    } catch (error) {
      console.error('Error saving changes:', error);
      alert('Error saving changes: ' + error.message);

      // Reset button state
      const saveBtn = modal.querySelector('.save-btn');
      saveBtn.disabled = false;
      saveBtn.innerHTML = 'Save Changes';
    }
  }

  /**
   * Extract edit form data
   */
  extractEditFormData(modal, type) {
    const data = {
      title: modal.querySelector('#edit-title').value.trim(),
      synopsis: modal.querySelector('#edit-synopsis').value.trim(),
      thumbnail: modal.querySelector('#edit-thumbnail').value.trim(),
      year: modal.querySelector('#edit-year').value.trim(),
      genre: modal.querySelector('#edit-genre').value.trim()
    };

    // Add type-specific fields
    switch (type.toLowerCase()) {
      case 'movie':
        const durationInput = modal.querySelector('#edit-duration');
        const ratingInput = modal.querySelector('#edit-rating');
        if (durationInput) data.duration = durationInput.value.trim();
        if (ratingInput) data.rating = parseFloat(ratingInput.value) || null;
        break;

      case 'series':
        const seasonInput = modal.querySelector('#edit-season');
        const seriesRatingInput = modal.querySelector('#edit-rating');
        if (seasonInput) data.season = seasonInput.value.trim();
        if (seriesRatingInput) data.rating = parseFloat(seriesRatingInput.value) || null;
        break;

      case 'anime':
        const animeSeasonInput = modal.querySelector('#edit-season');
        const languageInput = modal.querySelector('#edit-language');
        const episodesInput = modal.querySelector('#edit-episodes');
        const scoreInput = modal.querySelector('#edit-score');
        const statusInput = modal.querySelector('#edit-status');
        if (animeSeasonInput) data.season = animeSeasonInput.value.trim();
        if (languageInput) data.language = languageInput.value;
        if (episodesInput) data.episodes = parseInt(episodesInput.value) || null;
        if (scoreInput) data.score = parseFloat(scoreInput.value) || null;
        if (statusInput) data.status = statusInput.value.trim();
        break;

      case 'livetv':
        const categoryInput = modal.querySelector('#edit-category');
        if (categoryInput) data.category = categoryInput.value.trim();
        break;
    }

    return data;
  }

  /**
   * Validate edit form data
   */
  validateEditFormData(data, type) {
    if (!data.title) {
      alert('Title is required');
      return false;
    }
    return true;
  }

  /**
   * Update content via GraphQL (placeholder)
   */
  async updateContent(id, data, type) {
    // For now, return a mock success response
    // This would be replaced with actual GraphQL mutations
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ success: true, message: `${type} updated successfully` });
      }, 1000);
    });
  }

  /**
   * View content details
   */
  viewContent(id, type) {
    console.log('Opening content view for:', { id, type });

    // Validate inputs
    if (!id || !type) {
      alert('Error: Invalid content ID or type');
      return;
    }

    // Generate proper URL based on content type
    const baseUrl = window.location.origin;
    let detailUrl = '';

    switch (type.toLowerCase()) {
      case 'movie':
        detailUrl = `${baseUrl}/movies/${id}`;
        break;
      case 'series':
        detailUrl = `${baseUrl}/series/${id}`;
        break;
      case 'anime':
        detailUrl = `${baseUrl}/anime/${id}`;
        break;
      case 'livetv':
        detailUrl = `${baseUrl}/livetv/${id}`;
        break;
      default:
        // Fallback to media.html for unknown types
        detailUrl = `${baseUrl}/media.html?id=${id}&type=${type.toUpperCase()}`;
    }

    console.log('Opening URL:', detailUrl);
    window.open(detailUrl, '_blank');
  }

  /**
   * Delete content
   */
  async deleteContent(id, type) {
    const confirmed = confirm(`Are you sure you want to delete this ${type}? This action cannot be undone.`);
    if (!confirmed) return;

    try {
      // This would call the delete mutation
      alert(`Delete ${type} functionality will be implemented soon. Would delete ${type} ID: ${id}.`);

      // Refresh search results
      this.searchContent();
    } catch (error) {
      console.error('Error deleting content:', error);
      alert('Error deleting content: ' + error.message);
    }
  }

  /**
   * Bulk update metadata
   */
  async bulkUpdateMetadata() {
    const confirmed = confirm('Are you sure you want to update metadata for all content? This may take a long time.');
    if (!confirmed) return;

    try {
      alert('Bulk metadata update started. This will run in the background and may take several hours.');
      // This would call a bulk update endpoint
    } catch (error) {
      console.error('Error starting bulk update:', error);
      alert('Error starting bulk update: ' + error.message);
    }
  }

  /**
   * Bulk clean duplicates
   */
  async bulkCleanDuplicates() {
    const confirmed = confirm('Are you sure you want to remove duplicate content? This action cannot be undone.');
    if (!confirmed) return;

    try {
      alert('Duplicate cleanup started. This will run in the background.');
      // This would call a duplicate cleanup endpoint
    } catch (error) {
      console.error('Error starting duplicate cleanup:', error);
      alert('Error starting duplicate cleanup: ' + error.message);
    }
  }

  /**
   * Bulk delete old content
   */
  async bulkDeleteOld() {
    const confirmed = confirm('Are you sure you want to delete old content? This action cannot be undone.');
    if (!confirmed) return;

    try {
      alert('Old content deletion started. This will run in the background.');
      // This would call a bulk delete endpoint
    } catch (error) {
      console.error('Error starting bulk delete:', error);
      alert('Error starting bulk delete: ' + error.message);
    }
  }

  /**
   * Load config data
   */
  async loadConfigData() {
    try {
      // Config data is loaded when the panel is shown
      await this.fetchConfigValues();

      // Initialize display settings
      await this.initializeDisplaySettings();

      // Setup config event listeners
      this.setupConfigEventListeners();
    } catch (error) {
      console.error('Error loading config data:', error);
    }
  }

  /**
   * Setup config event listeners
   */
  setupConfigEventListeners() {
    // Grid items toggle
    const gridToggle = document.getElementById('grid-items-toggle');
    if (gridToggle) {
      // Remove existing listeners to avoid duplicates
      gridToggle.removeEventListener('change', this.handleGridToggle);

      // Add new listener
      this.handleGridToggle = (e) => {
        this.toggleGridItems(e.target.checked);
      };
      gridToggle.addEventListener('change', this.handleGridToggle);
    }

    // Base URL update buttons
    const updateWiflixBtn = document.getElementById('update-wiflix');
    if (updateWiflixBtn) {
      updateWiflixBtn.addEventListener('click', () => {
        this.updateBaseUrl('WIFLIX_BASE', document.getElementById('wiflix-base').value);
      });
    }

    const updateFrenchAnimeBtn = document.getElementById('update-french-anime');
    if (updateFrenchAnimeBtn) {
      updateFrenchAnimeBtn.addEventListener('click', () => {
        this.updateBaseUrl('FRENCH_ANIME_BASE', document.getElementById('french-anime-base').value);
      });
    }

    const updateWitvBtn = document.getElementById('update-witv');
    if (updateWitvBtn) {
      updateWitvBtn.addEventListener('click', () => {
        this.updateBaseUrl('WITV_BASE', document.getElementById('witv-base').value);
      });
    }

    // Manual scrape button
    const manualScrapeBtn = document.getElementById('start-manual-scrape');
    if (manualScrapeBtn) {
      manualScrapeBtn.addEventListener('click', () => {
        this.scrapeUrlManually();
      });
    }

    console.log('Config event listeners setup complete');
  }

  /**
   * Load logs data
   */
  async loadLogsData() {
    try {
      // Show placeholder logs
      const logsDisplay = document.getElementById('logs-display');
      if (logsDisplay) {
        logsDisplay.innerHTML = `
          <div class="log-entry info">
            <span>[${new Date().toISOString()}] INFO: Admin panel loaded</span>
          </div>
          <div class="log-entry success">
            <span>[${new Date().toISOString()}] SUCCESS: System healthy</span>
          </div>
        `;
      }
    } catch (error) {
      console.error('Error loading logs data:', error);
    }
  }

  /**
   * Update performance card
   */
  updatePerformanceCard(cardId, content) {
    const card = document.getElementById(cardId);
    if (card) {
      card.innerHTML = content;
    }
  }

  /**
   * Update system card
   */
  updateSystemCard(cardId, content) {
    const card = document.getElementById(cardId);
    if (card) {
      card.innerHTML = content;
    }
  }

  /**
   * Format memory stats
   */
  formatMemoryStats(memory) {
    if (!memory) return '<div class="stat-item"><span class="stat-label">No data available</span></div>';

    const heapUsedMB = Math.round(memory.heapUsed / 1024 / 1024);
    const heapTotalMB = Math.round(memory.heapTotal / 1024 / 1024);
    const usage = Math.round((memory.heapUsed / memory.heapTotal) * 100);

    return `
      <div class="stat-item">
        <span class="stat-label">Heap Used</span>
        <span class="stat-value">${heapUsedMB} MB</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Heap Total</span>
        <span class="stat-value">${heapTotalMB} MB</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Usage</span>
        <span class="stat-value ${usage > 80 ? 'warning' : 'success'}">${usage}%</span>
      </div>
    `;
  }

  /**
   * Format cache stats
   */
  formatCacheStats(cache) {
    if (!cache) return '<div class="stat-item"><span class="stat-label">No data available</span></div>';

    return `
      <div class="stat-item">
        <span class="stat-label">Hit Rate</span>
        <span class="stat-value success">${cache.hitRate || 0}%</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Total Items</span>
        <span class="stat-value">${cache.totalItems || 0}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Memory Usage</span>
        <span class="stat-value">${cache.memoryUsage || 0} MB</span>
      </div>
    `;
  }

  /**
   * Format rate limiter stats
   */
  formatRateLimiterStats(rateLimiters) {
    if (!rateLimiters) return '<div class="stat-item"><span class="stat-label">No data available</span></div>';

    const limiters = Object.keys(rateLimiters);
    if (limiters.length === 0) {
      return '<div class="stat-item"><span class="stat-label">No rate limiters active</span></div>';
    }

    return limiters.slice(0, 3).map(name => {
      const limiter = rateLimiters[name];
      const remaining = limiter.remaining || 0;
      const status = remaining > 10 ? 'success' : remaining > 5 ? 'warning' : 'error';

      return `
        <div class="stat-item">
          <span class="stat-label">${name}</span>
          <span class="stat-value ${status}">${remaining} remaining</span>
        </div>
      `;
    }).join('');
  }

  /**
   * Format database performance
   */
  formatDbPerformance(database) {
    if (!database) {
      return `
        <div class="stat-item">
          <span class="stat-label">Status</span>
          <span class="stat-value error">Unknown</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Response Time</span>
          <span class="stat-value">N/A</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Connections</span>
          <span class="stat-value">N/A</span>
        </div>
      `;
    }

    const status = database.status === 'connected' ? 'success' : 'error';
    const responseTime = database.responseTime || 0;
    const responseStatus = responseTime < 50 ? 'success' : responseTime < 100 ? 'warning' : 'error';
    const connections = database.connections || 0;

    return `
      <div class="stat-item">
        <span class="stat-label">Status</span>
        <span class="stat-value ${status}">${database.status || 'Unknown'}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Response Time</span>
        <span class="stat-value ${responseStatus}">${responseTime}ms</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Connections</span>
        <span class="stat-value">${connections}</span>
      </div>
    `;
  }

  /**
   * Format server resources
   */
  formatServerResources(data) {
    if (!data) {
      return `
        <div class="stat-item">
          <span class="stat-label">Performance API</span>
          <span class="stat-value error">Unavailable</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Status</span>
          <span class="stat-value error">Cannot fetch server data</span>
        </div>
      `;
    }

    const memoryUsage = data.memory ? Math.round((data.memory.heapUsed / data.memory.heapTotal) * 100) : 0;

    return `
      <div class="stat-item">
        <span class="stat-label">CPU Usage</span>
        <span class="stat-value success">Normal</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Memory</span>
        <span class="stat-value ${memoryUsage > 80 ? 'warning' : 'success'}">${memoryUsage}%</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Uptime</span>
        <span class="stat-value">${Math.round((data.uptime || 0) / 3600)}h</span>
      </div>
    `;
  }

  /**
   * Format storage usage
   */
  formatStorageUsage() {
    return `
      <div class="stat-item">
        <span class="stat-label">Database</span>
        <span class="stat-value">Unknown</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Logs</span>
        <span class="stat-value">Unknown</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Cache</span>
        <span class="stat-value">Unknown</span>
      </div>
    `;
  }

  /**
   * Format server resources - compact
   */
  formatServerResourcesCompact(data) {
    if (!data) {
      return `
        <div class="compact-stat">
          <div class="compact-stat-value">N/A</div>
          <div class="compact-stat-label">CPU Usage</div>
        </div>
        <div class="compact-stat">
          <div class="compact-stat-value">N/A</div>
          <div class="compact-stat-label">Memory</div>
        </div>
        <div class="compact-stat">
          <div class="compact-stat-value">N/A</div>
          <div class="compact-stat-label">Uptime</div>
        </div>
        <div class="compact-stat">
          <div class="compact-stat-value">N/A</div>
          <div class="compact-stat-label">Load</div>
        </div>
      `;
    }

    const memoryUsage = data.memory ? Math.round((data.memory.heapUsed / data.memory.heapTotal) * 100) : 0;
    const uptimeHours = Math.round((data.uptime || 0) / 3600);

    return `
      <div class="compact-stat">
        <div class="compact-stat-value success">Normal</div>
        <div class="compact-stat-label">CPU Usage</div>
      </div>
      <div class="compact-stat">
        <div class="compact-stat-value ${memoryUsage > 80 ? 'warning' : 'success'}">${memoryUsage}%</div>
        <div class="compact-stat-label">Memory</div>
      </div>
      <div class="compact-stat">
        <div class="compact-stat-value">${uptimeHours}h</div>
        <div class="compact-stat-label">Uptime</div>
      </div>
      <div class="compact-stat">
        <div class="compact-stat-value success">Low</div>
        <div class="compact-stat-label">Load</div>
      </div>
    `;
  }

  /**
   * Format storage usage - compact
   */
  formatStorageUsageCompact(data) {
    if (!data) {
      return `
        <div class="compact-stat">
          <div class="compact-stat-value warning">Unknown</div>
          <div class="compact-stat-label">Database</div>
        </div>
        <div class="compact-stat">
          <div class="compact-stat-value warning">Unknown</div>
          <div class="compact-stat-label">Logs</div>
        </div>
        <div class="compact-stat">
          <div class="compact-stat-value warning">Unknown</div>
          <div class="compact-stat-label">Cache</div>
        </div>
        <div class="compact-stat">
          <div class="compact-stat-value warning">Unknown</div>
          <div class="compact-stat-label">Total</div>
        </div>
      `;
    }

    return `
      <div class="compact-stat">
        <div class="compact-stat-value">${data.database || '0 MB'}</div>
        <div class="compact-stat-label">Database</div>
      </div>
      <div class="compact-stat">
        <div class="compact-stat-value">${data.logs || '0 MB'}</div>
        <div class="compact-stat-label">Logs</div>
      </div>
      <div class="compact-stat">
        <div class="compact-stat-value">${data.cache || '0 MB'}</div>
        <div class="compact-stat-label">Cache</div>
      </div>
      <div class="compact-stat">
        <div class="compact-stat-value success">${data.total || '0 MB'}</div>
        <div class="compact-stat-label">Total</div>
      </div>
    `;
  }

  /**
   * Format network status - compact
   */
  formatNetworkStatusCompact() {
    return `
      <div class="compact-stat">
        <div class="compact-stat-value success">Online</div>
        <div class="compact-stat-label">Status</div>
      </div>
      <div class="compact-stat">
        <div class="compact-stat-value success">Low</div>
        <div class="compact-stat-label">Latency</div>
      </div>
      <div class="compact-stat">
        <div class="compact-stat-value">Normal</div>
        <div class="compact-stat-label">Bandwidth</div>
      </div>
      <div class="compact-stat">
        <div class="compact-stat-value success">Stable</div>
        <div class="compact-stat-label">Connection</div>
      </div>
    `;
  }

  /**
   * Format security status - compact
   */
  formatSecurityStatusCompact() {
    return `
      <div class="compact-stat">
        <div class="compact-stat-value success">Secure</div>
        <div class="compact-stat-label">Status</div>
      </div>
      <div class="compact-stat">
        <div class="compact-stat-value">0</div>
        <div class="compact-stat-label">Failed Logins</div>
      </div>
      <div class="compact-stat">
        <div class="compact-stat-value success">Now</div>
        <div class="compact-stat-label">Last Scan</div>
      </div>
      <div class="compact-stat">
        <div class="compact-stat-value success">Active</div>
        <div class="compact-stat-label">Firewall</div>
      </div>
    `;
  }

  /**
   * Format network status
   */
  formatNetworkStatus() {
    return `
      <div class="stat-item">
        <span class="stat-label">Status</span>
        <span class="stat-value success">Online</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Latency</span>
        <span class="stat-value success">Low</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Bandwidth</span>
        <span class="stat-value">Normal</span>
      </div>
    `;
  }

  /**
   * Format security status
   */
  formatSecurityStatus() {
    return `
      <div class="stat-item">
        <span class="stat-label">Status</span>
        <span class="stat-value success">Secure</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Failed Logins</span>
        <span class="stat-value">0</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Last Scan</span>
        <span class="stat-value">Now</span>
      </div>
    `;
  }

  /**
   * Update running processes
   */
  updateRunningProcesses() {
    const processesContainer = document.getElementById('running-processes');
    if (processesContainer) {
      processesContainer.innerHTML = `
        <div class="process-list">
          <div class="process-item">
            <div class="process-name">Node.js Server</div>
            <div class="process-status success">Running</div>
            <div class="process-cpu">2.5%</div>
            <div class="process-memory">45 MB</div>
          </div>
          <div class="process-item">
            <div class="process-name">MongoDB</div>
            <div class="process-status success">Running</div>
            <div class="process-cpu">1.2%</div>
            <div class="process-memory">128 MB</div>
          </div>
          <div class="process-item">
            <div class="process-name">GraphQL Server</div>
            <div class="process-status success">Running</div>
            <div class="process-cpu">0.8%</div>
            <div class="process-memory">32 MB</div>
          </div>
        </div>
      `;
    }
  }

  /**
   * Update system errors
   */
  updateSystemErrors() {
    const errorsContainer = document.getElementById('system-errors');
    if (errorsContainer) {
      // Check for common system errors
      const errors = [];

      // Check if storage data is unknown
      if (document.getElementById('storage-usage')?.innerHTML.includes('Unknown')) {
        errors.push({
          type: 'warning',
          message: 'Storage usage data unavailable',
          time: 'Now'
        });
      }

      if (errors.length === 0) {
        errorsContainer.innerHTML = `
          <div class="no-errors">
            <i class="fas fa-check-circle success"></i>
            <span>No system errors detected</span>
          </div>
        `;
      } else {
        errorsContainer.innerHTML = `
          <div class="error-list">
            ${errors.map(error => `
              <div class="error-item ${error.type}">
                <div class="error-icon">
                  <i class="fas fa-${error.type === 'error' ? 'exclamation-circle' : 'exclamation-triangle'}"></i>
                </div>
                <div class="error-content">
                  <div class="error-message">${error.message}</div>
                  <div class="error-time">${error.time}</div>
                </div>
              </div>
            `).join('')}
          </div>
        `;
      }
    }
  }

  /**
   * Setup real-time updates
   */
  setupRealTimeUpdates() {
    // Update dashboard every 30 seconds
    setInterval(() => {
      const activeTab = document.querySelector('.tab-content.active');
      if (activeTab && activeTab.id === 'dashboard-tab') {
        this.loadDashboardData();
      }
    }, 30000);
  }

  /**
   * Show login modal
   */
  showLoginModal() {
    // Create modal if it doesn't exist
    if (!document.getElementById('admin-login-modal')) {
      const modal = document.createElement('div');
      modal.id = 'admin-login-modal';
      modal.className = 'modal';
      modal.innerHTML = `
        <div class="modal-content">
          <span class="close">&times;</span>
          <h2>Admin Login</h2>
          <div class="admin-login-form">
            <input type="password" id="admin-key" placeholder="Enter admin key" />
            <button id="admin-login-submit">Login</button>
          </div>
          <div id="admin-login-message"></div>
        </div>
      `;
      document.body.appendChild(modal);

      // Add event listeners
      document.querySelector('#admin-login-modal .close').addEventListener('click', () => {
        document.getElementById('admin-login-modal').style.display = 'none';
      });

      document.getElementById('admin-login-submit').addEventListener('click', () => {
        this.login();
      });

      // Allow pressing Enter to submit
      document.getElementById('admin-key').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          this.login();
        }
      });
    }

    // Show modal
    document.getElementById('admin-login-modal').style.display = 'block';
  }

  /**
   * Login with admin key
   */
  async login() {
    const adminKey = document.getElementById('admin-key').value;
    if (!adminKey) {
      this.showLoginMessage('Please enter admin key', 'error');
      return;
    }

    try {
      this.showLoginMessage('Logging in...', 'info');

      const response = await fetch('/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
            mutation AdminLogin($adminKey: String!) {
              adminLogin(adminKey: $adminKey) {
                success
                token
                message
              }
            }
          `,
          variables: {
            adminKey
          }
        })
      });

      const result = await response.json();

      if (result.data?.adminLogin?.success) {
        this.token = result.data.adminLogin.token;
        localStorage.setItem('adminToken', this.token);
        this.isAdmin = true;
        this.showLoginMessage('Login successful!', 'success');

        // Close modal after a short delay
        setTimeout(() => {
          document.getElementById('admin-login-modal').style.display = 'none';
          this.enableAdminMode();
        }, 1000);
      } else {
        this.showLoginMessage(result.data?.adminLogin?.message || 'Login failed', 'error');
      }
    } catch (error) {
      console.error('Admin login error:', error);
      this.showLoginMessage('Error logging in', 'error');
    }
  }

  /**
   * Validate admin token
   */
  async validateToken(token) {
    try {
      const response = await fetch('/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
            query ValidateAdminToken($token: String!) {
              validateAdminToken(token: $token) {
                isValid
              }
            }
          `,
          variables: {
            token
          }
        })
      });

      const result = await response.json();
      return result.data?.validateAdminToken?.isValid || false;
    } catch (error) {
      console.error('Token validation error:', error);
      return false;
    }
  }

  /**
   * Logout admin
   */
  logout() {
    this.token = null;
    this.isAdmin = false;
    localStorage.removeItem('adminToken');
    this.disableAdminMode();
  }

  /**
   * Show login message
   */
  showLoginMessage(message, type = 'info') {
    const messageElement = document.getElementById('admin-login-message');
    if (messageElement) {
      messageElement.textContent = message;
      messageElement.className = `message ${type}`;
    }
  }

  /**
   * Enable admin mode
   */
  enableAdminMode() {
    console.log('enableAdminMode called');
    document.body.classList.add('admin-mode');

    // Update admin button
    const adminButton = document.getElementById('admin-login-button');
    if (adminButton) {
      adminButton.innerHTML = `<i class="fas fa-unlock"></i> Admin`;
      adminButton.classList.add('active');
    }

    // Add delete buttons to grid items
    this.addDeleteButtons();

    // Add admin controls to media detail page if we're on one
    console.log('Calling addMediaDetailAdminControls from enableAdminMode');
    this.addMediaDetailAdminControls();

    // Add observer for new grid items
    this.setupGridObserver();

    // Add observer for media detail page
    this.setupMediaDetailObserver();
  }

  /**
   * Setup observer for media detail page
   */
  setupMediaDetailObserver() {
    console.log('Setting up media detail observer');

    // Create a mutation observer to watch for changes to the banner element
    const observer = new MutationObserver((mutations) => {
      console.log('Media detail mutation detected');
      this.addMediaDetailAdminControls();
    });

    // Start observing the document with the configured parameters
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: false,
      characterData: false
    });
  }

  /**
   * Disable admin mode
   */
  disableAdminMode() {
    document.body.classList.remove('admin-mode');

    // Update admin button
    const adminButton = document.getElementById('admin-login-button');
    if (adminButton) {
      adminButton.innerHTML = `<i class="fas fa-lock"></i> Admin`;
      adminButton.classList.remove('active');
    }

    // Remove delete buttons
    document.querySelectorAll('.delete-button').forEach(button => {
      button.remove();
    });
  }

  /**
   * Add delete buttons to grid items
   */
  addDeleteButtons() {
    document.querySelectorAll('.grid-item').forEach(item => {
      if (!item.querySelector('.delete-button')) {
        const id = item.dataset.id;
        const type = item.dataset.type;

        if (id && type) {
          // Normalize type to match GraphQL enum
          const itemType = this.normalizeType(type);

          const button = document.createElement('button');
          button.className = 'delete-button';
          button.innerHTML = `<i class="fas fa-times"></i>`;
          button.title = 'Delete item';
          button.dataset.id = id;
          button.dataset.type = itemType;

          button.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.confirmDelete(id, itemType, item);
          });

          item.appendChild(button);
        }
      }
    });
  }

  /**
   * Setup observer for new grid items
   */
  setupGridObserver() {
    // Create a mutation observer to watch for new grid items
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.addedNodes && mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach((node) => {
            // Check if the added node is a grid item or contains grid items
            if (node.classList && node.classList.contains('grid-item')) {
              this.addDeleteButtonToItem(node);
            } else if (node.querySelectorAll) {
              node.querySelectorAll('.grid-item').forEach(item => {
                this.addDeleteButtonToItem(item);
              });
            }
          });
        }
      });
    });

    // Start observing the document with the configured parameters
    observer.observe(document.body, { childList: true, subtree: true });
  }

  /**
   * Add delete button to a single grid item
   */
  addDeleteButtonToItem(item) {
    if (!item.querySelector('.delete-button')) {
      const id = item.dataset.id;
      const type = item.dataset.type;

      if (id && type) {
        // Normalize type to match GraphQL enum
        const itemType = this.normalizeType(type);

        const button = document.createElement('button');
        button.className = 'delete-button';
        button.innerHTML = `<i class="fas fa-times"></i>`;
        button.title = 'Delete item';
        button.dataset.id = id;
        button.dataset.type = itemType;

        button.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          this.confirmDelete(id, itemType, item);
        });

        item.appendChild(button);
      }
    }
  }

  /**
   * Normalize type to match GraphQL enum
   */
  normalizeType(type) {
    const typeMap = {
      'movies': 'MOVIE',
      'movie': 'MOVIE',
      'series': 'SERIES',
      'serie': 'SERIES',
      'anime': 'ANIME',
      'livetv': 'LIVETV',
      'live': 'LIVETV'
    };

    return typeMap[type.toLowerCase()] || type.toUpperCase();
  }

  /**
   * Show delete confirmation
   */
  confirmDelete(id, type, itemElement) {
    // Create confirmation modal if it doesn't exist
    if (!document.getElementById('delete-confirm-modal')) {
      const modal = document.createElement('div');
      modal.id = 'delete-confirm-modal';
      modal.className = 'modal';
      modal.innerHTML = `
        <div class="modal-content">
          <span class="close">&times;</span>
          <h2>Confirm Delete</h2>
          <p>Are you sure you want to delete this item? This action cannot be undone.</p>
          <div class="item-info">
            <strong>Title:</strong> <span id="delete-item-title"></span><br>
            <strong>Type:</strong> <span id="delete-item-type"></span>
          </div>
          <div class="modal-buttons">
            <button id="delete-cancel" class="button secondary">Cancel</button>
            <button id="delete-confirm" class="button danger">Delete</button>
          </div>
          <div id="delete-message"></div>
        </div>
      `;
      document.body.appendChild(modal);

      // Add event listeners
      document.querySelector('#delete-confirm-modal .close').addEventListener('click', () => {
        document.getElementById('delete-confirm-modal').style.display = 'none';
      });

      document.getElementById('delete-cancel').addEventListener('click', () => {
        document.getElementById('delete-confirm-modal').style.display = 'none';
      });
    }

    // Update modal content
    const titleElement = itemElement.querySelector('.title');
    const title = titleElement ? titleElement.textContent : 'Unknown';

    document.getElementById('delete-item-title').textContent = title;
    document.getElementById('delete-item-type').textContent = type;

    // Update confirm button event listener
    const confirmButton = document.getElementById('delete-confirm');
    confirmButton.onclick = null; // Remove previous listener
    confirmButton.addEventListener('click', () => {
      this.deleteItem(id, type, itemElement);
    });

    // Show modal
    document.getElementById('delete-confirm-modal').style.display = 'block';
  }

  /**
   * Delete item
   */
  async deleteItem(id, type, itemElement) {
    try {
      document.getElementById('delete-message').textContent = 'Deleting...';
      document.getElementById('delete-message').className = 'message info';

      const response = await fetch('/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
            mutation DeleteItem($id: ID!, $type: ItemType!, $adminToken: String!) {
              deleteItem(id: $id, type: $type, adminToken: $adminToken) {
                success
                message
                deletedId
                type
              }
            }
          `,
          variables: {
            id,
            type,
            adminToken: this.token
          }
        })
      });

      const result = await response.json();

      if (result.data?.deleteItem?.success) {
        document.getElementById('delete-message').textContent = 'Item deleted successfully!';
        document.getElementById('delete-message').className = 'message success';

        // Remove item from UI after a short delay
        setTimeout(() => {
          itemElement.remove();
          document.getElementById('delete-confirm-modal').style.display = 'none';
        }, 1000);
      } else {
        document.getElementById('delete-message').textContent = result.data?.deleteItem?.message || 'Failed to delete item';
        document.getElementById('delete-message').className = 'message error';
      }
    } catch (error) {
      console.error('Delete error:', error);
      document.getElementById('delete-message').textContent = 'Error deleting item';
      document.getElementById('delete-message').className = 'message error';
    }
  }

  /**
   * Add admin controls to media detail page
   */
  addMediaDetailAdminControls() {
    console.log('addMediaDetailAdminControls called');

    // Check if we're on a media detail page
    const banner = document.getElementById('banner');
    const mediaTitle = document.getElementById('media-title');
    console.log('Banner element:', banner);
    console.log('Media title element:', mediaTitle);

    if (!banner || !mediaTitle) {
      console.log('Not on a media detail page, banner or mediaTitle missing');
      return;
    }

    // Try to get the item ID and type from the banner data attributes
    let itemId = banner.dataset.id;
    let itemType = banner.dataset.type;

    // If not available in data attributes, try to get from URL
    if (!itemId || !itemType) {
      console.log('Item ID or type not found in data attributes, trying URL');

      // URL format is like: /movies/ID, /series/ID, /anime/ID, /livetv/ID
      const pathParts = window.location.pathname.split('/').filter(part => part);
      if (pathParts.length < 2) {
        console.log('Not on a media detail page, URL format incorrect');
        return;
      }

      itemType = pathParts[0].toUpperCase();
      itemId = pathParts[1];
    }

    console.log('Item type:', itemType);
    console.log('Item ID:', itemId);

    if (!itemId || !itemType) {
      console.log('Item ID or type missing');
      return;
    }

    // Create admin controls container if it doesn't exist
    if (!document.getElementById('admin-detail-controls')) {
      const controlsContainer = document.createElement('div');
      controlsContainer.id = 'admin-detail-controls';
      controlsContainer.className = 'admin-detail-controls';

      // Create a wrapper for the delete button
      const deleteWrapper = document.createElement('div');
      deleteWrapper.className = 'admin-detail-button-wrapper';

      // Add delete button
      const deleteButton = document.createElement('button');
      deleteButton.className = 'admin-detail-button delete-button';
      deleteButton.innerHTML = `<i class="fas fa-trash"></i>`;
      deleteButton.title = 'Delete item';
      deleteButton.dataset.id = itemId;
      deleteButton.dataset.type = this.normalizeType(itemType);

      deleteButton.addEventListener('click', (e) => {
        e.preventDefault();
        this.confirmDetailDelete(itemId, this.normalizeType(itemType));
      });

      // Add delete button to its wrapper
      deleteWrapper.appendChild(deleteButton);

      // Create a wrapper for the scrape button
      const scrapeWrapper = document.createElement('div');
      scrapeWrapper.className = 'admin-detail-button-wrapper';

      // Add scrape button
      const scrapeButton = document.createElement('button');
      scrapeButton.className = 'admin-detail-button scrape-button';
      scrapeButton.innerHTML = `<i class="fas fa-sync-alt"></i>`;
      scrapeButton.title = 'Scrape item';
      scrapeButton.dataset.id = itemId;
      scrapeButton.dataset.type = this.normalizeType(itemType);

      scrapeButton.addEventListener('click', (e) => {
        e.preventDefault();
        this.scrapeItem(itemId, this.normalizeType(itemType));
      });

      // Add scrape button to its wrapper
      scrapeWrapper.appendChild(scrapeButton);

      // Add button wrappers to container
      controlsContainer.appendChild(deleteWrapper);
      controlsContainer.appendChild(scrapeWrapper);

      // Add container to banner
      banner.appendChild(controlsContainer);

      console.log('Admin detail controls added to banner');
    }
  }

  /**
   * Confirm deletion of an item from the detail page
   */
  confirmDetailDelete(id, type) {
    // Create confirmation modal if it doesn't exist
    if (!document.getElementById('delete-confirm-modal')) {
      const modal = document.createElement('div');
      modal.id = 'delete-confirm-modal';
      modal.className = 'modal';
      modal.innerHTML = `
        <div class="modal-content">
          <span class="close">&times;</span>
          <h2>Confirm Delete</h2>
          <p>Are you sure you want to delete this item? This action cannot be undone.</p>
          <div class="item-info">
            <strong>Title:</strong> <span id="delete-item-title"></span><br>
            <strong>Type:</strong> <span id="delete-item-type"></span>
          </div>
          <div class="modal-buttons">
            <button id="delete-cancel" class="button secondary">Cancel</button>
            <button id="delete-confirm" class="button danger">Delete</button>
          </div>
          <div id="delete-message"></div>
        </div>
      `;
      document.body.appendChild(modal);

      // Add event listeners
      document.querySelector('#delete-confirm-modal .close').addEventListener('click', () => {
        document.getElementById('delete-confirm-modal').style.display = 'none';
      });

      document.getElementById('delete-cancel').addEventListener('click', () => {
        document.getElementById('delete-confirm-modal').style.display = 'none';
      });
    }

    // Update modal content
    const title = document.getElementById('media-title').textContent;

    document.getElementById('delete-item-title').textContent = title;
    document.getElementById('delete-item-type').textContent = type;

    // Update confirm button event listener
    const confirmButton = document.getElementById('delete-confirm');
    confirmButton.onclick = null; // Remove previous listener
    confirmButton.addEventListener('click', () => {
      this.deleteDetailItem(id, type);
    });

    // Show modal
    document.getElementById('delete-confirm-modal').style.display = 'block';
  }

  /**
   * Delete an item from the detail page
   */
  async deleteDetailItem(id, type) {
    try {
      document.getElementById('delete-message').textContent = 'Deleting...';
      document.getElementById('delete-message').className = 'message info';

      const response = await fetch('/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
            mutation DeleteItem($id: ID!, $type: ItemType!, $adminToken: String!) {
              deleteItem(id: $id, type: $type, adminToken: $adminToken) {
                success
                message
                deletedId
                type
              }
            }
          `,
          variables: {
            id,
            type,
            adminToken: this.token
          }
        })
      });

      const result = await response.json();

      if (result.data?.deleteItem?.success) {
        document.getElementById('delete-message').textContent = 'Item deleted successfully!';
        document.getElementById('delete-message').className = 'message success';

        // Redirect to home page after a short delay
        setTimeout(() => {
          window.location.href = '/';
        }, 1000);
      } else {
        document.getElementById('delete-message').textContent = result.data?.deleteItem?.message || 'Failed to delete item';
        document.getElementById('delete-message').className = 'message error';
      }
    } catch (error) {
      console.error('Delete error:', error);
      document.getElementById('delete-message').textContent = 'Error deleting item';
      document.getElementById('delete-message').className = 'message error';
    }
  }

  /**
   * Scrape an item
   */
  async scrapeItem(id, type) {
    try {
      // Create scrape log modal if it doesn't exist
      if (!document.getElementById('scrape-log-modal')) {
        const modal = document.createElement('div');
        modal.id = 'scrape-log-modal';
        modal.className = 'modal';
        modal.innerHTML = `
          <div class="modal-content scrape-log-content">
            <span class="close">&times;</span>
            <h2>Scrape Log</h2>
            <div class="scrape-status">
              <div class="scrape-spinner"><i class="fas fa-spinner fa-spin"></i></div>
              <div id="scrape-status-message">Starting scrape operation...</div>
            </div>
            <div id="scrape-log" class="scrape-log"></div>
            <div class="modal-buttons">
              <button id="scrape-close" class="button secondary">Close</button>
            </div>
          </div>
        `;
        document.body.appendChild(modal);

        // Add event listeners
        document.querySelector('#scrape-log-modal .close').addEventListener('click', () => {
          document.getElementById('scrape-log-modal').style.display = 'none';
          this.closeWebSocket();
        });

        document.getElementById('scrape-close').addEventListener('click', () => {
          document.getElementById('scrape-log-modal').style.display = 'none';
          this.closeWebSocket();
        });
      }

      // Clear previous log
      const logElement = document.getElementById('scrape-log');
      logElement.innerHTML = '';

      // Show modal
      document.getElementById('scrape-log-modal').style.display = 'block';
      document.getElementById('scrape-status-message').textContent = 'Starting scrape operation...';

      // Start the scrape operation
      const response = await fetch('/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
            mutation ScrapeItem($id: ID!, $type: ItemType!, $adminToken: String!) {
              scrapeItem(id: $id, type: $type, adminToken: $adminToken) {
                success
                message
                itemId
                type
                logId
              }
            }
          `,
          variables: {
            id,
            type,
            adminToken: this.token
          }
        })
      });

      const result = await response.json();

      if (result.data?.scrapeItem?.success) {
        const logId = result.data.scrapeItem.logId;
        document.getElementById('scrape-status-message').textContent = 'Scrape operation in progress...';

        // Connect to WebSocket for live logs
        this.connectToWebSocket(logId);
      } else {
        document.getElementById('scrape-status-message').textContent = result.data?.scrapeItem?.message || 'Failed to start scrape';
        this.addLogMessage('error', 'Failed to start scrape operation', result.data?.scrapeItem?.message || 'Unknown error');
      }
    } catch (error) {
      console.error('Scrape error:', error);
      document.getElementById('scrape-status-message').textContent = 'Error starting scrape';
      this.addLogMessage('error', 'Error starting scrape operation', error.message);
    }
  }

  /**
   * Connect to WebSocket for live logs
   */
  connectToWebSocket(logId) {
    // Close existing connection if any
    this.closeWebSocket();

    // Create new WebSocket connection
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.hostname;

    // Always use the same port as the current page
    // This ensures WebSockets work on all platforms including render.com
    const port = window.location.port || (window.location.protocol === 'https:' ? '443' : '80');
    const wsUrl = `${protocol}//${host}${port ? ':' + port : ''}`;

    console.log('Connecting to WebSocket at:', wsUrl);

    try {
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('WebSocket connected successfully');
        // Subscribe to log stream
        this.ws.send(JSON.stringify({
          type: 'subscribe',
          logId: logId
        }));

        this.addLogMessage('info', 'Connected to log stream', 'Waiting for scrape logs...');
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          if (data.type === 'log') {
            this.addLogMessage(data.level, data.message, data.meta);

            // Check if this is a completion message
            if (data.message.includes('completed successfully')) {
              document.getElementById('scrape-status-message').textContent = 'Scrape completed successfully!';
              document.querySelector('.scrape-spinner').innerHTML = '<i class="fas fa-check-circle"></i>';
              document.querySelector('.scrape-spinner').classList.add('success');
            } else if (data.message.includes('Error during scrape')) {
              document.getElementById('scrape-status-message').textContent = 'Scrape failed with errors';
              document.querySelector('.scrape-spinner').innerHTML = '<i class="fas fa-exclamation-circle"></i>';
              document.querySelector('.scrape-spinner').classList.add('error');
            }
          } else if (data.type === 'subscribed') {
            this.addLogMessage('info', 'Subscribed to log stream', `Log ID: ${data.logId}`);
          }
        } catch (error) {
          console.error('WebSocket message error:', error);
          this.addLogMessage('error', 'WebSocket message error', error.message);
        }
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.addLogMessage('error', 'WebSocket connection error', 'Could not connect to the server. The scraping process is still running in the background.');

        // Update the status message
        document.getElementById('scrape-status-message').textContent = 'Scraping in progress (logs unavailable)';

        // Add a reconnect button
        const logElement = document.getElementById('scrape-log');
        const reconnectButton = document.createElement('button');
        reconnectButton.className = 'button';
        reconnectButton.textContent = 'Try to reconnect';
        reconnectButton.addEventListener('click', () => {
          this.connectToWebSocket(logId);
        });

        const reconnectContainer = document.createElement('div');
        reconnectContainer.className = 'reconnect-container';
        reconnectContainer.appendChild(reconnectButton);

        logElement.appendChild(reconnectContainer);
      };

      this.ws.onclose = (event) => {
        console.log('WebSocket closed:', event.code, event.reason);

        if (event.code !== 1000) {
          // Abnormal closure
          this.addLogMessage('warn', 'WebSocket connection closed', `Code: ${event.code}${event.reason ? ', Reason: ' + event.reason : ''}`);
        }
      };
    } catch (error) {
      console.error('Error creating WebSocket:', error);
      this.addLogMessage('error', 'Failed to create WebSocket connection', error.message);

      // Update the status message
      document.getElementById('scrape-status-message').textContent = 'Scraping in progress (logs unavailable)';
    }
  }

  /**
   * Close WebSocket connection
   */
  closeWebSocket() {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.close();
    }
  }

  /**
   * Add log message to the log display
   */
  addLogMessage(level, message, meta) {
    const logElement = document.getElementById('scrape-log');
    if (!logElement) return;

    const timestamp = new Date().toLocaleTimeString();

    const logItem = document.createElement('div');
    logItem.className = `log-item ${level}`;

    // Check if this is a special log for problematic URLs
    if (message.startsWith('[SPECIAL]') || (meta && meta.special)) {
      logItem.classList.add('special');
      message = message.replace('[SPECIAL] ', '');
    }

    let metaText = '';
    if (meta && typeof meta === 'object') {
      // Filter out special flag and other internal properties from display
      const displayMeta = {...meta};
      delete displayMeta.special;

      metaText = Object.entries(displayMeta)
        .filter(([key, value]) => key !== 'stack' && value !== undefined)
        .map(([key, value]) => `${key}: ${value}`)
        .join(', ');
    } else if (meta && typeof meta === 'string') {
      metaText = meta;
    }

    logItem.innerHTML = `
      <span class="log-timestamp">[${timestamp}]</span>
      <span class="log-level ${level}">[${level.toUpperCase()}]</span>
      <span class="log-message">${message}</span>
      ${metaText ? `<div class="log-meta">${metaText}</div>` : ''}
    `;

    logElement.appendChild(logItem);
    logElement.scrollTop = logElement.scrollHeight;
  }

  /**
   * Show old admin panel (kept for compatibility)
   */
  async showOldAdminPanel() {
    // Create a simple admin panel with logout button
    if (!document.getElementById('admin-panel-modal')) {
      const modal = document.createElement('div');
      modal.id = 'admin-panel-modal';
      modal.className = 'modal';
      modal.innerHTML = `
        <div class="modal-content">
          <span class="close">&times;</span>
          <h2>Admin Panel</h2>
          <p>You are currently logged in as an administrator.</p>

          <div class="admin-tabs">
            <div class="tab-buttons">
              <button class="tab-button active" data-tab="config-tab">Base URL Configuration</button>
              <button class="tab-button" data-tab="manual-scrape-tab">Manual URL Scrape</button>
              <button class="tab-button" data-tab="display-settings-tab">Display Settings</button>
            </div>

            <div class="tab-content">
              <div id="config-tab" class="tab-pane active">
                <h3>Base URL Configuration</h3>
                <div class="config-form">
                  <div class="form-group">
                    <label for="wiflix-base">Wiflix Base URL:</label>
                    <div class="input-with-button">
                      <input type="text" id="wiflix-base" placeholder="e.g., wiflix-max.cam" />
                      <button id="update-wiflix" class="button small">Update</button>
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="french-anime-base">French Anime Base URL:</label>
                    <div class="input-with-button">
                      <input type="text" id="french-anime-base" placeholder="e.g., french-anime.com" />
                      <button id="update-french-anime" class="button small">Update</button>
                    </div>
                  </div>

                  <div class="form-group">
                    <label for="witv-base">WiTV Base URL:</label>
                    <div class="input-with-button">
                      <input type="text" id="witv-base" placeholder="e.g., witv.skin" />
                      <button id="update-witv" class="button small">Update</button>
                    </div>
                  </div>

                  <div id="config-message" class="message"></div>
                </div>
              </div>

              <div id="manual-scrape-tab" class="tab-pane">
                <h3>Manual URL Scrape</h3>
                <p>Add a URL to manually scrape content that wasn't automatically scraped.</p>
                <div class="manual-scrape-form">
                  <div class="form-group">
                    <label for="manual-scrape-url">URL to Scrape:</label>
                    <input type="text" id="manual-scrape-url" placeholder="e.g., https://flemmix.net/serie-en-streaming/33564-bet-saison-1.html" />
                  </div>

                  <div class="form-group">
                    <label for="manual-scrape-type">Content Type:</label>
                    <select id="manual-scrape-type">
                      <option value="MOVIE">Movie</option>
                      <option value="SERIES" selected>Series</option>
                      <option value="ANIME">Anime</option>
                      <option value="LIVETV">Live TV</option>
                    </select>
                  </div>

                  <button id="start-manual-scrape" class="button primary">Start Scrape</button>

                  <div id="manual-scrape-message" class="message"></div>
                </div>
              </div>

              <div id="display-settings-tab" class="tab-pane">
                <h3>Display Settings</h3>
                <p>Control what content is displayed to reduce server load when needed.</p>
                <div class="display-settings-form">
                  <div class="form-group">
                    <div class="toggle-group">
                      <label class="toggle-label">
                        <span class="toggle-text">
                          <strong>Grid Items Display</strong>
                          <small>Show/hide grid items in Movies, Series, and Anime sections</small>
                        </span>
                        <div class="toggle-switch">
                          <input type="checkbox" id="grid-items-toggle" checked />
                          <span class="toggle-slider"></span>
                        </div>
                      </label>
                    </div>
                    <div class="toggle-description">
                      <p><strong>When ON:</strong> Grid items are loaded and displayed normally</p>
                      <p><strong>When OFF:</strong> Grid items are hidden and not loaded, reducing server load. Only carousels will be visible.</p>
                    </div>
                  </div>

                  <div class="form-group">
                    <div class="grid-status">
                      <span id="grid-status-indicator" class="status-indicator active">Grid items are currently <strong>enabled</strong></span>
                    </div>
                  </div>

                  <div id="display-settings-message" class="message"></div>
                </div>
              </div>
            </div>
          </div>

          <div class="admin-actions">
            <button id="admin-logout" class="button">Logout</button>
          </div>
        </div>
      `;
      document.body.appendChild(modal);

      // Add event listeners
      document.querySelector('#admin-panel-modal .close').addEventListener('click', () => {
        document.getElementById('admin-panel-modal').style.display = 'none';
      });

      document.getElementById('admin-logout').addEventListener('click', () => {
        this.logout();
        document.getElementById('admin-panel-modal').style.display = 'none';
      });

      // Add event listeners for update buttons
      document.getElementById('update-wiflix').addEventListener('click', () => {
        this.updateBaseUrl('WIFLIX_BASE', document.getElementById('wiflix-base').value);
      });

      document.getElementById('update-french-anime').addEventListener('click', () => {
        this.updateBaseUrl('FRENCH_ANIME_BASE', document.getElementById('french-anime-base').value);
      });

      document.getElementById('update-witv').addEventListener('click', () => {
        this.updateBaseUrl('WITV_BASE', document.getElementById('witv-base').value);
      });

      // Add event listener for manual scrape button
      document.getElementById('start-manual-scrape').addEventListener('click', () => {
        this.scrapeUrlManually();
      });

      // Add event listeners for tab buttons
      document.querySelectorAll('.tab-button').forEach(button => {
        button.addEventListener('click', (e) => {
          // Remove active class from all buttons and panes
          document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
          document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));

          // Add active class to clicked button
          e.target.classList.add('active');

          // Show corresponding tab pane
          const tabId = e.target.dataset.tab;
          document.getElementById(tabId).classList.add('active');
        });
      });

      // Add event listener for grid items toggle
      document.getElementById('grid-items-toggle').addEventListener('change', (e) => {
        this.toggleGridItems(e.target.checked);
      });
    }

    // Show modal
    document.getElementById('admin-panel-modal').style.display = 'block';

    // Fetch current config values
    await this.fetchConfigValues();

    // Initialize display settings
    this.initializeDisplaySettings();
  }

  /**
   * Fetch current config values
   */
  async fetchConfigValues() {
    try {
      const response = await fetch('/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
            query GetConfig {
              config {
                wiflixBase
                frenchAnimeBase
                witvBase
              }
            }
          `
        })
      });

      const result = await response.json();

      if (result.data?.config) {
        const { wiflixBase, frenchAnimeBase, witvBase } = result.data.config;

        // Update input fields with current values
        const wiflixInput = document.getElementById('wiflix-base');
        const frenchAnimeInput = document.getElementById('french-anime-base');
        const witvInput = document.getElementById('witv-base');

        if (wiflixInput) wiflixInput.value = wiflixBase || '';
        if (frenchAnimeInput) frenchAnimeInput.value = frenchAnimeBase || '';
        if (witvInput) witvInput.value = witvBase || '';
      }
    } catch (error) {
      console.error('Error fetching config:', error);
      this.showConfigMessage('Error fetching configuration values', 'error');
    }
  }

  /**
   * Update base URL
   */
  async updateBaseUrl(key, value) {
    if (!value) {
      this.showConfigMessage('Please enter a value', 'error');
      return;
    }

    try {
      this.showConfigMessage('Updating...', 'info');

      const response = await fetch('/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
            mutation UpdateBaseUrl($key: String!, $value: String!, $adminToken: String!) {
              updateBaseUrl(key: $key, value: $value, adminToken: $adminToken) {
                success
                message
                key
                value
              }
            }
          `,
          variables: {
            key,
            value,
            adminToken: this.token
          }
        })
      });

      const result = await response.json();

      if (result.data?.updateBaseUrl?.success) {
        this.showConfigMessage(result.data.updateBaseUrl.message, 'success');
      } else {
        this.showConfigMessage(result.data?.updateBaseUrl?.message || 'Update failed', 'error');
      }
    } catch (error) {
      console.error('Error updating base URL:', error);
      this.showConfigMessage('Error updating base URL', 'error');
    }
  }

  /**
   * Show config message
   */
  showConfigMessage(message, type) {
    const messageElement = document.getElementById('config-message');
    if (messageElement) {
      messageElement.textContent = message;
      messageElement.className = `message ${type}`;
    }
  }

  /**
   * Show manual scrape message
   */
  showManualScrapeMessage(message, type) {
    const messageElement = document.getElementById('manual-scrape-message');
    if (messageElement) {
      messageElement.textContent = message;
      messageElement.className = `message ${type}`;
    }
  }

  /**
   * Scrape URL manually
   */
  async scrapeUrlManually() {
    const url = document.getElementById('manual-scrape-url').value;
    const type = document.getElementById('manual-scrape-type').value;

    if (!url) {
      this.showManualScrapeMessage('Please enter a URL to scrape', 'error');
      return;
    }

    try {
      // Create scrape log modal if it doesn't exist
      if (!document.getElementById('scrape-log-modal')) {
        const modal = document.createElement('div');
        modal.id = 'scrape-log-modal';
        modal.className = 'modal';
        modal.innerHTML = `
          <div class="modal-content scrape-log-content">
            <span class="close">&times;</span>
            <h2>Scrape Log</h2>
            <div class="scrape-status">
              <div class="scrape-spinner"><i class="fas fa-spinner fa-spin"></i></div>
              <div id="scrape-status-message">Starting scrape operation...</div>
            </div>
            <div id="scrape-log" class="scrape-log"></div>
            <div class="modal-buttons">
              <button id="scrape-close" class="button secondary">Close</button>
            </div>
          </div>
        `;
        document.body.appendChild(modal);

        // Add event listeners
        document.querySelector('#scrape-log-modal .close').addEventListener('click', () => {
          document.getElementById('scrape-log-modal').style.display = 'none';
          this.closeWebSocket();
        });

        document.getElementById('scrape-close').addEventListener('click', () => {
          document.getElementById('scrape-log-modal').style.display = 'none';
          this.closeWebSocket();
        });
      }

      // Clear previous log
      const logElement = document.getElementById('scrape-log');
      logElement.innerHTML = '';

      // Show modal
      document.getElementById('scrape-log-modal').style.display = 'block';
      document.getElementById('scrape-status-message').textContent = 'Starting manual scrape operation...';

      // Reset scrape spinner
      document.querySelector('.scrape-spinner').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
      document.querySelector('.scrape-spinner').classList.remove('success', 'error');

      // Start the scrape operation
      const response = await fetch('/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
            mutation ScrapeUrlManually($url: String!, $type: ItemType!, $adminToken: String!) {
              scrapeUrlManually(url: $url, type: $type, adminToken: $adminToken) {
                success
                message
                url
                type
                logId
              }
            }
          `,
          variables: {
            url,
            type,
            adminToken: this.token
          }
        })
      });

      const result = await response.json();

      if (result.data?.scrapeUrlManually?.success) {
        const logId = result.data.scrapeUrlManually.logId;
        document.getElementById('scrape-status-message').textContent = 'Manual scrape operation in progress...';
        this.showManualScrapeMessage('Scrape operation started successfully', 'success');

        // Connect to WebSocket for live logs
        this.connectToWebSocket(logId);
      } else {
        document.getElementById('scrape-status-message').textContent = result.data?.scrapeUrlManually?.message || 'Failed to start scrape';
        this.addLogMessage('error', 'Failed to start manual scrape operation', result.data?.scrapeUrlManually?.message || 'Unknown error');
        this.showManualScrapeMessage(result.data?.scrapeUrlManually?.message || 'Failed to start scrape', 'error');
      }
    } catch (error) {
      console.error('Manual scrape error:', error);
      document.getElementById('scrape-status-message').textContent = 'Error starting manual scrape';
      this.addLogMessage('error', 'Error starting manual scrape operation', error.message);
      this.showManualScrapeMessage('Error starting manual scrape: ' + error.message, 'error');
    }
  }

  /**
   * Initialize display settings
   */
  async initializeDisplaySettings() {
    try {
      // Fetch current display settings from server
      const settings = await this.fetchDisplaySettings();
      const isEnabled = settings.gridItemsEnabled;

      // Update toggle state
      const toggle = document.getElementById('grid-items-toggle');
      if (toggle) {
        toggle.checked = isEnabled;
      }

      // Update status indicator
      this.updateGridStatusIndicator(isEnabled);

      // Apply current setting
      this.applyGridItemsDisplay(isEnabled);

      // Also store in localStorage for immediate access
      localStorage.setItem('admin_grid_items_enabled', isEnabled.toString());

      console.log('Display settings initialized. Grid items enabled:', isEnabled);
    } catch (error) {
      console.error('Error initializing display settings:', error);

      // Fallback to localStorage if server fetch fails
      const gridItemsEnabled = localStorage.getItem('admin_grid_items_enabled');
      const isEnabled = gridItemsEnabled === null ? true : gridItemsEnabled === 'true';

      // Update toggle state
      const toggle = document.getElementById('grid-items-toggle');
      if (toggle) {
        toggle.checked = isEnabled;
      }

      // Update status indicator
      this.updateGridStatusIndicator(isEnabled);

      // Apply current setting
      this.applyGridItemsDisplay(isEnabled);

      console.log('Display settings initialized from localStorage fallback. Grid items enabled:', isEnabled);
    }
  }

  /**
   * Toggle grid items display
   */
  async toggleGridItems(enabled) {
    console.log('Toggling grid items display:', enabled);

    // Apply the setting IMMEDIATELY for instant feedback
    this.applyGridItemsDisplay(enabled);

    // Update status indicator immediately
    this.updateGridStatusIndicator(enabled);

    // Save to localStorage immediately
    localStorage.setItem('admin_grid_items_enabled', enabled.toString());

    try {
      // Save setting to server (in background)
      await this.updateDisplaySettings(enabled);

      // Show success message
      const messageDiv = document.getElementById('display-settings-message');
      if (messageDiv) {
        messageDiv.innerHTML = `<p class="success">Grid items display ${enabled ? 'enabled' : 'disabled'} successfully!</p>`;
        setTimeout(() => {
          messageDiv.innerHTML = '';
        }, 3000);
      }
    } catch (error) {
      console.error('Error saving grid items display to server:', error);

      // Show warning message (but don't revert since local change worked)
      const messageDiv = document.getElementById('display-settings-message');
      if (messageDiv) {
        messageDiv.innerHTML = `<p class="warning">Grid items display changed locally, but failed to save to server: ${error.message}</p>`;
        setTimeout(() => {
          messageDiv.innerHTML = '';
        }, 5000);
      }

      // Don't revert the toggle since the local change is working
      // The setting will be applied from localStorage on next page load
    }
  }

  /**
   * Update grid status indicator
   */
  updateGridStatusIndicator(enabled) {
    const indicator = document.getElementById('grid-status-indicator');
    if (indicator) {
      indicator.className = `status-indicator ${enabled ? 'active' : 'inactive'}`;
      indicator.innerHTML = `Grid items are currently <strong>${enabled ? 'enabled' : 'disabled'}</strong>`;
    }
  }

  /**
   * Apply grid items display setting
   */
  applyGridItemsDisplay(enabled) {
    console.log('Applying grid items display setting:', enabled);

    // Use a more robust approach with retries for timing issues
    const applyWithRetry = (attempt = 0) => {
      const maxAttempts = 5;

      // Grid containers
      const gridContainers = [
        document.getElementById('movies-list'),
        document.getElementById('series-list'),
        document.getElementById('anime-list')
      ];

      // Filter controls (sort dropdowns) - direct children of sections
      const filterControls = [
        document.querySelector('#movies .filter-controls'),
        document.querySelector('#series .filter-controls'),
        document.querySelector('#anime .filter-controls')
      ];

      // Check if elements are available
      const gridElementsFound = gridContainers.filter(el => el).length;
      const filterElementsFound = filterControls.filter(el => el).length;

      console.log(`Attempt ${attempt + 1}: Found ${gridElementsFound}/3 grid containers, ${filterElementsFound}/3 filter controls`);

      // Apply to grid containers
      gridContainers.forEach((container, index) => {
        if (container) {
          if (enabled) {
            container.style.display = '';
            container.classList.remove('admin-hidden');
            console.log(`Grid container ${index} (${container.id}) enabled`);
          } else {
            container.style.display = 'none';
            container.classList.add('admin-hidden');
            // Clear the container to reduce memory usage
            container.innerHTML = '';
            console.log(`Grid container ${index} (${container.id}) disabled and cleared`);
          }
        } else {
          console.warn(`Grid container ${index} not found`);
        }
      });

      // Apply to filter controls (sort dropdowns)
      filterControls.forEach((filterControl, index) => {
        if (filterControl) {
          if (enabled) {
            filterControl.style.display = '';
            filterControl.classList.remove('admin-hidden');
            console.log(`Filter control ${index} enabled`);
          } else {
            filterControl.style.display = 'none';
            filterControl.classList.add('admin-hidden');
            console.log(`Filter control ${index} disabled`);
          }
        } else {
          console.warn(`Filter control ${index} not found`);
        }
      });

      // If some elements are missing and we haven't reached max attempts, retry
      if ((gridElementsFound < 3 || filterElementsFound < 3) && attempt < maxAttempts - 1) {
        console.log(`Retrying in 500ms (attempt ${attempt + 1}/${maxAttempts})`);
        setTimeout(() => applyWithRetry(attempt + 1), 500);
        return;
      }

      // Store the setting globally so other scripts can check it
      window.adminGridItemsEnabled = enabled;

      // Dispatch custom event to notify other scripts
      document.dispatchEvent(new CustomEvent('admin:gridItemsToggled', {
        detail: { enabled }
      }));

      console.log(`Grid items and filter controls display applied: ${enabled} (${gridElementsFound}/3 grids, ${filterElementsFound}/3 filters)`);
    };

    // Start the application process
    applyWithRetry();
  }

  /**
   * Fetch display settings from server
   */
  async fetchDisplaySettings() {
    try {
      const response = await fetch('/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
            query GetDisplaySettings($adminToken: String!) {
              displaySettings(adminToken: $adminToken) {
                gridItemsEnabled
              }
            }
          `,
          variables: {
            adminToken: this.token
          }
        })
      });

      const result = await response.json();

      if (result.data?.displaySettings) {
        return result.data.displaySettings;
      } else {
        throw new Error(result.errors?.[0]?.message || 'Failed to fetch display settings');
      }
    } catch (error) {
      console.error('Error fetching display settings:', error);
      throw error;
    }
  }

  /**
   * Update display settings on server
   */
  async updateDisplaySettings(gridItemsEnabled) {
    try {
      const response = await fetch('/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
            mutation UpdateDisplaySettings($gridItemsEnabled: Boolean!, $adminToken: String!) {
              updateDisplaySettings(gridItemsEnabled: $gridItemsEnabled, adminToken: $adminToken) {
                success
                message
                key
                value
              }
            }
          `,
          variables: {
            gridItemsEnabled,
            adminToken: this.token
          }
        })
      });

      const result = await response.json();

      if (result.data?.updateDisplaySettings?.success) {
        return result.data.updateDisplaySettings;
      } else {
        throw new Error(result.data?.updateDisplaySettings?.message || result.errors?.[0]?.message || 'Failed to update display settings');
      }
    } catch (error) {
      console.error('Error updating display settings:', error);
      throw error;
    }
  }
}

// Initialize admin manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.adminManager = new AdminManager();
});
