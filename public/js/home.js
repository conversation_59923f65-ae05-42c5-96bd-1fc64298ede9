// File: public/js/home.js
// COMPLETE AND CORRECTED VERSION (Take 2)

document.addEventListener('DOMContentLoaded', async () => {
  console.log('Home.js: DOM loaded, initializing');
  const navLinks = document.querySelectorAll('.sidebar a');
  const sections = document.querySelectorAll('.section');
  const loadedSections = {}; // Keep track of loaded pages per section { section: { page: boolean } }
  let tmdbApiKey = null; // Fetched via GraphQL
  let isFetching = false; // Flag for infinite scroll

  // --- GraphQL Helper ---
   async function fetchGraphQL(query, variables = {}) {
    // console.log('Home.js: Fetching GraphQL', { query: query.substring(0, 100) + '...', variables });
    try {
      const response = await fetch('/graphql', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
        body: JSON.stringify({ query, variables }),
      });

      if (!response.ok) {
        let errorBody = `Status: ${response.status} ${response.statusText}`;
        try { const bodyText = await response.text(); errorBody += ` - Body: ${bodyText.substring(0, 200)}`; } catch (e) { /* Ignore */ }
        throw new Error(`GraphQL fetch failed: ${errorBody}`);
      }

      const responseJson = await response.json();
      const { data, errors } = responseJson;

      if (errors) {
        console.error('Home.js: GraphQL Errors:', errors);
        const combinedErrorMessage = errors.map(e => e.message + (e.path ? ` (path: ${e.path.join('.')})` : '')).join('; ');
        throw new Error(`GraphQL Error: ${combinedErrorMessage}`);
      }

      // console.log('Home.js: GraphQL fetch successful');
      return data;

    } catch (err) {
      console.error('Home.js: fetchGraphQL Error:', err);
    }
  }

  // --- API Key Fetching (via GraphQL) ---
  async function getTmdbApiKey() {
    if (tmdbApiKey === null) {
        tmdbApiKey = ''; // Prevent race conditions
        try {
            // console.log('Home.js: Fetching TMDb API key via GraphQL');
            const query = `query GetConfig { config { tmdbApiKey } }`;
            const data = await fetchGraphQL(query);
            tmdbApiKey = data?.config?.tmdbApiKey || '';
            if (!tmdbApiKey) console.warn("Home.js: TMDb API Key missing or null in config response.");
            // else console.log("Home.js: TMDb API Key obtained.");
        } catch (err) {
            console.error('Home.js: Failed to fetch TMDb API key.');
            tmdbApiKey = '';
        }
    }
    return tmdbApiKey || null;
  }

  // --- Thumbnail Management System ---
  // Create a thumbnail manager to handle all thumbnail updates
  const thumbnailManager = {
    // Store pending thumbnail updates
    pendingUpdates: new Map(),

    // Register an image element for a specific item
    registerImage: function(imgElement, itemId) {
      if (!imgElement || !itemId) return;

      // Set a unique data attribute to identify this image
      imgElement.dataset.itemId = itemId;
    },

    // Update an image with a new thumbnail
    updateThumbnail: function(imgElement, thumbnailUrl, itemId) {
      if (!imgElement || !thumbnailUrl) return;

      // Verify that the image still belongs to the same item
      if (imgElement.dataset.itemId === itemId) {
        // Only update if the current image is a default or different from the new one
        if (imgElement.src.includes('default-') || !imgElement.src.includes(thumbnailUrl)) {
          console.log(`ThumbnailManager: Updating thumbnail for item ${itemId}`);
          imgElement.src = thumbnailUrl;
        }
      } else {
        console.warn(`ThumbnailManager: Image element no longer belongs to item ${itemId}, update skipped`);
      }
    },

    // Queue a thumbnail update for later processing
    queueUpdate: function(imgElement, itemId, thumbnailPromise) {
      if (!imgElement || !itemId || !thumbnailPromise) return;

      // Register the image first
      this.registerImage(imgElement, itemId);

      // Store the pending update
      this.pendingUpdates.set(itemId, {
        imgElement,
        promise: thumbnailPromise
      });

      // Process the update when the promise resolves
      thumbnailPromise.then(thumbnailUrl => {
        if (thumbnailUrl) {
          this.updateThumbnail(imgElement, thumbnailUrl, itemId);
        }
        // Remove from pending updates
        this.pendingUpdates.delete(itemId);
      }).catch(err => {
        console.error(`ThumbnailManager: Error fetching thumbnail for item ${itemId}:`, err);
        this.pendingUpdates.delete(itemId);
      });
    }
  };

  // --- Image Fetching ---
  async function fetchTmdbThumbnail(tmdbId, type) {
      const apiKey = await getTmdbApiKey();
      if (!apiKey || !tmdbId) return null;
      try {
          const tmdbType = (type === 'series' || type === 'anime') ? 'tv' : 'movie';
          const url = `https://api.themoviedb.org/3/${tmdbType}/${tmdbId}?api_key=${apiKey}&language=fr-FR`;
      const tmdbRes = await fetch(url);
      if (!tmdbRes.ok) return null;
      const tmdbData = await tmdbRes.json();
      return tmdbData.poster_path ? `https://image.tmdb.org/t/p/w342${tmdbData.poster_path}` : null;
    } catch (err) { return null; }
  }

  // Fetch TMDB backdrop image for hero backgrounds
  async function fetchTmdbBackdrop(tmdbId, type) {
    const apiKey = await getTmdbApiKey();
    if (!apiKey || !tmdbId) return null;
    try {
      const tmdbType = (type === 'series' || type === 'anime') ? 'tv' : 'movie';
      const url = `https://api.themoviedb.org/3/${tmdbType}/${tmdbId}?api_key=${apiKey}&language=fr-FR`;
      const tmdbRes = await fetch(url);
      if (!tmdbRes.ok) return null;
      const tmdbData = await tmdbRes.json();
      return tmdbData.backdrop_path ? `https://image.tmdb.org/t/p/w1280${tmdbData.backdrop_path}` : null;
    } catch (err) {
      console.error('Error fetching TMDB backdrop:', err);
      return null;
    }
  }

   async function fetchJikanThumbnail(malId) {
       if (!malId) return null;

       try {
         if (window.jikanClient) {
           // Use the rate-limited client
           console.log(`Using JikanClient to fetch thumbnail for anime ${malId}`);
           return await window.jikanClient.getAnimeThumbnail(malId);
         } else {
           console.warn('JikanClient not available, using cached or fallback image');

           // Check if we have a cached version in localStorage
           const cacheKey = `jikan_anime_${malId}`;
           const cached = localStorage.getItem(cacheKey);
           if (cached) {
             try {
               const data = JSON.parse(cached);
               if (data.timestamp && (Date.now() - data.timestamp < 24 * 60 * 60 * 1000)) {
                 console.log(`Using cached thumbnail for anime ${malId}`);
                 return data.thumbnail || null;
               }
             } catch (e) {
               console.error('Error parsing cached data:', e);
             }
           }

           // If no cache or expired, try a direct API call with a timeout
           const controller = new AbortController();
           const timeoutId = setTimeout(() => controller.abort(), 3000);

           try {
             const url = `https://api.jikan.moe/v4/anime/${malId}`;
             const res = await fetch(url, { signal: controller.signal });
             clearTimeout(timeoutId);

             if (!res.ok) return null;

             const data = await res.json();
             const thumbnail = data.data?.images?.jpg?.large_image_url || data.data?.images?.jpg?.image_url || null;

             // Cache the result
             if (thumbnail) {
               localStorage.setItem(cacheKey, JSON.stringify({
                 thumbnail,
                 timestamp: Date.now()
               }));
             }

             return thumbnail;
           } catch (err) {
             clearTimeout(timeoutId);
             console.warn(`Failed to fetch thumbnail for anime ${malId}:`, err.message);
             return null;
           }
         }
       } catch (err) {
         console.error(`Error in fetchJikanThumbnail for ${malId}:`, err);
         return null;
       }
    }

    async function fetchJikanBanner(malId) {
        if (!malId) return null;

        try {
          if (window.jikanClient) {
            // Use the rate-limited client
            console.log(`Using JikanClient to fetch banner for anime ${malId}`);
            return await window.jikanClient.getAnimeBanner(malId);
          } else {
            console.warn('JikanClient not available, using cached or fallback image');

            // Check if we have a cached version in localStorage
            const cacheKey = `jikan_anime_banner_${malId}`;
            const cached = localStorage.getItem(cacheKey);
            if (cached) {
              try {
                const data = JSON.parse(cached);
                if (data.timestamp && (Date.now() - data.timestamp < 24 * 60 * 60 * 1000)) {
                  console.log(`Using cached banner for anime ${malId}`);
                  return data.banner || null;
                }
              } catch (e) {
                console.error('Error parsing cached data:', e);
              }
            }

            // If no cache or expired, try a direct API call with a timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 3000);

            try {
              const url = `https://api.jikan.moe/v4/anime/${malId}/pictures`;
              const res = await fetch(url, { signal: controller.signal });
              clearTimeout(timeoutId);

              if (!res.ok) return null;

              const data = await res.json();
              const pictures = data.data || [];
              const banner = pictures[0]?.jpg?.large_image_url || pictures[0]?.jpg?.image_url || null;

              // Cache the result
              if (banner) {
                localStorage.setItem(cacheKey, JSON.stringify({
                  banner,
                  timestamp: Date.now()
                }));
              }

              return banner;
            } catch (err) {
              clearTimeout(timeoutId);
              console.warn(`Failed to fetch banner for anime ${malId}:`, err.message);
              return null;
            }
          }
        } catch (err) {
          console.error(`Error in fetchJikanBanner for ${malId}:`, err);
          return null;
        }
    }

  // --- Title Logic ---
    // Make this function available globally so it can be used by wishlist.js
    window.getTitleWithState = function(item, sectionType) {
        const baseTitle = item.displayTitle || 'Untitled';
        let details = '';
        const itemType = item.__typename?.toLowerCase() || sectionType;

        if (itemType === 'anime') {
            const language = item.animeLanguage || 'Unknown';
            const season = item.season || '1';

            let latestEpisodeNum = 1; // Default to episode 1

            if (item.episodes?.length > 0) {
                // Get all valid episode numbers, including string numbers
                const episodeNumbers = item.episodes
                    .map(ep => {
                        // Handle both string and number episode numbers
                        const epNum = ep.episodeNumber;
                        if (typeof epNum === 'string') {
                            // Check if it's "Film" or similar
                            if (epNum.toLowerCase() === 'film' || epNum.toLowerCase() === 'movie') {
                                return 'Film';
                            }
                            // Try to parse as number
                            const parsed = parseInt(epNum, 10);
                            return !isNaN(parsed) && parsed > 0 ? parsed : null;
                        } else if (typeof epNum === 'number') {
                            return epNum > 0 ? epNum : null;
                        }
                        return null;
                    })
                    .filter(n => n !== null);

                if (episodeNumbers.length > 0) {
                    // Check if we have any "Film" entries
                    if (episodeNumbers.includes('Film')) {
                        latestEpisodeNum = 'Film';
                    } else {
                        // Get the maximum episode number
                        const numericEpisodes = episodeNumbers.filter(n => typeof n === 'number');
                        if (numericEpisodes.length > 0) {
                            latestEpisodeNum = Math.max(...numericEpisodes);
                        }
                    }
                }
            } else if (item.streamingUrls?.length > 0) {
                // Handle films/OVAs - they have streaming URLs but no episodes
                latestEpisodeNum = 'Film';
            }

            details = ` ${language} <span class="season-episode">S${season}${latestEpisodeNum !== 'Film' ? ':E'+latestEpisodeNum : ''}</span>`;
        } else if (itemType === 'series') {
             const season = item.season || '1';
            const episodes = item.episodes || [];
            const langEpisodes = {}; // { VF: 12, VOSTFR: 10 }
            episodes.forEach(ep => {
                (ep.streamingUrls || []).forEach(stream => {
                    const lang = stream.language?.toUpperCase() || 'UNKNOWN'; // Use UNKNOWN consistently
                    if (!langEpisodes[lang]) langEpisodes[lang] = 0;
                    const epNum = parseInt(ep.episodeNumber, 10);
                    if (!isNaN(epNum) && epNum > langEpisodes[lang]) {
                       langEpisodes[lang] = epNum;
                    }
                });
            });

            const titleParts = Object.entries(langEpisodes)
                                   .sort(([langA], [langB]) => langA.localeCompare(langB)) // Sort languages (e.g., UNKNOWN, VF, VOSTFR)
                                   .map(([lang, latestEpNum]) => `<span class="season-episode">${lang} S${season}:E${latestEpNum}</span>`);

            if (titleParts.length > 0) {
               details = `<br>${titleParts.join('<br>')}`;
            }
        }
        return baseTitle + (details || '');
    }

  // --- Carousel Functions ---
  function createCarouselItem(item, section, options = {}) {
    const itemType = item.__typename?.toLowerCase() || section;
    const carouselItem = document.createElement('div');
    carouselItem.className = 'carousel-item';
    carouselItem.dataset.id = item.id;

    // Determine proper navigation type
    const navigationType = itemType === 'movie' ? 'movies' :
                      itemType === 'series' ? 'series' :
                      itemType === 'anime' ? 'anime' :
                      itemType === 'livetv' ? 'livetv' : section;
    carouselItem.dataset.type = navigationType;

    // Create image element
    const img = document.createElement('img');
    img.alt = item.displayTitle || item.title || 'Item';
    img.loading = 'lazy';

    // Register the image with the thumbnail manager
    thumbnailManager.registerImage(img, item.id);

    // For anime, use the banner class and prioritize banner URL
    if (itemType === 'anime') {
      img.className = 'banner';

      // Prioritize banner URL for anime
      if (item.itemBannerUrl && !item.itemBannerUrl.includes('/default-banner.jpg')) {
        img.src = item.itemBannerUrl;
      } else if (item.itemThumbnailUrl && !item.itemThumbnailUrl.includes('/default-thumbnail.jpg')) {
        img.src = item.itemThumbnailUrl;
      } else if (item.thumbnail && !item.thumbnail.includes('/default-thumbnail.jpg')) {
        img.src = item.thumbnail;
      } else if (item.image) {
        img.src = `/proxy-image?url=${encodeURIComponent(item.image)}`;
      } else {
        img.src = '/default-banner.jpg';
      }
    } else {
      // For non-anime items
      img.className = 'thumbnail';

      // Attempt to get thumbnail
      if (item.thumbnail) {
        img.src = item.thumbnail;
      } else if (item.image) {
        img.src = item.image;
      } else {
        img.src = '/default-thumbnail.jpg';
      }
    }

    // Set appropriate error handler based on item type
    if (itemType === 'anime') {
      img.onerror = function() {
        this.onerror = null;
        this.src = '/default-banner.jpg';
      };
    } else {
      img.onerror = function() {
        this.onerror = null;
        this.src = '/default-thumbnail.jpg';
      };
    }

    // For anime items in recently watched, try to fetch a better image asynchronously
    if (itemType === 'anime' && options.watchedTime &&
        (!item.thumbnail || item.thumbnail.includes('default-thumbnail.jpg') || item.thumbnail.includes('default-banner.jpg'))) {
      // Try to fetch from Jikan first
      if (item.jikan?.mal_id) {
        fetchJikanBanner(item.jikan.mal_id).then(bannerUrl => {
          if (bannerUrl) {
            img.src = bannerUrl;
          }
        }).catch(() => {
          // Ignore errors
        });
      }
      // Then try TMDB as fallback
      else if (item.tmdb?.id) {
        fetchTmdbThumbnail(item.tmdb.id, 'tv').then(thumbnailUrl => {
          if (thumbnailUrl) {
            // Convert to higher resolution
            const highResUrl = thumbnailUrl.replace('w154', 'w500');
            img.src = highResUrl;
          }
        }).catch(() => {
          // Ignore errors
        });
      }
    }

    // Add watched indicator if specified
    if (options.watchedTime) {
      const watchedIndicator = document.createElement('div');
      watchedIndicator.className = 'watched-indicator';
      watchedIndicator.innerHTML = `<i class="fas fa-history"></i> ${options.watchedTime}`;
      carouselItem.appendChild(watchedIndicator);
    }

    // Add progress bar if specified
    if (options.progress && options.progress > 0) {
      const progressBar = document.createElement('div');
      progressBar.className = 'watched-progress';
      progressBar.style.width = `${options.progress}%`;
      carouselItem.appendChild(progressBar);
    }

    // Create title element with proper format (matching the grid items)
    const titleElem = document.createElement('div');
    titleElem.className = 'title';

    // Use the same title formatting as grid items
    const titleHtml = window.getTitleWithState(item, itemType);
    titleElem.innerHTML = titleHtml; // Use innerHTML to support the HTML tags from getTitleWithState

    // Add wishlist button if wishlist manager is available
    if (window.wishlistManager) {
      const isInWishlist = window.wishlistManager.isInWishlist(item.id, navigationType);
      const wishlistButton = document.createElement('button');
      wishlistButton.className = `wishlist-button ${isInWishlist ? 'active' : ''}`;
      wishlistButton.innerHTML = `<i class="${isInWishlist ? 'fas' : 'far'} fa-heart"></i>`;
      wishlistButton.title = isInWishlist ? 'Remove from Wish List' : 'Add to Wish List';
      carouselItem.appendChild(wishlistButton);
    }

    carouselItem.appendChild(img);
    carouselItem.appendChild(titleElem);
    return carouselItem;
  }

  function setupCarouselNavigation(section) {
    const container = document.getElementById(`${section}-trending-carousel`);
    if (!container) return;

    const itemsContainer = container.querySelector('.carousel-items');
    const prevBtn = container.querySelector('.carousel-nav.prev');
    const nextBtn = container.querySelector('.carousel-nav.next');

    if (!itemsContainer || !prevBtn || !nextBtn) return;

    // Clean up existing handlers
    if (prevBtn._scrollHandler) {
      prevBtn.removeEventListener('click', prevBtn._scrollHandler);
    }
    if (nextBtn._scrollHandler) {
      nextBtn.removeEventListener('click', nextBtn._scrollHandler);
    }

    // Set up new navigation button handlers
    prevBtn._scrollHandler = () => {
      itemsContainer.scrollBy({ left: -400, behavior: 'smooth' });
    };

    nextBtn._scrollHandler = () => {
      itemsContainer.scrollBy({ left: 400, behavior: 'smooth' });
    };

    // Attach the new handlers
    prevBtn.addEventListener('click', prevBtn._scrollHandler);
    nextBtn.addEventListener('click', nextBtn._scrollHandler);
  }

  async function populateTrendingCarousel(section) {
    const carouselContainer = document.getElementById(`${section}-trending-carousel`);
    if (!carouselContainer) return;

    const itemsContainer = carouselContainer.querySelector('.carousel-items');
    if (!itemsContainer) return;

    // Complete cleanup of existing items
    itemsContainer.innerHTML = '';

    try {
      // Always fetch trending items specifically for the carousel
      const queryName = section === 'livetv' ? 'liveTV' : section;
      if (section === 'livetv') return; // No trending for liveTV

      const commonFields = `id title displayTitle thumbnail image __typename`;
      const movieFields = `... on Movie { tmdb { id } }`;
      const seriesFields = `... on Series { season episodes { episodeNumber streamingUrls { language } } tmdb { id } }`;
      const animeFields = `... on Anime { season animeLanguage episodes { episodeNumber } streamingUrls { id } jikan { mal_id } tmdb { id } }`;

      let requestedFields = commonFields;
      if (section === 'movies') requestedFields += movieFields;
      else if (section === 'series') requestedFields += seriesFields;
      else if (section === 'anime') requestedFields += animeFields;

      const gqlQuery = `
        query GetTrending${section.charAt(0).toUpperCase() + section.slice(1)}($limit: Int, $sort: SortOption) {
          ${queryName}(limit: $limit, sort: $sort) { ${requestedFields} }
        }`;

      const variables = { limit: 15, sort: 'TRENDING' };
      console.log(`Fetching trending items for ${section} carousel...`);
      const data = await fetchGraphQL(gqlQuery, variables);
      const trendingItems = data?.[queryName] || [];

      console.log(`Home.js: Fetched ${trendingItems.length} trending items for ${section} carousel`);

      if (trendingItems.length === 0) {
        carouselContainer.style.display = 'none';
        return;
      }

      // Store trending items for cache
      if (!window.trendingItemsCache) window.trendingItemsCache = {};
      window.trendingItemsCache[section] = trendingItems;

      // Show the container
      carouselContainer.style.display = 'block';

      // Create and append items with new implementation
      trendingItems.forEach(item => {
        const carouselItem = createCarouselItem(item, section);
        itemsContainer.appendChild(carouselItem);
      });

      // Load thumbnails asynchronously for items with TMDb or Jikan IDs
      trendingItems.forEach((item, index) => {
        const itemType = item.__typename?.toLowerCase() || section;
        const carouselItem = itemsContainer.children[index];
        if (!carouselItem) return;

        const img = carouselItem.querySelector('img');
        if (!img) return;

        // Try to get better thumbnails for items without one
        if (img.src.includes('default-thumbnail.jpg')) {
          let thumbnailPromise;

          if (itemType === 'anime' && item.jikan?.mal_id) {
            thumbnailPromise = fetchJikanThumbnail(item.jikan.mal_id);
          } else if (item.tmdb?.id) {
            thumbnailPromise = fetchTmdbThumbnail(item.tmdb.id, itemType);
          }

          if (thumbnailPromise) {
            // Queue the thumbnail update with the thumbnail manager
            thumbnailManager.queueUpdate(img, item.id, thumbnailPromise);
          }
        }
      });
    } catch (err) {
      console.error(`Home.js: Error populating ${section} trending carousel:`, err);
      carouselContainer.style.display = 'none';
    }
  }

  // Enhanced function to populate the latest movies carousel with infinite scroll
  async function populateLatestMoviesCarousel(reset = false) {
    const carouselContainer = document.getElementById('movies-latest-carousel');
    if (!carouselContainer) return;

    const itemsContainer = carouselContainer.querySelector('.carousel-items');
    if (!itemsContainer) return;

    // Initialize carousel state if not exists or reset requested
    if (!window.latestMoviesCarouselState || reset) {
      window.latestMoviesCarouselState = {
        currentPage: 1,
        isLoading: false,
        hasMoreItems: true,
        loadedItemIds: new Set(), // Track loaded items to prevent duplicates
        totalLoaded: 0
      };
    }

    const state = window.latestMoviesCarouselState;

    // Reset container if requested
    if (reset) {
      itemsContainer.innerHTML = '';
      state.currentPage = 1;
      state.loadedItemIds.clear();
      state.totalLoaded = 0;
      state.hasMoreItems = true;

      // Remove any indicators
      removeCarouselLoadingIndicator(carouselContainer);
      removeNoMoreItemsIndicator(carouselContainer);
    }

    // Prevent multiple simultaneous loads
    if (state.isLoading || !state.hasMoreItems) {
      return;
    }

    state.isLoading = true;

    // Add loading indicator
    addCarouselLoadingIndicator(carouselContainer, !reset);

    try {
      const gqlQuery = `
        query GetLatestMovies($page: Int, $limit: Int, $excludeAncien: Boolean) {
          latestMovies(page: $page, limit: $limit, excludeAncien: $excludeAncien) {
            id title displayTitle thumbnail image __typename
            tmdb { id }
          }
        }
      `;

      const variables = {
        page: state.currentPage,
        limit: 20, // Load 20 items per page
        excludeAncien: true
      };

      console.log(`Fetching latest movies for carousel - Page ${state.currentPage}...`);
      const data = await fetchGraphQL(gqlQuery, variables);
      const latestMovies = data?.latestMovies || [];

      console.log(`Home.js: Fetched ${latestMovies.length} latest movies for carousel (page ${state.currentPage})`);

      // Filter out duplicates
      const newMovies = latestMovies.filter(movie => !state.loadedItemIds.has(movie.id));
      console.log(`Home.js: ${newMovies.length} new movies after duplicate filtering`);

      if (newMovies.length === 0) {
        state.hasMoreItems = false;
        console.log('Home.js: No more new movies to load');
        return;
      }

      // Show the container if hidden
      carouselContainer.style.display = 'block';

      // Create and append new items
      newMovies.forEach(item => {
        // Mark as loaded
        state.loadedItemIds.add(item.id);

        const carouselItem = createCarouselItem(item, 'movies');
        itemsContainer.appendChild(carouselItem);
        state.totalLoaded++;
      });

      // Load thumbnails asynchronously for new items and ensure wishlist buttons
      newMovies.forEach((item, index) => {
        const carouselItem = itemsContainer.children[itemsContainer.children.length - newMovies.length + index];
        if (!carouselItem) return;

        const img = carouselItem.querySelector('img');
        if (!img) return;

        // Try to get better thumbnails for items without one
        if (img.src.includes('default-thumbnail.jpg') && item.tmdb?.id) {
          const thumbnailPromise = fetchTmdbThumbnail(item.tmdb.id, 'movie');
          thumbnailManager.queueUpdate(img, item.id, thumbnailPromise);
        }
      });

      // Ensure wishlist buttons are added to new items (fallback)
      if (window.wishlistManager) {
        setTimeout(() => {
          window.wishlistManager.addWishlistButtons();
        }, 100);
      }

      // Increment page for next load
      state.currentPage++;

      // Check if we should stop loading (if we got fewer items than requested)
      if (latestMovies.length < variables.limit) {
        state.hasMoreItems = false;
        console.log('Home.js: Reached end of latest movies');

        // Add "no more items" indicator
        addNoMoreItemsIndicator(carouselContainer);
      }

      console.log(`Home.js: Latest movies carousel now has ${state.totalLoaded} items`);

    } catch (error) {
      console.error('Home.js: Error populating latest movies carousel:', error);
      if (state.totalLoaded === 0) {
        carouselContainer.style.display = 'none';
      }
    } finally {
      state.isLoading = false;
      removeCarouselLoadingIndicator(carouselContainer);
    }
  }

  // Helper function to add loading indicator to carousel
  function addCarouselLoadingIndicator(carouselContainer, append = false) {
    // Remove existing indicator first
    removeCarouselLoadingIndicator(carouselContainer);

    const itemsContainer = carouselContainer.querySelector('.carousel-items');
    if (!itemsContainer) return;

    const loadingItem = document.createElement('div');
    loadingItem.className = 'carousel-item carousel-loading-item';
    loadingItem.innerHTML = `
      <div class="loading-placeholder">
        <i class="fas fa-spinner fa-spin"></i>
        <div class="loading-text">Loading more...</div>
      </div>
    `;

    if (append) {
      itemsContainer.appendChild(loadingItem);
    } else {
      itemsContainer.insertBefore(loadingItem, itemsContainer.firstChild);
    }
  }

  // Helper function to remove loading indicator from carousel
  function removeCarouselLoadingIndicator(carouselContainer) {
    const loadingItems = carouselContainer.querySelectorAll('.carousel-loading-item');
    loadingItems.forEach(item => item.remove());
  }

  // Helper function to add "no more items" indicator
  function addNoMoreItemsIndicator(carouselContainer) {
    // Remove existing indicator first
    removeNoMoreItemsIndicator(carouselContainer);

    const itemsContainer = carouselContainer.querySelector('.carousel-items');
    if (!itemsContainer) return;

    // Determine which refresh function to use based on carousel ID
    const carouselId = carouselContainer.id;
    let refreshFunction = 'window.refreshLatestMoviesCarousel()';
    if (carouselId === 'movies-ancien-carousel') {
      refreshFunction = 'window.refreshAncienMoviesCarousel()';
    }

    const noMoreItem = document.createElement('div');
    noMoreItem.className = 'carousel-item carousel-no-more-item';
    noMoreItem.innerHTML = `
      <div class="no-more-placeholder">
        <i class="fas fa-check-circle"></i>
        <div class="no-more-text">All caught up!</div>
        <button class="refresh-carousel-btn" onclick="${refreshFunction}">
          <i class="fas fa-sync-alt"></i> Refresh
        </button>
      </div>
    `;

    itemsContainer.appendChild(noMoreItem);
  }

  // Helper function to remove "no more items" indicator
  function removeNoMoreItemsIndicator(carouselContainer) {
    const noMoreItems = carouselContainer.querySelectorAll('.carousel-no-more-item');
    noMoreItems.forEach(item => item.remove());
  }

  // Enhanced function to populate the ancien movies carousel with infinite scroll
  async function populateAncienMoviesCarousel(reset = false) {
    const carouselContainer = document.getElementById('movies-ancien-carousel');
    if (!carouselContainer) return;

    const itemsContainer = carouselContainer.querySelector('.carousel-items');
    if (!itemsContainer) return;

    // Initialize carousel state if not exists or reset requested
    if (!window.ancienMoviesCarouselState || reset) {
      window.ancienMoviesCarouselState = {
        currentPage: 1,
        isLoading: false,
        hasMoreItems: true,
        loadedItemIds: new Set(), // Track loaded items to prevent duplicates
        totalLoaded: 0
      };
    }

    const state = window.ancienMoviesCarouselState;

    // Reset container if requested
    if (reset) {
      itemsContainer.innerHTML = '';
      state.currentPage = 1;
      state.loadedItemIds.clear();
      state.totalLoaded = 0;
      state.hasMoreItems = true;

      // Remove any indicators
      removeCarouselLoadingIndicator(carouselContainer);
      removeNoMoreItemsIndicator(carouselContainer);
    }

    // Prevent multiple simultaneous loads
    if (state.isLoading || !state.hasMoreItems) {
      return;
    }

    state.isLoading = true;

    // Add loading indicator
    addCarouselLoadingIndicator(carouselContainer, !reset);

    try {
      const gqlQuery = `
        query GetAncienMovies($page: Int, $limit: Int) {
          ancienMovies(page: $page, limit: $limit) {
            id title displayTitle thumbnail image __typename
            tmdb { id }
          }
        }
      `;

      const variables = {
        page: state.currentPage,
        limit: 20, // Load 20 items per page
      };

      console.log(`Fetching ancien movies for carousel - Page ${state.currentPage}...`);
      const data = await fetchGraphQL(gqlQuery, variables);
      const ancienMovies = data?.ancienMovies || [];

      console.log(`Home.js: Fetched ${ancienMovies.length} ancien movies for carousel (page ${state.currentPage})`);

      // Filter out duplicates
      const newMovies = ancienMovies.filter(movie => !state.loadedItemIds.has(movie.id));
      console.log(`Home.js: ${newMovies.length} new ancien movies after duplicate filtering`);

      if (newMovies.length === 0) {
        state.hasMoreItems = false;
        console.log('Home.js: No more new ancien movies to load');
        return;
      }

      // Show the container if hidden
      carouselContainer.style.display = 'block';

      // Create and append new items
      newMovies.forEach(item => {
        // Mark as loaded
        state.loadedItemIds.add(item.id);

        const carouselItem = createCarouselItem(item, 'movies');
        itemsContainer.appendChild(carouselItem);
        state.totalLoaded++;
      });

      // Load thumbnails asynchronously for new items and ensure wishlist buttons
      newMovies.forEach((item, index) => {
        const carouselItem = itemsContainer.children[itemsContainer.children.length - newMovies.length + index];
        if (!carouselItem) return;

        const img = carouselItem.querySelector('img');
        if (!img) return;

        // Try to get better thumbnails for items without one
        if (img.src.includes('default-thumbnail.jpg') && item.tmdb?.id) {
          const thumbnailPromise = fetchTmdbThumbnail(item.tmdb.id, 'movie');
          thumbnailManager.queueUpdate(img, item.id, thumbnailPromise);
        }
      });

      // Ensure wishlist buttons are added to new items (fallback)
      if (window.wishlistManager) {
        setTimeout(() => {
          window.wishlistManager.addWishlistButtons();
        }, 100);
      }

      // Increment page for next load
      state.currentPage++;

      // Check if we should stop loading (if we got fewer items than requested)
      if (ancienMovies.length < variables.limit) {
        state.hasMoreItems = false;
        console.log('Home.js: Reached end of ancien movies');

        // Add "no more items" indicator
        addNoMoreItemsIndicator(carouselContainer);
      }

      console.log(`Home.js: Ancien movies carousel now has ${state.totalLoaded} items`);

    } catch (error) {
      console.error('Home.js: Error populating ancien movies carousel:', error);
      if (state.totalLoaded === 0) {
        carouselContainer.style.display = 'none';
      }
    } finally {
      state.isLoading = false;
      removeCarouselLoadingIndicator(carouselContainer);
    }
  }

  // --- Hero Background Management ---
  async function initializeHeroBackgrounds() {
    console.log('Initializing hero backgrounds...');

    const sections = ['movies', 'series', 'anime'];

    for (const section of sections) {
      try {
        await setHeroBackground(section);
      } catch (error) {
        console.error(`Error setting hero background for ${section}:`, error);
      }
    }
  }

  async function setHeroBackground(section) {
    const heroBackground = document.getElementById(`${section}-hero-bg`);
    if (!heroBackground) return;

    console.log(`Setting hero background for ${section}...`);

    try {
      // Get a trending item for this section
      const trendingItem = await fetchRandomTrendingItem(section);
      if (!trendingItem) {
        console.log(`No trending item found for ${section}, using fallback`);
        await setFallbackHeroBackground(section);
        return;
      }

      let backdropUrl = null;

      // Try to get TMDB backdrop
      if (trendingItem.tmdb?.id) {
        backdropUrl = await fetchTmdbBackdrop(trendingItem.tmdb.id, section);
      }

      // Fallback to Jikan for anime if no TMDB backdrop
      if (!backdropUrl && section === 'anime' && trendingItem.jikan?.mal_id) {
        backdropUrl = await fetchJikanBackdrop(trendingItem.jikan.mal_id);
      }

      // Set the background image
      if (backdropUrl) {
        heroBackground.style.backgroundImage = `url(${backdropUrl})`;
        heroBackground.classList.add('loaded');
        console.log(`Hero background set for ${section}:`, backdropUrl);
      } else {
        console.log(`No backdrop found for ${section}, using fallback`);
        await setFallbackHeroBackground(section);
      }

    } catch (error) {
      console.error(`Error setting hero background for ${section}:`, error);
      await setFallbackHeroBackground(section);
    }
  }

  async function fetchRandomTrendingItem(section) {
    try {
      // Use the same query as trending carousels
      const queryName = section === 'livetv' ? 'liveTV' : section;
      if (section === 'livetv') return null;

      // Different fields for different sections
      let commonFields;
      if (section === 'movies') {
        commonFields = `id title tmdb { id }`;
      } else if (section === 'series') {
        commonFields = `id title tmdb { id }`;
      } else if (section === 'anime') {
        commonFields = `id title tmdb { id } jikan { mal_id }`;
      }

      const gqlQuery = `
        query GetTrending${section.charAt(0).toUpperCase() + section.slice(1)}($limit: Int, $sort: SortOption) {
          ${queryName}(limit: $limit, sort: $sort) { ${commonFields} }
        }`;

      const variables = { limit: 5, sort: 'TRENDING' };
      const data = await fetchGraphQL(gqlQuery, variables);
      const trendingItems = data?.[queryName] || [];

      if (trendingItems.length === 0) return null;

      // Return a random item from the first 5 trending items
      const randomIndex = Math.floor(Math.random() * trendingItems.length);
      return trendingItems[randomIndex];

    } catch (error) {
      console.error(`Error fetching trending item for ${section}:`, error);
      return null;
    }
  }

  async function fetchJikanBackdrop(malId) {
    if (!malId) return null;
    try {
      const response = await fetch(`https://api.jikan.moe/v4/anime/${malId}/pictures`);
      if (!response.ok) return null;
      const data = await response.json();

      // Get the largest available image
      const pictures = data.data || [];
      if (pictures.length > 0) {
        return pictures[0].jpg?.large_image_url || pictures[0].jpg?.image_url;
      }
      return null;
    } catch (error) {
      console.error('Error fetching Jikan backdrop:', error);
      return null;
    }
  }

  async function setFallbackHeroBackground(section) {
    const heroBackground = document.getElementById(`${section}-hero-bg`);
    if (!heroBackground) return;

    // Set a subtle gradient based on section
    const gradients = {
      movies: 'linear-gradient(135deg, rgba(33, 150, 243, 0.3) 0%, rgba(13, 71, 161, 0.3) 100%)',
      series: 'linear-gradient(135deg, rgba(76, 175, 80, 0.3) 0%, rgba(27, 94, 32, 0.3) 100%)',
      anime: 'linear-gradient(135deg, rgba(255, 152, 0, 0.3) 0%, rgba(230, 81, 0, 0.3) 100%)'
    };

    heroBackground.style.backgroundImage = gradients[section] || gradients.movies;
    heroBackground.classList.add('loaded');
    console.log(`Fallback background set for ${section}`);
  }

  // --- Recently Watched Carousel ---
  function populateRecentlyWatchedCarousel(section) {
    if (!window.recentlyWatchedManager) return;

    const carouselContainer = document.getElementById(`${section}-recently-watched-carousel`);
    if (!carouselContainer) return;

    const itemsContainer = carouselContainer.querySelector('.carousel-items');
    if (!itemsContainer) return;

    // Clear existing items
    itemsContainer.innerHTML = '';

    // Get recently watched items for this section
    const watchedItems = window.recentlyWatchedManager.watchedItems[section] || [];

    // Hide carousel if no items
    if (watchedItems.length === 0) {
      carouselContainer.style.display = 'none';
      return;
    }

    // Show carousel
    carouselContainer.style.display = 'block';

    // Create items for carousel
    watchedItems.forEach(item => {
      // Create a compatible item object for the carousel
      const carouselItemData = {
        id: item.id,
        title: item.title,
        displayTitle: item.title,
        thumbnail: item.thumbnail,
        image: item.image,
        itemBannerUrl: item.itemBannerUrl,
        itemThumbnailUrl: item.itemThumbnailUrl,
        __typename: section === 'movies' ? 'Movie' : section === 'series' ? 'Series' : section === 'anime' ? 'Anime' : 'LiveTV'
      };

      // For anime, add the necessary season and episode information
      if (section === 'anime') {
        // Add the same properties that are used in the getTitleWithState function
        carouselItemData.season = item.season || '1';
        carouselItemData.animeLanguage = item.animeLanguage || 'VOSTFR';

        // Pass MAL ID and TMDB ID for image fetching
        if (item.malId) {
          carouselItemData.jikan = { mal_id: item.malId };
        }

        if (item.tmdbId) {
          carouselItemData.tmdb = { id: item.tmdbId };
        }

        // If we have episode information, use it
        if (item.episodeNumber) {
          carouselItemData.episodes = [{ episodeNumber: item.episodeNumber }];
        } else if (item.episode) {
          carouselItemData.episodes = [{ episodeNumber: item.episode }];
        } else {
          // Default to episode 1 if no episode information is available
          carouselItemData.episodes = [{ episodeNumber: '1' }];
        }
      }

      // Format the time ago
      const timeAgo = window.recentlyWatchedManager.getTimeAgo(item.timestamp);

      // Create carousel item with watched indicator
      const carouselItem = createCarouselItem(carouselItemData, section, {
        watchedTime: timeAgo,
        progress: item.progress || 0
      });

      itemsContainer.appendChild(carouselItem);
    });

    // Setup navigation for the carousel
    setupCarouselNavigation(`${section}-recently-watched`);
  }

  // Enhanced function to populate the latest series carousel with infinite scroll
  async function populateLatestSeriesCarousel(reset = false) {
    const carouselContainer = document.getElementById('series-latest-carousel');
    if (!carouselContainer) return;

    const itemsContainer = carouselContainer.querySelector('.carousel-items');
    if (!itemsContainer) return;

    // Initialize carousel state if not exists or reset requested
    if (!window.latestSeriesCarouselState || reset) {
      window.latestSeriesCarouselState = {
        currentPage: 1,
        isLoading: false,
        hasMoreItems: true,
        loadedItemIds: new Set(), // Track loaded items to prevent duplicates
        totalLoaded: 0
      };
    }

    const state = window.latestSeriesCarouselState;

    // Reset container if requested
    if (reset) {
      itemsContainer.innerHTML = '';
      state.currentPage = 1;
      state.loadedItemIds.clear();
      state.totalLoaded = 0;
      state.hasMoreItems = true;

      removeCarouselLoadingIndicator(carouselContainer);
      removeNoMoreItemsIndicator(carouselContainer);
    }

    // Prevent multiple simultaneous loads
    if (state.isLoading || !state.hasMoreItems) {
      return;
    }

    state.isLoading = true;
    addCarouselLoadingIndicator(carouselContainer, !reset);

    try {
      const gqlQuery = `
        query GetLatestSeries($page: Int, $limit: Int) {
          latestSeries(page: $page, limit: $limit) {
            id title displayTitle thumbnail image __typename
            season episodes { episodeNumber streamingUrls { language } }
            tmdb { id }
          }
        }
      `;

      const variables = {
        page: state.currentPage,
        limit: 15
      };

      console.log(`Fetching latest series for carousel - Page ${state.currentPage}...`);
      const data = await fetchGraphQL(gqlQuery, variables);
      const latestSeries = data?.latestSeries || [];

      console.log(`Home.js: Fetched ${latestSeries.length} latest series for carousel (page ${state.currentPage})`);

      // Filter out duplicates
      const newSeries = latestSeries.filter(series => !state.loadedItemIds.has(series.id));
      console.log(`Home.js: ${newSeries.length} new series after duplicate filtering`);

      if (newSeries.length === 0) {
        state.hasMoreItems = false;
        console.log('Home.js: No more new series to load');
        return;
      }

      // Show the container if hidden
      carouselContainer.style.display = 'block';

      // Create and append new items
      newSeries.forEach(series => {
        state.loadedItemIds.add(series.id);

        const carouselItem = createCarouselItem(series, 'series');
        itemsContainer.appendChild(carouselItem);
        state.totalLoaded++;
      });

      // Load thumbnails asynchronously for new items
      newSeries.forEach((series, index) => {
        const carouselItem = itemsContainer.children[itemsContainer.children.length - newSeries.length + index];
        if (!carouselItem) return;

        const img = carouselItem.querySelector('img');
        if (!img) return;

        // Try to get better thumbnails for items without one
        if (img.src.includes('default-thumbnail.jpg') && series.tmdb?.id) {
          const thumbnailPromise = fetchTmdbThumbnail(series.tmdb.id, 'tv');
          thumbnailManager.queueUpdate(img, series.id, thumbnailPromise);
        }
      });

      // Ensure wishlist buttons are added to new items
      if (window.wishlistManager) {
        setTimeout(() => {
          window.wishlistManager.addWishlistButtons();
        }, 100);
      }

      // Increment page for next load
      state.currentPage++;

      // Check if we should stop loading (if we got fewer items than requested)
      if (latestSeries.length < variables.limit) {
        state.hasMoreItems = false;
        console.log('Home.js: Reached end of latest series');

        // Add "no more items" indicator
        addNoMoreItemsIndicator(carouselContainer);
      }

      console.log(`Home.js: Latest series carousel now has ${state.totalLoaded} items`);

    } catch (error) {
      console.error('Home.js: Error populating latest series carousel:', error);
      if (state.totalLoaded === 0) {
        carouselContainer.style.display = 'none';
      }
    } finally {
      state.isLoading = false;
      removeCarouselLoadingIndicator(carouselContainer);
    }
  }

  // Enhanced function to populate the latest anime carousel with infinite scroll
  async function populateLatestAnimeCarousel(reset = false) {
    const carouselContainer = document.getElementById('anime-latest-carousel');
    if (!carouselContainer) return;

    const itemsContainer = carouselContainer.querySelector('.carousel-items');
    if (!itemsContainer) return;

    // Initialize carousel state if not exists or reset requested
    if (!window.latestAnimeCarouselState || reset) {
      window.latestAnimeCarouselState = {
        currentPage: 1,
        isLoading: false,
        hasMoreItems: true,
        loadedItemIds: new Set(), // Track loaded items to prevent duplicates
        totalLoaded: 0
      };
    }

    const state = window.latestAnimeCarouselState;

    // Reset container if requested
    if (reset) {
      itemsContainer.innerHTML = '';
      state.currentPage = 1;
      state.loadedItemIds.clear();
      state.totalLoaded = 0;
      state.hasMoreItems = true;

      removeCarouselLoadingIndicator(carouselContainer);
      removeNoMoreItemsIndicator(carouselContainer);
    }

    // Prevent multiple simultaneous loads
    if (state.isLoading || !state.hasMoreItems) {
      return;
    }

    state.isLoading = true;
    addCarouselLoadingIndicator(carouselContainer, !reset);

    try {
      const gqlQuery = `
        query GetLatestAnime($page: Int, $limit: Int) {
          latestAnime(page: $page, limit: $limit) {
            id title displayTitle thumbnail image __typename
            season animeLanguage episodes { episodeNumber }
            streamingUrls { id } jikan { mal_id } tmdb { id }
          }
        }
      `;

      const variables = {
        page: state.currentPage,
        limit: 15
      };

      console.log(`Fetching latest anime for carousel - Page ${state.currentPage}...`);
      const data = await fetchGraphQL(gqlQuery, variables);
      const latestAnime = data?.latestAnime || [];

      console.log(`Home.js: Fetched ${latestAnime.length} latest anime for carousel (page ${state.currentPage})`);

      // Filter out duplicates
      const newAnime = latestAnime.filter(anime => !state.loadedItemIds.has(anime.id));
      console.log(`Home.js: ${newAnime.length} new anime after duplicate filtering`);

      if (newAnime.length === 0) {
        state.hasMoreItems = false;
        console.log('Home.js: No more new anime to load');
        return;
      }

      // Show the container if hidden
      carouselContainer.style.display = 'block';

      // Create and append new items
      newAnime.forEach(anime => {
        state.loadedItemIds.add(anime.id);

        const carouselItem = createCarouselItem(anime, 'anime');
        itemsContainer.appendChild(carouselItem);
        state.totalLoaded++;
      });

      // Load thumbnails asynchronously for new items
      newAnime.forEach((anime, index) => {
        const carouselItem = itemsContainer.children[itemsContainer.children.length - newAnime.length + index];
        if (!carouselItem) return;

        const img = carouselItem.querySelector('img');
        if (!img) return;

        // Try to get better thumbnails for items without one
        if (img.src.includes('default-thumbnail.jpg')) {
          if (anime.jikan?.mal_id) {
            const thumbnailPromise = fetchJikanThumbnail(anime.jikan.mal_id);
            thumbnailManager.queueUpdate(img, anime.id, thumbnailPromise);
          } else if (anime.tmdb?.id) {
            const thumbnailPromise = fetchTmdbThumbnail(anime.tmdb.id, 'tv');
            thumbnailManager.queueUpdate(img, anime.id, thumbnailPromise);
          }
        }
      });

      // Ensure wishlist buttons are added to new items
      if (window.wishlistManager) {
        setTimeout(() => {
          window.wishlistManager.addWishlistButtons();
        }, 100);
      }

      // Increment page for next load
      state.currentPage++;

      // Check if we should stop loading (if we got fewer items than requested)
      if (latestAnime.length < variables.limit) {
        state.hasMoreItems = false;
        console.log('Home.js: Reached end of latest anime');

        // Add "no more items" indicator
        addNoMoreItemsIndicator(carouselContainer);
      }

      console.log(`Home.js: Latest anime carousel now has ${state.totalLoaded} items`);

    } catch (error) {
      console.error('Home.js: Error populating latest anime carousel:', error);
      if (state.totalLoaded === 0) {
        carouselContainer.style.display = 'none';
      }
    } finally {
      state.isLoading = false;
      removeCarouselLoadingIndicator(carouselContainer);
    }
  }



  // Enhanced function to populate the anime movies carousel with infinite scroll
  async function populateAnimeMoviesCarousel(reset = false) {
    const carouselContainer = document.getElementById('anime-movies-carousel');
    if (!carouselContainer) return;

    const itemsContainer = carouselContainer.querySelector('.carousel-items');
    if (!itemsContainer) return;

    // Initialize carousel state if not exists or reset requested
    if (!window.animeMoviesCarouselState || reset) {
      window.animeMoviesCarouselState = {
        currentPage: 1,
        isLoading: false,
        hasMoreItems: true,
        loadedItemIds: new Set(), // Track loaded items to prevent duplicates
        totalLoaded: 0
      };
    }

    const state = window.animeMoviesCarouselState;

    // Reset container if requested
    if (reset) {
      itemsContainer.innerHTML = '';
      state.currentPage = 1;
      state.loadedItemIds.clear();
      state.totalLoaded = 0;
      state.hasMoreItems = true;

      removeCarouselLoadingIndicator(carouselContainer);
      removeNoMoreItemsIndicator(carouselContainer);
    }

    // Prevent multiple simultaneous loads
    if (state.isLoading || !state.hasMoreItems) {
      return;
    }

    state.isLoading = true;
    addCarouselLoadingIndicator(carouselContainer, !reset);

    try {
      const gqlQuery = `
        query GetAnimeMovies($page: Int, $limit: Int) {
          animeMovies(page: $page, limit: $limit) {
            id title displayTitle thumbnail image __typename
            season animeLanguage episodes { episodeNumber }
            streamingUrls { id } jikan { mal_id } tmdb { id }
          }
        }
      `;

      const variables = {
        page: state.currentPage,
        limit: 15
      };

      console.log(`Fetching anime movies for carousel - Page ${state.currentPage}...`);
      const data = await fetchGraphQL(gqlQuery, variables);
      const animeMovies = data?.animeMovies || [];

      console.log(`Home.js: Fetched ${animeMovies.length} anime movies for carousel (page ${state.currentPage})`);

      // Filter out duplicates
      const newAnimeMovies = animeMovies.filter(movie => !state.loadedItemIds.has(movie.id));
      console.log(`Home.js: ${newAnimeMovies.length} new anime movies after duplicate filtering`);

      if (newAnimeMovies.length === 0) {
        state.hasMoreItems = false;
        console.log('Home.js: No more new anime movies to load');
        return;
      }

      // Show the container if hidden
      carouselContainer.style.display = 'block';

      // Create and append new items
      newAnimeMovies.forEach(movie => {
        state.loadedItemIds.add(movie.id);

        const carouselItem = createCarouselItem(movie, 'anime');
        itemsContainer.appendChild(carouselItem);
        state.totalLoaded++;
      });

      // Load thumbnails asynchronously for new items
      newAnimeMovies.forEach((movie, index) => {
        const carouselItem = itemsContainer.children[itemsContainer.children.length - newAnimeMovies.length + index];
        if (!carouselItem) return;

        const img = carouselItem.querySelector('img');
        if (!img) return;

        // Try to get better thumbnails for items without one
        if (img.src.includes('default-thumbnail.jpg')) {
          if (movie.jikan?.mal_id) {
            const thumbnailPromise = fetchJikanThumbnail(movie.jikan.mal_id);
            thumbnailManager.queueUpdate(img, movie.id, thumbnailPromise);
          } else if (movie.tmdb?.id) {
            const thumbnailPromise = fetchTmdbThumbnail(movie.tmdb.id, 'movie');
            thumbnailManager.queueUpdate(img, movie.id, thumbnailPromise);
          }
        }
      });

      // Ensure wishlist buttons are added to new items
      if (window.wishlistManager) {
        setTimeout(() => {
          window.wishlistManager.addWishlistButtons();
        }, 100);
      }

      // Increment page for next load
      state.currentPage++;

      // Check if we should stop loading (if we got fewer items than requested)
      if (animeMovies.length < variables.limit) {
        state.hasMoreItems = false;
        console.log('Home.js: Reached end of anime movies');

        // Add "no more items" indicator
        addNoMoreItemsIndicator(carouselContainer);
      }

      console.log(`Home.js: Anime movies carousel now has ${state.totalLoaded} items`);

    } catch (error) {
      console.error('Home.js: Error populating anime movies carousel:', error);
      if (state.totalLoaded === 0) {
        carouselContainer.style.display = 'none';
      }
    } finally {
      state.isLoading = false;
      removeCarouselLoadingIndicator(carouselContainer);
    }
  }

  // --- Load Section Logic ---
  async function loadSection(section, page = 1, append = false) {
    // Grid items are no longer used for main sections, only for search
    if (['movies', 'series', 'anime'].includes(section)) {
      console.log(`Home.js: Grid items removed for ${section}, only carousels are used`);
      return;
    }

    const list = document.getElementById(`${section}-list`);
    if (!list) {
      console.error(`Home.js: No list element found for section #${section}-list`);
      return;
    }

    if (typeof toggleLoading === 'function') toggleLoading(section, true);

    try {
      const sortSelect = document.getElementById(`${section}-sort`);
      const rawSortValue = sortSelect ? sortSelect.value : ''; // Get current value ("latest", "alpha", etc.)
      let sortValue = null; // Default GraphQL variable to null

      if (sortSelect) { // Only process if a sort dropdown exists for the section
          if (rawSortValue === "") { // Default to LATEST if empty
              sortValue = 'LATEST';
          } else if (rawSortValue) { // Handle "latest", "alpha", "release"
              sortValue = rawSortValue.toUpperCase();
          }
      }
      // Now sortValue is 'LATEST', 'ALPHA', 'RELEASE', or null

      // Always ensure trending carousel is visible, regardless of sort option
      const carouselContainer = document.getElementById(`${section}-trending-carousel`);
      if (carouselContainer && section !== 'livetv' && section !== 'search') {
          carouselContainer.style.display = 'block';
          // Only populate the first time or if it's empty
          if (carouselContainer.querySelector('.carousel-items').children.length === 0) {
              populateTrendingCarousel(section);
          }
      }

      const limit = 40;
      const queryName = section === 'livetv' ? 'liveTV' : section;
      const isLiveTV = section === 'livetv';

      const commonFields = `id title displayTitle thumbnail image __typename`;
      const movieFields = `... on Movie { tmdb { id } }`;
      const seriesFields = `... on Series { season episodes { episodeNumber streamingUrls { language } } tmdb { id } }`;
      const animeFields = `... on Anime { season animeLanguage episodes { episodeNumber } streamingUrls { id } jikan { mal_id } tmdb { id } }`;

      let requestedFields = commonFields;
      if (section === 'movies') requestedFields += movieFields;
      else if (section === 'series') requestedFields += seriesFields;
      else if (section === 'anime') requestedFields += animeFields;

      let querySignature = `($page: Int, $limit: Int`;
      let queryArguments = `page: $page, limit: $limit`;
      if (!isLiveTV) {
          querySignature += `, $sort: SortOption`;
          queryArguments += `, sort: $sort`;
      }
      querySignature += `)`;

      const gqlQuery = `
        query Get${section.charAt(0).toUpperCase() + section.slice(1)}${querySignature} {
          ${queryName}(${queryArguments}) { ${requestedFields} }
        }`;

      const variables = { page, limit };
      if (!isLiveTV && sortValue && ['LATEST', 'ALPHA', 'RELEASE'].includes(sortValue)) {
          variables.sort = sortValue;
      }

      const data = await fetchGraphQL(gqlQuery, variables);
      const items = data?.[queryName] || [];
      console.log(`Home.js: Fetched ${items.length} ${section} items (page ${page})`);

      // Continue with display logic...
      // First, prepare all items with default values
      const itemsWithImages = items.map(item => {
        return {
          ...item,
          itemThumbnailUrl: item.thumbnail || '/default-thumbnail.jpg',
          itemBannerUrl: item.__typename === 'Anime' ? '/default-banner.jpg' : null
        };
      });

      // Render the grid with default images first for better UX
      const renderGridItems = () => {
        const html = itemsWithImages.map(item => {
          const titleHtml = window.getTitleWithState(item, section);
          const dataType = section === 'livetv' ? 'livetv' : section;

          // Determine image source with proper fallbacks
          let imgHtml = '';

          if (item.__typename === 'Anime') {
            // For anime items, try to use banner or thumbnail
            if (item.image) {
              // If we have a direct image URL, use it as a fallback
              imgHtml = `<img src="/proxy-image?url=${encodeURIComponent(item.image)}" loading="lazy" class="banner" alt="${item.displayTitle || item.title}" onerror="this.onerror=null; this.src='/default-banner.jpg';">`;
            } else if (item.thumbnail && !item.thumbnail.includes('/default-thumbnail.jpg')) {
              // If we have a thumbnail, use it as a fallback
              imgHtml = `<img src="${item.thumbnail}" loading="lazy" class="banner" alt="${item.displayTitle || item.title}" onerror="this.onerror=null; this.src='/default-banner.jpg';">`;
            } else {
              // No image available, use placeholder
              imgHtml = '<div class="thumbnail-placeholder banner" style="height: 200px; background: #333;"><i class="fas fa-photo-video" style="font-size: 2em; color: #555;"></i></div>';
            }
          } else {
            // For non-anime items, use thumbnail
            if (item.thumbnail && !item.thumbnail.includes('/default-thumbnail.jpg')) {
              imgHtml = `<img src="${item.thumbnail}" loading="lazy" class="thumbnail" alt="${item.displayTitle || item.title}" onerror="this.onerror=null; this.src='/default-thumbnail.jpg';">`;
            } else if (item.image) {
              imgHtml = `<img src="/proxy-image?url=${encodeURIComponent(item.image)}" loading="lazy" class="thumbnail" alt="${item.displayTitle || item.title}" onerror="this.onerror=null; this.src='/default-thumbnail.jpg';">`;
            } else {
              imgHtml = '<div class="thumbnail-placeholder thumbnail" style="height: 150px; background: #333;"><i class="fas fa-photo-video" style="font-size: 2em; color: #555;"></i></div>';
            }
          }

          // Check if item is in wishlist
          const isInWishlist = window.wishlistManager ? window.wishlistManager.isInWishlist(item.id, dataType) : false;
          const wishlistButton = `<button class="wishlist-button ${isInWishlist ? 'active' : ''}" title="${isInWishlist ? 'Remove from Wish List' : 'Add to Wish List'}">
            <i class="${isInWishlist ? 'fas' : 'far'} fa-heart"></i>
          </button>`;

          // Add more data attributes to help with title formatting
          const seasonAttr = item.season ? `data-season="${item.season}"` : '';
          const langAttr = item.animeLanguage ? `data-anime-language="${item.animeLanguage}"` : '';

          return `
            <div class="grid-item"
                data-id="${item.id}"
                data-type="${dataType}"
                data-title="${(item.displayTitle || item.title || '').replace(/"/g, '&quot;')}"
                data-typename="${item.__typename || ''}"
                ${seasonAttr}
                ${langAttr}>
              ${imgHtml}
              <div class="title">${titleHtml}</div>
              ${wishlistButton}
            </div>`;
        }).join('');

        if (append) {
          list.insertAdjacentHTML('beforeend', html);
        } else {
          list.innerHTML = html;
        }
      };

      // Render immediately with default images
      renderGridItems();

      // Then fetch and update images asynchronously
      const updateImages = async () => {
        // Process items in batches to avoid overwhelming the browser
        const batchSize = 5;
        const batches = Math.ceil(items.length / batchSize);

        for (let batchIndex = 0; batchIndex < batches; batchIndex++) {
          const batchStart = batchIndex * batchSize;
          const batchEnd = Math.min(batchStart + batchSize, items.length);
          const batchItems = items.slice(batchStart, batchEnd);

          // Process this batch
          await Promise.all(batchItems.map(async (item, localIndex) => {
            const globalIndex = batchStart + localIndex;
            let itemThumbnailUrl = item.thumbnail;
            let itemBannerUrl = null;

            // For anime items, use our rate-limited client
            if (item.__typename === 'Anime' && item.jikan?.mal_id) {
              // Fetch banner for anime items
              itemBannerUrl = await fetchJikanBanner(item.jikan.mal_id);

              // If we need a thumbnail too
              if (!itemThumbnailUrl || itemThumbnailUrl.includes('/default-thumbnail.jpg')) {
                itemThumbnailUrl = await fetchJikanThumbnail(item.jikan.mal_id);
              }
            }

            // For other items or as fallback
            if (!itemThumbnailUrl || itemThumbnailUrl.includes('/default-thumbnail.jpg')) {
              if (item.tmdb?.id) {
                itemThumbnailUrl = await fetchTmdbThumbnail(item.tmdb.id, item.__typename?.toLowerCase() || section);
              }
            }

            // Final fallback for thumbnail
            if (!itemThumbnailUrl || itemThumbnailUrl.includes('/default-thumbnail.jpg')) {
              itemThumbnailUrl = item.image ? `/proxy-image?url=${encodeURIComponent(item.image)}` : '/default-thumbnail.jpg';
            }

            // Final fallback for anime banner
            if (item.__typename === 'Anime' && !itemBannerUrl) {
              if (item.tmdb?.id) {
                const highResTmdb = await fetchTmdbThumbnail(item.tmdb.id, 'tv');
                itemBannerUrl = highResTmdb?.replace('w154', 'w500') || itemThumbnailUrl;
              } else {
                itemBannerUrl = itemThumbnailUrl;
              }

              if (!itemBannerUrl || itemBannerUrl.includes('/default-thumbnail.jpg') || itemBannerUrl.includes('w154')) {
                itemBannerUrl = '/default-banner.jpg';
              }
            }

            // Update the item in our array
            itemsWithImages[globalIndex].itemThumbnailUrl = itemThumbnailUrl;
            itemsWithImages[globalIndex].itemBannerUrl = itemBannerUrl;

            // Update the image in the DOM if it exists
            const gridItem = list.querySelectorAll('.grid-item')[globalIndex];
            if (gridItem) {
              const img = gridItem.querySelector('img');
              if (img) {
                // Only update if we have a better image than what's currently shown
                const currentSrc = img.src;
                const isCurrentDefault = currentSrc.includes('/default-') ||
                                        currentSrc.includes('thumbnail-placeholder');

                if (item.__typename === 'Anime') {
                  // For anime, prioritize banner
                  if (itemBannerUrl && !itemBannerUrl.includes('/default-banner.jpg') &&
                      (isCurrentDefault || !currentSrc.includes(itemBannerUrl))) {
                    console.log(`Updating anime banner for ${item.displayTitle || item.title}`);

                    // Use the thumbnail manager to update the image
                    thumbnailManager.updateThumbnail(img, itemBannerUrl, item.id);

                    // Set error handler
                    img.onerror = function() {
                      this.onerror = null;
                      this.src = '/default-banner.jpg';
                    };
                  } else if (itemThumbnailUrl && !itemThumbnailUrl.includes('/default-thumbnail.jpg') &&
                           (isCurrentDefault || !currentSrc.includes(itemThumbnailUrl))) {
                    console.log(`Updating anime thumbnail for ${item.displayTitle || item.title}`);

                    // Use the thumbnail manager to update the image
                    thumbnailManager.updateThumbnail(img, itemThumbnailUrl, item.id);

                    // Set error handler
                    img.onerror = function() {
                      this.onerror = null;
                      this.src = '/default-banner.jpg';
                    };
                  } else if (item.image && isCurrentDefault) {
                    // Try direct image as last resort
                    const proxyUrl = `/proxy-image?url=${encodeURIComponent(item.image)}`;

                    // Use the thumbnail manager to update the image
                    thumbnailManager.updateThumbnail(img, proxyUrl, item.id);

                    // Set error handler
                    img.onerror = function() {
                      this.onerror = null;
                      this.src = '/default-banner.jpg';
                    };
                  }
                } else {
                  // For other types, use thumbnail
                  if (itemThumbnailUrl && !itemThumbnailUrl.includes('/default-thumbnail.jpg') &&
                      (isCurrentDefault || !currentSrc.includes(itemThumbnailUrl))) {
                    console.log(`Updating thumbnail for ${item.displayTitle || item.title}`);

                    // Use the thumbnail manager to update the image
                    thumbnailManager.updateThumbnail(img, itemThumbnailUrl, item.id);
                  } else if (item.image && isCurrentDefault) {
                    // Try direct image as last resort
                    const proxyUrl = `/proxy-image?url=${encodeURIComponent(item.image)}`;

                    // Use the thumbnail manager to update the image
                    thumbnailManager.updateThumbnail(img, proxyUrl, item.id);

                    // Set error handler
                    img.onerror = function() {
                      this.onerror = null;
                      this.src = '/default-thumbnail.jpg';
                    };
                  }
                }
              }
            }
          }));

          // Small delay between batches to avoid overwhelming the browser
          if (batchIndex < batches - 1) {
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        }
      };

      // Start the async image update process
      updateImages();
      if (!loadedSections[section]) loadedSections[section] = {};
      loadedSections[section][page] = true;
      if (items.length === limit) { setupIntersectionObserver(section); }
      else {
         if (window[`observer_${section}`]) window[`observer_${section}`].disconnect();
         // console.log(`Home.js: Reached end for section ${section}, page ${page}`);
      }

    } catch (err) {
      console.error(`Home.js: Load section error (${section}, page ${page}):`, err);
      if (!append) list.innerHTML = `<p class="error-message">Error loading content: ${err.message}</p>`;
      if (window[`observer_${section}`]) window[`observer_${section}`].disconnect();
    } finally {
      if (typeof toggleLoading === 'function') toggleLoading(section, false);
    }
  }

  // --- Search Logic ---
  async function performSearch(query, page = 1, append = false) {
    const list = document.getElementById('search-list');
    if (!list) return;
    if (typeof toggleLoading === 'function') toggleLoading('search', true);

    try {
      const limit = 40;
      const searchFields = `
        items {
          __typename id title displayTitle thumbnail image
          ... on Anime { season animeLanguage episodes { episodeNumber } streamingUrls { id } jikan { mal_id } tmdb { id } }
          ... on Series { season episodes { episodeNumber streamingUrls { language } } tmdb { id } }
          ... on Movie { tmdb { id } }
        }`;
      const gqlQuery = `query SearchItems($query: String!, $page: Int, $limit: Int) { search(query: $query, page: $page, limit: $limit) { ${searchFields} } }`;
      const variables = { query: query || "", page, limit };
      const data = await fetchGraphQL(gqlQuery, variables);
      const items = data?.search?.items || [];
      console.log(`Home.js: Fetched ${items.length} search items (page ${page}) for query "${query}"`);

       const itemsWithImages = await Promise.all(items.map(async (item) => {
           let itemThumbnailUrl = item.thumbnail; let itemBannerUrl = null;
           if (item.__typename === 'Anime' && item.jikan?.mal_id) { itemBannerUrl = await fetchJikanBanner(item.jikan.mal_id); }
           if ((!itemThumbnailUrl || itemThumbnailUrl.includes('/default-thumbnail.jpg'))) {
               if (item.tmdb?.id) { itemThumbnailUrl = await fetchTmdbThumbnail(item.tmdb.id, item.__typename?.toLowerCase()); }
               else if (item.__typename === 'Anime' && item.jikan?.mal_id) { itemThumbnailUrl = await fetchJikanThumbnail(item.jikan.mal_id); }
           }
           if (!itemThumbnailUrl || itemThumbnailUrl.includes('/default-thumbnail.jpg')) { itemThumbnailUrl = item.image ? `/proxy-image?url=${encodeURIComponent(item.image)}` : '/default-thumbnail.jpg'; }
           if (item.__typename === 'Anime' && !itemBannerUrl) {
               if (item.tmdb?.id) { const highResTmdb = await fetchTmdbThumbnail(item.tmdb.id, 'tv'); itemBannerUrl = highResTmdb?.replace('w154', 'w500') || itemThumbnailUrl; }
               else { itemBannerUrl = itemThumbnailUrl; }
               if (!itemBannerUrl || itemBannerUrl.includes('/default-thumbnail.jpg') || itemBannerUrl.includes('w154')) itemBannerUrl = '/default-banner.jpg';
           }
           return { ...item, itemThumbnailUrl, itemBannerUrl };
       }));

      const html = itemsWithImages.length ? itemsWithImages.map(item => {
        const itemTypeLower = item.__typename.toLowerCase();
        const navDataType = itemTypeLower === 'livetv' ? 'livetv' : itemTypeLower === 'movie' ? 'movies' : itemTypeLower === 'series' ? 'series' : itemTypeLower === 'anime' ? 'anime' : 'unknown';
        const titleHtml = window.getTitleWithState(item, itemTypeLower);
        // Check if item is in wishlist
        const isInWishlist = window.wishlistManager ? window.wishlistManager.isInWishlist(item.id, navDataType) : false;
        const wishlistButton = `<button class="wishlist-button ${isInWishlist ? 'active' : ''}" title="${isInWishlist ? 'Remove from Wish List' : 'Add to Wish List'}">
          <i class="${isInWishlist ? 'fas' : 'far'} fa-heart"></i>
        </button>`;

        // Add more data attributes to help with title formatting
        const seasonAttr = item.season ? `data-season="${item.season}"` : '';
        const langAttr = item.animeLanguage ? `data-anime-language="${item.animeLanguage}"` : '';

        return `
          <div class="grid-item"
              data-id="${item.id}"
              data-type="${navDataType}"
              data-title="${(item.displayTitle || item.title || '').replace(/"/g, '&quot;')}"
              data-typename="${item.__typename || ''}"
              ${seasonAttr}
              ${langAttr}>
             ${item.__typename === 'Anime'
              ? (item.itemBannerUrl && !item.itemBannerUrl.includes('/default-banner.jpg')
                  ? `<img src="${item.itemBannerUrl}" loading="lazy" class="banner" alt="${item.displayTitle || item.title} banner" onerror="this.onerror=null; this.src='/default-banner.jpg';">`
                  : (item.itemThumbnailUrl && !item.itemThumbnailUrl.includes('/default-thumbnail.jpg')
                      ? `<img src="${item.itemThumbnailUrl}" loading="lazy" class="banner" alt="${item.displayTitle || item.title} thumb as banner" onerror="this.onerror=null; this.src='/default-banner.jpg';">`
                      : '<div class="thumbnail-placeholder banner" style="height: 100px; background: #333;"><i class="fas fa-photo-video" style="font-size: 2em; color: #555;"></i></div>') )
              : (item.itemThumbnailUrl && !item.itemThumbnailUrl.includes('/default-thumbnail.jpg')
                  ? `<img src="${item.itemThumbnailUrl}" loading="lazy" class="thumbnail" alt="${item.displayTitle || item.title}" onerror="this.onerror=null; this.src='/default-thumbnail.jpg';">`
                  : '<div class="thumbnail-placeholder thumbnail" style="height: 150px; background: #333;"><i class="fas fa-photo-video" style="font-size: 2em; color: #555;"></i></div>') }
            <div class="title">${titleHtml}</div>
            ${wishlistButton}
          </div>`;
      }).join('') : '<p>No results found</p>';

      if (append) { list.insertAdjacentHTML('beforeend', html); }
      else { list.innerHTML = html; }
      if (!loadedSections['search']) loadedSections['search'] = {};
      loadedSections['search'][page] = true;
      if (items.length === limit) { setupIntersectionObserver('search'); }
      else {
         if (window[`observer_search`]) window[`observer_search`].disconnect();
         // console.log(`Home.js: Reached end for search query "${query}", page ${page}`);
      }

    } catch (err) {
      console.error(`Home.js: Search error (query: "${query}", page ${page}):`, err);
       if (!append) list.innerHTML = `<p class="error-message">Error performing search: ${err.message}</p>`;
       if (window[`observer_search`]) window[`observer_search`].disconnect();
    } finally {
        if (typeof toggleLoading === 'function') toggleLoading('search', false);
    }
  }

  // --- Infinite Scroll ---
  const debounceLoadSection = (section, page, searchQuery = null) => {
       if (isFetching) return;
       isFetching = true;
       // console.log(`Home.js: Debouncing load for ${section}, page ${page}`);
       setTimeout(() => {
           let loadPromise;
           if (section === 'search' && searchQuery) {
               loadPromise = performSearch(searchQuery, page, true);
           } else {
               loadPromise = loadSection(section, page, true);
           }
           loadPromise.finally(() => { isFetching = false; });
       }, 150);
   };
   function setupIntersectionObserver(section) {
       // Don't set up observer for main sections (only carousels now)
       if (['movies', 'series', 'anime'].includes(section)) {
         console.log(`Home.js: Grid items removed for ${section}, skipping intersection observer`);
         return;
       }

       const list = document.getElementById(`${section}-list`);
       const lastItem = list?.querySelector('.grid-item:last-child');
       if (!lastItem) return;
       if (window[`observer_${section}`]) window[`observer_${section}`].disconnect();
       const observer = new IntersectionObserver((entries) => {
           if (entries[0].isIntersecting && !isFetching) {
               // Skip main sections (only carousels now)
               if (['movies', 'series', 'anime'].includes(section)) {
                 console.log(`Home.js: Grid items removed for ${section}, stopping intersection observer`);
                 return;
               }

               observer.unobserve(lastItem);
               const loadedPages = loadedSections[section] ? Object.keys(loadedSections[section]).map(Number) : [0];
               const currentPage = Math.max(0, ...loadedPages);
               const nextPage = currentPage + 1;
               console.log(`Home.js: Intersection triggered, loading more ${section}, page ${nextPage}`);
               const searchInput = document.getElementById('search-input');
               const currentSearchQuery = (section === 'search' && searchInput) ? searchInput.value.trim() : null;
               debounceLoadSection(section, nextPage, currentSearchQuery);
           }
       }, { threshold: 0.1 });
       observer.observe(lastItem);
       window[`observer_${section}`] = observer;
   }

  // --- Event Listeners ---
   // Note: Sort controls and grid loading removed for main sections (movies, series, anime)
   // Only carousels are used now for content browsing
   navLinks.forEach(link => {
       link.addEventListener('click', (e) => {
           e.preventDefault();
           const section = link.dataset.section;
           if (!section) return;
           window.location.hash = section; // Trigger hashchange
        });
   });
    document.querySelector('.content').addEventListener('click', (e) => {
       // Check if the click was on a wishlist button or its icon
       const wishlistButton = e.target.closest('.wishlist-button');
       if (wishlistButton) {
           // Prevent default behavior and stop event propagation
           e.preventDefault();
           e.stopPropagation();

           // Get the parent grid item or carousel item
           const itemElement = wishlistButton.closest('.grid-item, .carousel-item');
           if (itemElement && window.wishlistManager) {
               const id = itemElement.dataset.id;
               const type = itemElement.dataset.type;

               if (id && type) {
                   // Get the title element for the title
                   const titleElement = itemElement.querySelector('.title');
                   if (!titleElement) {
                       console.error('No title element found for wishlist item', itemElement);
                       return;
                   }

                   // Get the HTML content to preserve formatting (season/episode info)
                   const title = titleElement.innerHTML.trim();

                   // Get thumbnail
                   const imgElement = itemElement.querySelector('img');
                   const thumbnail = imgElement ? imgElement.src : '/default-thumbnail.jpg';

                   // Create a complete item object with all data from the grid item
                   const item = {
                       id,
                       title,
                       formattedTitle: title,
                       displayTitle: itemElement.dataset.title || '',
                       thumbnail,
                       type,
                       __typename: itemElement.dataset.typename ||
                                  (type === 'movies' ? 'Movie' :
                                   type === 'series' ? 'Series' :
                                   type === 'anime' ? 'Anime' : 'LiveTV'),
                       season: itemElement.dataset.season || '',
                       animeLanguage: itemElement.dataset.animeLanguage || ''
                   };

                   // Toggle wishlist status
                   window.wishlistManager.toggleWishlistItem(item);

                   // Update button state
                   const isInWishlist = window.wishlistManager.isInWishlist(id, type);
                   wishlistButton.classList.toggle('active', isInWishlist);
                   const icon = wishlistButton.querySelector('i');
                   if (icon) {
                       icon.className = isInWishlist ? 'fas fa-heart' : 'far fa-heart';
                   }
                   wishlistButton.title = isInWishlist ? 'Remove from Wish List' : 'Add to Wish List';
               }
           }
           return; // Exit early to prevent navigation
       }

       const gridItem = e.target.closest('.grid-item');
       const isInsidePlayer = e.target.closest('#player-container');
       const isSourceLink = e.target.closest('.source-link');
       const isSortSelect = e.target.closest('select[id$="-sort"]');
       if (gridItem && !isInsidePlayer && !isSourceLink && !isSortSelect) {
           const { id, type } = gridItem.dataset; // type is 'movies', 'series', 'anime', 'livetv'
           if (id && type && ['movies', 'series', 'anime', 'livetv'].includes(type)) {
                const targetPath = `/${type}/${id}`;
                if (window.location.pathname !== targetPath) {
                    // console.log(`Home.js: Grid item clicked, navigating to ${targetPath}`);
                    window.location.href = targetPath;
                }
           } else {
               console.warn('Home.js: Grid item clicked, but missing/invalid id or type dataset.', gridItem.dataset);
           }
       }
   });
   const searchInput = document.getElementById('search-input');
   const searchButton = document.getElementById('search-button');
   const triggerSearch = () => {
       const query = searchInput.value.trim();
       // Trigger UI update for search section via hash change
       window.location.hash = 'search';
       handleHashChange(); // Update UI first

       if (!query) {
           console.log("Home.js: Search triggered with empty query.");
           const list = document.getElementById('search-list');
           if (list) list.innerHTML = '<p>Please enter a search term.</p>';
           return; // Don't perform fetch
       }
       console.log(`Home.js: Search triggered, query: ${query}`);
       delete loadedSections['search']; // Reset loaded state for new search
       if (window[`observer_search`]) window[`observer_search`].disconnect();
       performSearch(query, 1, false); // Perform the actual search
   };
   searchButton.addEventListener('click', triggerSearch);
   searchInput.addEventListener('keypress', (e) => { if (e.key === 'Enter') triggerSearch(); });

    // --- Hash Change Handler ---
    function handleHashChange() {
        const validSections = ['search', 'movies', 'series', 'anime', 'livetv'];
        let currentSection = window.location.hash ? window.location.hash.substring(1) : 'movies';
        if (!validSections.includes(currentSection)) { currentSection = 'movies'; }
        console.log(`Home.js: Hash changed/handled, section: "${currentSection}"`);

        // Update active section display
        sections.forEach(s => s.classList.remove('active'));
        const sectionEl = document.getElementById(currentSection);
        if (sectionEl) sectionEl.classList.add('active');
        else document.getElementById('movies')?.classList.add('active'); // Fallback

        // Update active sidebar link
        navLinks.forEach(l => l.classList.remove('active'));
        document.querySelector(`.sidebar a[data-section="${currentSection}"]`)?.classList.add('active');

        // Handle trending carousel and sort
        if (currentSection !== 'search' && currentSection !== 'livetv') {
            // Make sure sort dropdown is set to "latest"
            const sortSelect = document.getElementById(`${currentSection}-sort`);
            if (sortSelect) {
                // Set to "latest" as default
                sortSelect.value = "latest";
            }

            // Always show trending carousel in content sections
            const carouselContainer = document.getElementById(`${currentSection}-trending-carousel`);
            if (carouselContainer) {
                carouselContainer.style.display = 'block';
                // If carousel is empty, populate it
                if (carouselContainer.querySelector('.carousel-items').children.length === 0) {
                    populateTrendingCarousel(currentSection);
                }
            }
        }

        // Handle search section UI state when hash changes to #search
        if (currentSection === 'search') {
             const searchList = document.getElementById('search-list');
             // If results exist, ensure observer is potentially setup
             if (searchList && searchList.children.length > 0 && searchList.lastElementChild?.classList.contains('grid-item')) {
                 const totalItems = searchList.children.length;
                 if(totalItems % 20 === 0){ setupIntersectionObserver('search'); }
             } else if (searchList && searchList.innerHTML.trim() === '') {
                 // If search list is empty when switching to it, show prompt
                 searchList.innerHTML = '<p>Enter a term above to search.</p>';
             }
        }
        // Note: Main sections (movies, series, anime) no longer load grid items
        // They only use carousels for content browsing
    }
    window.addEventListener('hashchange', handleHashChange);

    // --- Helper: Toggle Loading (defined in script.js, ensure it's loaded first) ---
    // This is just a placeholder, relies on the function in script.js
    function toggleLoading(sectionId, show) {
        const sectionElement = document.getElementById(sectionId);
        if (sectionElement) {
            let indicator = sectionElement.querySelector('.loading-indicator');
            if (!indicator) {
                indicator = document.createElement('div');
                indicator.classList.add('loading-indicator');
                sectionElement.prepend(indicator);
            }
             if (show) indicator.classList.add('active');
             else indicator.classList.remove('active');
        }
    }

    // Function to clean up all carousel-related handlers
    function cleanupCarouselHandlers() {
      console.log('Cleaning up all carousel handlers');

      // Clean up document-level handlers
      if (window.carouselClickHandler) {
        document.removeEventListener('click', window.carouselClickHandler);
        window.carouselClickHandler = null;
      }

      // Clean up section-specific handlers
      ['movies', 'series', 'anime'].forEach(section => {
        const container = document.getElementById(`${section}-trending-carousel`);
        if (!container) return;

        const prevBtn = container.querySelector('.carousel-nav.prev');
        const nextBtn = container.querySelector('.carousel-nav.next');

        if (prevBtn && prevBtn._scrollHandler) {
          prevBtn.removeEventListener('click', prevBtn._scrollHandler);
          prevBtn._scrollHandler = null;
        }

        if (nextBtn && nextBtn._scrollHandler) {
          nextBtn.removeEventListener('click', nextBtn._scrollHandler);
          nextBtn._scrollHandler = null;
        }
      });
    }

  // --- Initial Load ---
  async function initialize() {
    await getTmdbApiKey(); // Fetch API key early

    // Initialize admin grid settings check
    initializeAdminGridSettings();

    // Clean up any existing handlers first
    cleanupCarouselHandlers();

    // Force a complete reset of trending carousels
    window.trendingItemsCache = null; // Clear any cached data
    window.latestMoviesCache = null; // Clear latest movies cache
    window.ancienMoviesCache = null; // Clear ancien movies cache

    // Add a global refresh function that can be called from console if needed
    window.refreshTrendingCarousels = function() {
      console.log("Force refreshing all trending carousels...");
      // Clean up
      cleanupCarouselHandlers();
      // Clear all caches
      window.trendingItemsCache = null;
      window.latestMoviesCache = null;
      window.ancienMoviesCache = null;
      window.latestMoviesCarouselState = null;
      // Reinitialize all carousels
      initializeTrendingCarousels();
    };

    // Add a function to refresh just the latest movies carousel
    window.refreshLatestMoviesCarousel = function() {
      console.log("Refreshing latest movies carousel...");
      window.latestMoviesCarouselState = null;
      populateLatestMoviesCarousel(true);
    };

    // Add a function to refresh just the ancien movies carousel
    window.refreshAncienMoviesCarousel = function() {
      console.log("Refreshing ancien movies carousel...");
      window.ancienMoviesCarouselState = null;
      populateAncienMoviesCarousel(true);
    };

    // Add a function to refresh just the latest series carousel
    window.refreshLatestSeriesCarousel = function() {
      console.log("Refreshing latest series carousel...");
      window.latestSeriesCarouselState = null;
      populateLatestSeriesCarousel(true);
    };

    // Add a function to refresh just the latest anime carousel
    window.refreshLatestAnimeCarousel = function() {
      console.log("Refreshing latest anime carousel...");
      window.latestAnimeCarouselState = null;
      populateLatestAnimeCarousel(true);
    };

    // Add a function to refresh just the anime movies carousel
    window.refreshAnimeMoviesCarousel = function() {
      console.log("Refreshing anime movies carousel...");
      window.animeMoviesCarouselState = null;
      populateAnimeMoviesCarousel(true);
    };

  // Add a function to refresh just the action series carousel
  window.refreshActionSeriesCarousel = function() {
    console.log("Refreshing action series carousel...");
    refreshGenreCarousel('series', 'Action');
  };

    // Initialize genre carousels
    initializeGenreCarousels();

    // Setup latest carousels navigation and populate them
    setupLatestCarousels();

    // Add global function to scrape a section in latest mode
    window.scrapeSectionLatest = async function(section) {
      if (!window.adminManager || !window.adminManager.isAdmin) {
        console.error('Admin access required to scrape');
        return;
      }

      console.log(`Initiating scrape for ${section}`);

      // Map section name to GraphQL ItemType
      const typeMap = {
        'movies': 'MOVIE',
        'series': 'SERIES',
        'anime': 'ANIME',
        'livetv': 'LIVETV'
      };

      const type = typeMap[section];
      if (!type) {
        console.error(`Invalid section: ${section}`);
        return;
      }

      try {
        // Create scrape log modal if it doesn't exist
        if (!document.getElementById('scrape-log-modal')) {
          const modal = document.createElement('div');
          modal.id = 'scrape-log-modal';
          modal.className = 'modal';
          modal.innerHTML = `
            <div class="modal-content scrape-log-content">
              <span class="close">&times;</span>
              <h2>Scrape Log</h2>
              <div class="scrape-status">
                <div class="scrape-spinner"><i class="fas fa-spinner fa-spin"></i></div>
                <div id="scrape-status-message">Starting scrape operation...</div>
              </div>
              <div id="scrape-log" class="scrape-log"></div>
              <div class="modal-buttons">
                <button id="scrape-close" class="button secondary">Close</button>
              </div>
            </div>
          `;
          document.body.appendChild(modal);

          // Add event listeners
          document.querySelector('#scrape-log-modal .close').addEventListener('click', () => {
            document.getElementById('scrape-log-modal').style.display = 'none';
            window.adminManager.closeWebSocket();
          });

          document.getElementById('scrape-close').addEventListener('click', () => {
            document.getElementById('scrape-log-modal').style.display = 'none';
            window.adminManager.closeWebSocket();
          });
        }

        // Clear previous log
        const logElement = document.getElementById('scrape-log');
        logElement.innerHTML = '';

        // Show modal
        document.getElementById('scrape-log-modal').style.display = 'block';
        document.getElementById('scrape-status-message').textContent = `Starting ${section} scrape...`;

        // Reset scrape spinner
        document.querySelector('.scrape-spinner').innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        document.querySelector('.scrape-spinner').classList.remove('success', 'error');

        // Create a dummy URL for the scrape operation
        const dummyUrl = `latest://${section}`;

        // Start the scrape operation
        const response = await fetch('/graphql', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query: `
              mutation ScrapeUrlManually($url: String!, $type: ItemType!, $adminToken: String!) {
                scrapeUrlManually(url: $url, type: $type, adminToken: $adminToken) {
                  success
                  message
                  url
                  type
                  logId
                }
              }
            `,
            variables: {
              url: dummyUrl,
              type: type,
              adminToken: window.adminManager.token
            }
          })
        });

        const result = await response.json();

        if (result.data?.scrapeUrlManually?.success) {
          const logId = result.data.scrapeUrlManually.logId;
          document.getElementById('scrape-status-message').textContent = `${section} scrape operation in progress...`;

          // Connect to WebSocket for live logs
          window.adminManager.connectToWebSocket(logId);
        } else {
          document.getElementById('scrape-status-message').textContent = result.data?.scrapeUrlManually?.message || 'Failed to start scrape';
          window.adminManager.addLogMessage('error', 'Failed to start scrape operation', result.data?.scrapeUrlManually?.message || 'Unknown error');
        }
      } catch (error) {
        console.error(`Error scraping ${section}:`, error);
        document.getElementById('scrape-status-message').textContent = 'Error starting scrape';
        window.adminManager.addLogMessage('error', 'Error starting scrape operation', error.message);
      }
    };

    handleHashChange(); // Initial setup based on current hash (or default)

    // Setup trending carousels for each section
    ['movies', 'series', 'anime'].forEach(section => {
      setupCarouselNavigation(section);
    });

    // Setup latest updates carousels
    setupLatestCarousels();

    // Initialize with a short delay to ensure DOM is ready
    setTimeout(() => {
      window.refreshTrendingCarousels();
    }, 100);

    // Initialize hero backgrounds
    setTimeout(() => {
      initializeHeroBackgrounds();
    }, 500);
  }

  // Initialize the application
  initialize();

  // --- Simple Carousel Implementation ---
  function initializeTrendingCarousels() {
    console.log('Initializing trending carousels');

    // First clean up all existing handlers
    cleanupCarouselHandlers();

    // Create a fresh click handler and store a reference
    window.carouselClickHandler = function(e) {
      // Check if the click was on a wishlist button or its icon
      const wishlistButton = e.target.closest('.wishlist-button');
      if (wishlistButton) {
        // Prevent default behavior and stop event propagation
        e.preventDefault();
        e.stopPropagation();

        // Get the parent carousel item
        const carouselItem = wishlistButton.closest('.carousel-item');
        if (carouselItem && window.wishlistManager) {
          const id = carouselItem.dataset.id;
          const type = carouselItem.dataset.type;

          if (id && type) {
            // Get the title element for the title
            const titleElement = carouselItem.querySelector('.title');
            if (!titleElement) {
              console.error('No title element found for wishlist item', carouselItem);
              return;
            }

            // Get the HTML content to preserve formatting (season/episode info)
            const title = titleElement.innerHTML.trim();

            // Get thumbnail
            const imgElement = carouselItem.querySelector('img');
            const thumbnail = imgElement ? imgElement.src : '/default-thumbnail.jpg';

            // Create a complete item object with all data from the carousel item
            const item = {
              id,
              title,
              formattedTitle: title,
              displayTitle: carouselItem.dataset.title || '',
              thumbnail,
              type,
              __typename: carouselItem.dataset.typename ||
                        (type === 'movies' ? 'Movie' :
                         type === 'series' ? 'Series' :
                         type === 'anime' ? 'Anime' : 'LiveTV'),
              season: carouselItem.dataset.season || '',
              animeLanguage: carouselItem.dataset.animeLanguage || ''
            };

            // Toggle wishlist status
            window.wishlistManager.toggleWishlistItem(item);

            // Update button state
            const isInWishlist = window.wishlistManager.isInWishlist(id, type);
            wishlistButton.classList.toggle('active', isInWishlist);
            const icon = wishlistButton.querySelector('i');
            if (icon) {
              icon.className = isInWishlist ? 'fas fa-heart' : 'far fa-heart';
            }
            wishlistButton.title = isInWishlist ? 'Remove from Wish List' : 'Add to Wish List';
          }
        }
        return; // Exit early to prevent navigation
      }

      // Handle regular carousel item clicks (not wishlist button)
      const carouselItem = e.target.closest('.carousel-item');
      if (carouselItem) {
        const id = carouselItem.dataset.id;
        const type = carouselItem.dataset.type;

        console.log('Carousel item clicked:', {
          target: e.target,
          id: id,
          type: type,
          dataset: carouselItem.dataset,
          href: `/${type}/${id}`
        });

        if (id && type && ['movies', 'series', 'anime', 'livetv'].includes(type)) {
          e.preventDefault();
          e.stopPropagation(); // Prevent other handlers from firing
          const targetPath = `/${type}/${id}`;
          console.log(`Navigating to: ${targetPath}`);
          window.location.href = targetPath;
        } else {
          console.warn('Invalid carousel item click - missing id or type:', { id, type });
        }
      }
    };

    // Attach the handler
    document.addEventListener('click', window.carouselClickHandler);

    // Sections to add carousels to
    const sections = ['movies', 'series', 'anime'];

    sections.forEach(section => {
      const carouselContainer = document.getElementById(`${section}-trending-carousel`);
      if (!carouselContainer) {
        console.error(`Carousel container for ${section} not found`);
        return;
      }

      console.log(`Setting up carousel for ${section}`);

      // Set up navigation
      const itemsContainer = carouselContainer.querySelector('.carousel-items');
      const prevBtn = carouselContainer.querySelector('.carousel-nav.prev');
      const nextBtn = carouselContainer.querySelector('.carousel-nav.next');

      if (!itemsContainer || !prevBtn || !nextBtn) {
        console.error(`Missing carousel elements for ${section}`);
        return;
      }

      // Clear any existing items and event handlers
      itemsContainer.innerHTML = '';

      // Set up navigation buttons with fresh handlers
      prevBtn._scrollHandler = () => {
        console.log(`Scroll left in ${section} carousel`);
        itemsContainer.scrollBy({ left: -400, behavior: 'smooth' });
      };

      nextBtn._scrollHandler = () => {
        console.log(`Scroll right in ${section} carousel`);
        itemsContainer.scrollBy({ left: 400, behavior: 'smooth' });
      };

      prevBtn.addEventListener('click', prevBtn._scrollHandler);
      nextBtn.addEventListener('click', nextBtn._scrollHandler);

      // Populate carousel with fresh data
      populateTrendingCarousel(section);
    });

    // Set up the new movie carousels
    setupMovieCarousel('movies-latest-carousel', populateLatestMoviesCarousel);
    setupMovieCarousel('movies-ancien-carousel', populateAncienMoviesCarousel);
  }

  // Enhanced helper function to set up a movie carousel with infinite scroll
  function setupMovieCarousel(carouselId, populateFunction) {
    const carouselContainer = document.getElementById(carouselId);
    if (!carouselContainer) {
      console.error(`Carousel container for ${carouselId} not found`);
      return;
    }

    console.log(`Setting up carousel for ${carouselId}`);

    // Set up navigation
    const itemsContainer = carouselContainer.querySelector('.carousel-items');
    const prevBtn = carouselContainer.querySelector('.carousel-nav.prev');
    const nextBtn = carouselContainer.querySelector('.carousel-nav.next');

    if (!itemsContainer || !prevBtn || !nextBtn) {
      console.error(`Missing carousel elements for ${carouselId}`);
      return;
    }

    // Clear any existing items and event handlers
    itemsContainer.innerHTML = '';

    // Clean up existing handlers
    if (prevBtn._scrollHandler) {
      prevBtn.removeEventListener('click', prevBtn._scrollHandler);
    }
    if (nextBtn._scrollHandler) {
      nextBtn.removeEventListener('click', nextBtn._scrollHandler);
    }
    if (itemsContainer._scrollHandler) {
      itemsContainer.removeEventListener('scroll', itemsContainer._scrollHandler);
    }

    // Set up navigation buttons with fresh handlers
    prevBtn._scrollHandler = () => {
      console.log(`Scroll left in ${carouselId} carousel`);
      itemsContainer.scrollBy({ left: -400, behavior: 'smooth' });
    };

    nextBtn._scrollHandler = () => {
      console.log(`Scroll right in ${carouselId} carousel`);
      itemsContainer.scrollBy({ left: 400, behavior: 'smooth' });

      // Check if we need to load more items after scrolling right
      setTimeout(() => {
        checkAndLoadMoreItems(carouselId, itemsContainer, populateFunction);
      }, 300); // Wait for scroll animation to complete
    };

    // Set up scroll detection for infinite loading with throttling
    let scrollTimeout;
    itemsContainer._scrollHandler = () => {
      if (scrollTimeout) clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        checkAndLoadMoreItems(carouselId, itemsContainer, populateFunction);
      }, 100); // Throttle scroll events
    };

    prevBtn.addEventListener('click', prevBtn._scrollHandler);
    nextBtn.addEventListener('click', nextBtn._scrollHandler);
    itemsContainer.addEventListener('scroll', itemsContainer._scrollHandler);

    // Populate carousel with initial data
    populateFunction(true); // Reset on initial setup
  }

  // Helper function to check if we need to load more items
  function checkAndLoadMoreItems(carouselId, itemsContainer, populateFunction) {
    // Apply infinite scroll to both latest movies and ancien movies carousels
    if (carouselId !== 'movies-latest-carousel' && carouselId !== 'movies-ancien-carousel') {
      return;
    }

    const scrollLeft = itemsContainer.scrollLeft;
    const scrollWidth = itemsContainer.scrollWidth;
    const clientWidth = itemsContainer.clientWidth;

    // Calculate how close we are to the end (load when 80% scrolled)
    const scrollPercentage = (scrollLeft + clientWidth) / scrollWidth;

    console.log(`${carouselId} scroll: ${Math.round(scrollPercentage * 100)}%`);

    // Load more items when we're 80% through the current items
    if (scrollPercentage > 0.8) {
      console.log(`Near end of ${carouselId}, loading more items...`);
      populateFunction(false); // Don't reset, just add more items
    }
  }

  // Call the function after the page loads
  setTimeout(initializeTrendingCarousels, 500);

  // Initialize recently watched carousels
  function initializeRecentlyWatchedCarousels() {
    console.log('Initializing recently watched carousels');

    if (!window.recentlyWatchedManager) {
      console.log('Recently watched manager not available');
      return;
    }

    // Sections to add recently watched carousels to
    const sections = ['movies', 'series', 'anime'];

    sections.forEach(section => {
      const carouselContainer = document.getElementById(`${section}-recently-watched-carousel`);
      if (!carouselContainer) {
        console.error(`Recently watched carousel container for ${section} not found`);
        return;
      }

      console.log(`Setting up recently watched carousel for ${section}`);

      // Set up navigation
      const itemsContainer = carouselContainer.querySelector('.carousel-items');
      const prevBtn = carouselContainer.querySelector('.carousel-nav.prev');
      const nextBtn = carouselContainer.querySelector('.carousel-nav.next');

      if (!itemsContainer || !prevBtn || !nextBtn) {
        console.error(`Missing recently watched carousel elements for ${section}`);
        return;
      }

      // Clear any existing items
      itemsContainer.innerHTML = '';

      // Set up navigation buttons
      prevBtn._scrollHandler = () => {
        console.log(`Scroll left in ${section} recently watched carousel`);
        itemsContainer.scrollBy({ left: -400, behavior: 'smooth' });
      };

      nextBtn._scrollHandler = () => {
        console.log(`Scroll right in ${section} recently watched carousel`);
        itemsContainer.scrollBy({ left: 400, behavior: 'smooth' });
      };

      prevBtn.addEventListener('click', prevBtn._scrollHandler);
      nextBtn.addEventListener('click', nextBtn._scrollHandler);

      // Populate carousel with recently watched items
      populateRecentlyWatchedCarousel(section);
    });
  }

  // Call the recently watched initialization after a delay
  setTimeout(initializeRecentlyWatchedCarousels, 800);

  // Initialize keyboard navigation
  setTimeout(setupKeyboardNavigation, 1000);

  // Function to handle keyboard navigation for carousels
  function setupKeyboardNavigation() {
    console.log('Setting up keyboard navigation');

    // Remove existing keyboard handler if any
    if (window.keyboardNavigationHandler) {
      document.removeEventListener('keydown', window.keyboardNavigationHandler);
    }

    // Variables to track current focus state
    let activeSection = null;
    let activeCarouselIndex = -1;

    // Function to focus a specific carousel item
    function focusCarouselItem(section, index) {
      // Get the active section's carousel
      const carousel = document.getElementById(`${section}-trending-carousel`);
      if (!carousel) return;

      const itemsContainer = carousel.querySelector('.carousel-items');
      if (!itemsContainer) return;

      const items = itemsContainer.querySelectorAll('.carousel-item');
      if (items.length === 0) return;

      // Constrain index to valid range
      index = Math.max(0, Math.min(items.length - 1, index));

      // Remove focus from all items across all carousels
      document.querySelectorAll('.carousel-item').forEach(item => {
        item.classList.remove('keyboard-focus');
        item.setAttribute('tabindex', '-1');
      });

      // Set focus on the target item
      const targetItem = items[index];
      if (targetItem) {
        targetItem.classList.add('keyboard-focus');
        targetItem.setAttribute('tabindex', '0');
        targetItem.focus();

        // Scroll item into view if needed
        targetItem.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });

        // Update state
        activeSection = section;
        activeCarouselIndex = index;

        console.log(`Focused item: ${section} carousel, index ${index}`);
      }
    }

    // Set up keyboard handler - disabled when remoteManager is available
    window.keyboardNavigationHandler = function(e) {
      // Always skip if remoteManager is active to prevent conflicts
      if (window.remoteManager) {
        return;
      }

      // Only handle navigation when not in an input field
      if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'SELECT') {
        return;
      }

      const sections = ['movies', 'series', 'anime'];

      // If no active carousel yet, set initial focus on the visible section
      if (!activeSection) {
        for (const section of sections) {
          const sectionElement = document.getElementById(section);
          if (sectionElement && sectionElement.classList.contains('active')) {
            // Section is visible, set focus on first carousel item
            activeSection = section;
            focusCarouselItem(section, 0);
            break;
          }
        }
      }

      // If we have an active section, handle navigation
      if (activeSection) {
        const carousel = document.getElementById(`${activeSection}-trending-carousel`);
        if (carousel) {
          const itemsContainer = carousel.querySelector('.carousel-items');
          const items = itemsContainer ? itemsContainer.querySelectorAll('.carousel-item') : [];

          switch (e.key) {
            case 'ArrowLeft':
              // Move focus left
              e.preventDefault();
              focusCarouselItem(activeSection, activeCarouselIndex - 1);
              break;

            case 'ArrowRight':
              // Move focus right
              e.preventDefault();
              focusCarouselItem(activeSection, activeCarouselIndex + 1);
              break;

            case 'ArrowUp':
              // Switch to previous section's carousel
              e.preventDefault();
              const currentIndex = sections.indexOf(activeSection);
              if (currentIndex > 0) {
                const prevSection = sections[currentIndex - 1];
                focusCarouselItem(prevSection, 0);
              }
              break;

            case 'ArrowDown':
              // Switch to next section's carousel
              e.preventDefault();
              const currentSectionIndex = sections.indexOf(activeSection);
              if (currentSectionIndex < sections.length - 1) {
                const nextSection = sections[currentSectionIndex + 1];
                focusCarouselItem(nextSection, 0);
              }
              break;

            case 'Enter':
              // Select the focused item
              if (activeCarouselIndex >= 0) {
                e.preventDefault();
                const activeItem = items[activeCarouselIndex];
                if (activeItem) {
                  const id = activeItem.dataset.id;
                  const type = activeItem.dataset.type;
                  if (id && type && ['movies', 'series', 'anime', 'livetv'].includes(type)) {
                    console.log(`Keyboard selected: ${type}/${id}`);
                    window.location.href = `/${type}/${id}`;
                  }
                }
              }
              break;

            case 'Escape':
              // Clear focus
              document.querySelectorAll('.carousel-item').forEach(item => {
                item.classList.remove('keyboard-focus');
                item.setAttribute('tabindex', '-1');
              });
              activeSection = null;
              activeCarouselIndex = -1;
              break;
          }
        }
      } else if (['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(e.key)) {
        // If no item is focused yet, start navigation with the active section
        for (const section of sections) {
          const sectionElement = document.getElementById(section);
          if (sectionElement && sectionElement.classList.contains('active')) {
            focusCarouselItem(section, 0);
            e.preventDefault();
            break;
          }
        }
      }
    };

    // Attach the keyboard handler - but check if remoteManager is already handling this
    if (!window.remoteManager) {
      console.log('No remoteManager found, attaching home.js keyboard handler');
      document.addEventListener('keydown', window.keyboardNavigationHandler);
    } else {
      console.log('remoteManager is active, skipping home.js keyboard handler');
    }
  }

  // --- Wishlist Event Listener ---
  document.addEventListener('wishlist:updated', (event) => {
    if (event.detail) {
      const { category, isInWishlist, itemId } = event.detail;

      // Update all grid items with this ID
      document.querySelectorAll(`.grid-item[data-id="${itemId}"][data-type="${category}"]`).forEach(item => {
        const wishlistButton = item.querySelector('.wishlist-button');
        if (wishlistButton) {
          wishlistButton.classList.toggle('active', isInWishlist);
          const icon = wishlistButton.querySelector('i');
          if (icon) {
            icon.className = isInWishlist ? 'fas fa-heart' : 'far fa-heart';
          }
          wishlistButton.title = isInWishlist ? 'Remove from Wish List' : 'Add to Wish List';
        }
      });

      // Update all carousel items with this ID
      document.querySelectorAll(`.carousel-item[data-id="${itemId}"][data-type="${category}"]`).forEach(item => {
        const wishlistButton = item.querySelector('.wishlist-button');
        if (wishlistButton) {
          wishlistButton.classList.toggle('active', isInWishlist);
          const icon = wishlistButton.querySelector('i');
          if (icon) {
            icon.className = isInWishlist ? 'fas fa-heart' : 'far fa-heart';
          }
          wishlistButton.title = isInWishlist ? 'Remove from Wish List' : 'Add to Wish List';
        }
      });
    }
  });

  // --- Genre Carousels Implementation ---
  // Client-side cache for genre data
  let genreCache = {
    data: null,
    timestamp: null,
    ttl: 30 * 60 * 1000 // 30 minutes
  };

  // Client-side cache for genre carousel data
  let genreCarouselCache = new Map();
  const GENRE_CAROUSEL_CACHE_TTL = 15 * 60 * 1000; // 15 minutes

  async function initializeGenreCarousels() {
    console.log('Initializing genre carousels');

    // Check if we already have cached genre data
    if (genreCache.data && genreCache.timestamp &&
        (Date.now() - genreCache.timestamp) < genreCache.ttl) {
      console.log('Using cached genre data');
      const genres = genreCache.data;

      // Create genre carousels for each section
      createGenreCarousels('movies', genres.movies.slice(0, 10)); // Show top 10 genres
      createGenreCarousels('series', genres.series.slice(0, 10));
      createGenreCarousels('anime', genres.anime.slice(0, 10));
      return;
    }

    try {
      // Fetch available genres
      const gqlQuery = `
        query GetAvailableGenres {
          availableGenres {
            movies
            series
            anime
          }
        }
      `;

      const data = await fetchGraphQL(gqlQuery);
      const genres = data?.availableGenres;

      if (!genres) {
        console.error('Failed to fetch available genres');
        return;
      }

      console.log('Available genres:', genres);

      // Cache the genre data
      genreCache.data = genres;
      genreCache.timestamp = Date.now();

      // Create genre carousels for each section
      createGenreCarousels('movies', genres.movies.slice(0, 10)); // Show top 10 genres
      createGenreCarousels('series', genres.series.slice(0, 10));
      createGenreCarousels('anime', genres.anime.slice(0, 10));

    } catch (error) {
      console.error('Error initializing genre carousels:', error);
    }
  }

  function createGenreCarousels(section, genres) {
    const container = document.getElementById(`${section}-genre-carousels`);
    if (!container) {
      console.error(`Genre carousel container not found for ${section}`);
      return;
    }

    // Clear existing carousels
    container.innerHTML = '';

    genres.forEach(genre => {
      const carouselId = `${section}-${genre.toLowerCase().replace(/[^a-z0-9]/g, '-')}-carousel`;

      const carouselHTML = `
        <div class="trending-carousel-container genre-carousel" id="${carouselId}">
          <div class="carousel-header">
            <h3>${genre} ${section.charAt(0).toUpperCase() + section.slice(1)}</h3>
            <button class="carousel-refresh-btn" onclick="refreshGenreCarousel('${section}', '${genre}')" title="Refresh ${genre} ${section}">
              <i class="fas fa-sync-alt"></i>
            </button>
          </div>
          <div class="carousel-navigation">
            <button class="carousel-nav prev" aria-label="Previous"><i class="fas fa-chevron-left"></i></button>
            <div class="carousel-items"></div>
            <button class="carousel-nav next" aria-label="Next"><i class="fas fa-chevron-right"></i></button>
          </div>
        </div>
      `;

      container.insertAdjacentHTML('beforeend', carouselHTML);

      // Set up the carousel
      setupGenreCarousel(carouselId, section, genre);
    });
  }

  function setupGenreCarousel(carouselId, section, genre) {
    const carouselContainer = document.getElementById(carouselId);
    if (!carouselContainer) {
      console.error(`Carousel container for ${carouselId} not found`);
      return;
    }

    console.log(`Setting up genre carousel for ${section} - ${genre}`);

    // Set up navigation
    const itemsContainer = carouselContainer.querySelector('.carousel-items');
    const prevBtn = carouselContainer.querySelector('.carousel-nav.prev');
    const nextBtn = carouselContainer.querySelector('.carousel-nav.next');

    if (!itemsContainer || !prevBtn || !nextBtn) {
      console.error(`Missing carousel elements for ${carouselId}`);
      return;
    }

    // Clean up existing handlers
    if (prevBtn._scrollHandler) {
      prevBtn.removeEventListener('click', prevBtn._scrollHandler);
    }
    if (nextBtn._scrollHandler) {
      nextBtn.removeEventListener('click', nextBtn._scrollHandler);
    }
    if (itemsContainer._scrollHandler) {
      itemsContainer.removeEventListener('scroll', itemsContainer._scrollHandler);
    }

    // Set up navigation buttons
    prevBtn._scrollHandler = () => {
      console.log(`Scroll left in ${carouselId} carousel`);
      itemsContainer.scrollBy({ left: -400, behavior: 'smooth' });
    };

    nextBtn._scrollHandler = () => {
      console.log(`Scroll right in ${carouselId} carousel`);
      itemsContainer.scrollBy({ left: 400, behavior: 'smooth' });

      // Check if we need to load more items after scrolling right
      setTimeout(() => {
        checkAndLoadMoreGenreItems(carouselId, itemsContainer, section, genre);
      }, 300);
    };

    // Set up scroll detection for infinite loading
    let scrollTimeout;
    itemsContainer._scrollHandler = () => {
      if (scrollTimeout) clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        checkAndLoadMoreGenreItems(carouselId, itemsContainer, section, genre);
      }, 100);
    };

    prevBtn.addEventListener('click', prevBtn._scrollHandler);
    nextBtn.addEventListener('click', nextBtn._scrollHandler);
    itemsContainer.addEventListener('scroll', itemsContainer._scrollHandler);

    // Populate carousel with initial data
    populateGenreCarousel(carouselId, section, genre, true);
  }

  async function populateGenreCarousel(carouselId, section, genre, reset = false) {
    const carouselContainer = document.getElementById(carouselId);
    if (!carouselContainer) return;

    const itemsContainer = carouselContainer.querySelector('.carousel-items');
    if (!itemsContainer) return;

    // Initialize carousel state
    const stateKey = `${carouselId}State`;
    if (!window[stateKey] || reset) {
      window[stateKey] = {
        currentPage: 1,
        isLoading: false,
        hasMoreItems: true,
        loadedItemIds: new Set(),
        totalLoaded: 0
      };
    }

    const state = window[stateKey];

    // Reset container if requested
    if (reset) {
      itemsContainer.innerHTML = '';
      state.currentPage = 1;
      state.loadedItemIds.clear();
      state.totalLoaded = 0;
      state.hasMoreItems = true;

      removeCarouselLoadingIndicator(carouselContainer);
      removeNoMoreItemsIndicator(carouselContainer);
    }

    // Check cache for first page only
    if (state.currentPage === 1 && !reset) {
      const cacheKey = `${section}-${genre}-page-1`;
      const cachedData = genreCarouselCache.get(cacheKey);

      if (cachedData && (Date.now() - cachedData.timestamp) < GENRE_CAROUSEL_CACHE_TTL) {
        console.log(`Using cached data for ${cacheKey}`);

        // Clear existing items
        itemsContainer.innerHTML = '';
        state.loadedItemIds.clear();
        state.totalLoaded = 0;

        // Add cached items
        cachedData.items.forEach(item => {
          if (!state.loadedItemIds.has(item.id)) {
            const carouselItem = createCarouselItem(item, section);
            itemsContainer.appendChild(carouselItem);
            state.loadedItemIds.add(item.id);
            state.totalLoaded++;
          }
        });

        state.currentPage++;
        if (cachedData.items.length < 15) {
          state.hasMoreItems = false;
          addNoMoreItemsIndicator(carouselContainer);
        }

        // Show the container
        carouselContainer.style.display = 'block';

        // Ensure wishlist buttons are added
        if (window.wishlistManager) {
          setTimeout(() => {
            window.wishlistManager.addWishlistButtons();
          }, 100);
        }

        return;
      }
    }

    // Prevent multiple simultaneous loads
    if (state.isLoading || !state.hasMoreItems) {
      return;
    }

    state.isLoading = true;
    addCarouselLoadingIndicator(carouselContainer, !reset);

    try {
      // Determine the correct GraphQL query based on section
      let gqlQuery, queryName;
      switch (section) {
        case 'movies':
          queryName = 'moviesByGenre';
          gqlQuery = `
            query GetMoviesByGenre($genre: String!, $page: Int, $limit: Int) {
              moviesByGenre(genre: $genre, page: $page, limit: $limit) {
                id title displayTitle thumbnail image __typename
                tmdb { id }
              }
            }
          `;
          break;
        case 'series':
          queryName = 'seriesByGenre';
          gqlQuery = `
            query GetSeriesByGenre($genre: String!, $page: Int, $limit: Int) {
              seriesByGenre(genre: $genre, page: $page, limit: $limit) {
                id title displayTitle thumbnail image __typename
                tmdb { id }
              }
            }
          `;
          break;
        case 'anime':
          queryName = 'animeByGenre';
          gqlQuery = `
            query GetAnimeByGenre($genre: String!, $page: Int, $limit: Int) {
              animeByGenre(genre: $genre, page: $page, limit: $limit) {
                id title displayTitle thumbnail image __typename
                season animeLanguage episodes { episodeNumber }
                streamingUrls { id } jikan { mal_id } tmdb { id }
              }
            }
          `;
          break;
        default:
          throw new Error(`Unknown section: ${section}`);
      }

      const variables = {
        genre: genre,
        page: state.currentPage,
        limit: 15
      };

      console.log(`Fetching ${section} by genre ${genre} - Page ${state.currentPage}...`);
      const data = await fetchGraphQL(gqlQuery, variables);
      const items = data?.[queryName] || [];

      console.log(`Fetched ${items.length} ${section} for genre ${genre} (page ${state.currentPage})`);

      // Cache first page results
      if (state.currentPage === 1 && items.length > 0) {
        const cacheKey = `${section}-${genre}-page-1`;
        genreCarouselCache.set(cacheKey, {
          items: items,
          timestamp: Date.now()
        });
        console.log(`Cached data for ${cacheKey}`);
      }

      // Filter out duplicates
      const newItems = items.filter(item => !state.loadedItemIds.has(item.id));
      console.log(`${newItems.length} new ${section} after duplicate filtering`);

      if (newItems.length === 0) {
        state.hasMoreItems = false;
        console.log(`No more new ${section} for genre ${genre}`);
        return;
      }

      // Show the container if hidden
      carouselContainer.style.display = 'block';

      // Create and append new items
      newItems.forEach(item => {
        state.loadedItemIds.add(item.id);

        const carouselItem = createCarouselItem(item, section);
        itemsContainer.appendChild(carouselItem);
        state.totalLoaded++;
      });

      // Load thumbnails asynchronously for new items
      newItems.forEach((item, index) => {
        const carouselItem = itemsContainer.children[itemsContainer.children.length - newItems.length + index];
        if (!carouselItem) return;

        const img = carouselItem.querySelector('img');
        if (!img) return;

        // Try to get better thumbnails for items without one
        if (img.src.includes('default-thumbnail.jpg')) {
          if (section === 'anime' && item.jikan?.mal_id) {
            const thumbnailPromise = fetchJikanThumbnail(item.jikan.mal_id);
            thumbnailManager.queueUpdate(img, item.id, thumbnailPromise);
          } else if ((section === 'movies' || section === 'series') && item.tmdb?.id) {
            const thumbnailPromise = fetchTmdbThumbnail(item.tmdb.id, section === 'movies' ? 'movie' : 'tv');
            thumbnailManager.queueUpdate(img, item.id, thumbnailPromise);
          }
        }
      });

      // Ensure wishlist buttons are added to new items
      if (window.wishlistManager) {
        setTimeout(() => {
          window.wishlistManager.addWishlistButtons();
        }, 100);
      }

      // Increment page for next load
      state.currentPage++;

      // Check if we should stop loading
      if (items.length < variables.limit) {
        state.hasMoreItems = false;
        console.log(`Reached end of ${section} for genre ${genre}`);
        addNoMoreItemsIndicator(carouselContainer);
      }

      console.log(`${section} genre carousel for ${genre} now has ${state.totalLoaded} items`);

    } catch (error) {
      console.error(`Error populating ${section} genre carousel for ${genre}:`, error);
      if (state.totalLoaded === 0) {
        carouselContainer.style.display = 'none';
      }
    } finally {
      state.isLoading = false;
      removeCarouselLoadingIndicator(carouselContainer);
    }
  }

  function checkAndLoadMoreGenreItems(carouselId, itemsContainer, section, genre) {
    const scrollLeft = itemsContainer.scrollLeft;
    const scrollWidth = itemsContainer.scrollWidth;
    const clientWidth = itemsContainer.clientWidth;

    // Calculate how close we are to the end (load when 80% scrolled)
    const scrollPercentage = (scrollLeft + clientWidth) / scrollWidth;

    console.log(`${carouselId} scroll: ${Math.round(scrollPercentage * 100)}%`);

    // Load more items when we're 80% through the current items
    if (scrollPercentage > 0.8) {
      console.log(`Near end of ${carouselId}, loading more items...`);
      populateGenreCarousel(carouselId, section, genre, false);
    }
  }

  // Function to clear genre carousel cache
  function clearGenreCarouselCache(section = null, genre = null) {
    if (section && genre) {
      // Clear specific cache entry
      const cacheKey = `${section}-${genre}-page-1`;
      genreCarouselCache.delete(cacheKey);
      console.log(`Cleared cache for ${cacheKey}`);
    } else {
      // Clear all cache
      genreCarouselCache.clear();
      console.log('Cleared all genre carousel cache');
    }
  }

  // Global function to refresh genre carousels
  window.refreshGenreCarousel = function(section, genre) {
    const carouselId = `${section}-${genre.toLowerCase().replace(/[^a-z0-9]/g, '-')}-carousel`;
    console.log(`Refreshing genre carousel: ${carouselId}`);

    // Clear cache for this specific genre
    clearGenreCarouselCache(section, genre);

    // Reset state
    const stateKey = `${carouselId}State`;
    window[stateKey] = null;

    // Repopulate
    populateGenreCarousel(carouselId, section, genre, true);
  };

  // Global function to clear all genre caches
  window.clearGenreCache = function() {
    genreCache.data = null;
    genreCache.timestamp = null;
    clearGenreCarouselCache();
    console.log('Cleared all genre caches');
  };

  // --- Latest Carousels Setup ---
  function setupLatestCarousels() {
    console.log('Setting up latest carousels...');

    // Setup latest series carousel
    const seriesLatestCarousel = document.getElementById('series-latest-carousel');
    if (seriesLatestCarousel) {
      setupLatestCarouselNavigation('series-latest-carousel');

      // Set up infinite scroll for series latest carousel
      const seriesItemsContainer = seriesLatestCarousel.querySelector('.carousel-items');
      if (seriesItemsContainer) {
        let scrollTimeout;
        seriesItemsContainer.addEventListener('scroll', () => {
          if (scrollTimeout) clearTimeout(scrollTimeout);
          scrollTimeout = setTimeout(() => {
            checkAndLoadMoreLatestItems('series', seriesItemsContainer);
          }, 100);
        });
      }

      // Populate initially
      populateLatestSeriesCarousel(true);
    }

    // Setup action series carousel using genre carousel setup
    const actionSeriesCarousel = document.getElementById('series-action-carousel');
    if (actionSeriesCarousel) {
      setupGenreCarousel('series-action-carousel', 'series', 'Action');
    }

    // Setup latest anime carousel
    const animeLatestCarousel = document.getElementById('anime-latest-carousel');
    if (animeLatestCarousel) {
      setupLatestCarouselNavigation('anime-latest-carousel');

      // Set up infinite scroll for anime latest carousel
      const animeItemsContainer = animeLatestCarousel.querySelector('.carousel-items');
      if (animeItemsContainer) {
        let scrollTimeout;
        animeItemsContainer.addEventListener('scroll', () => {
          if (scrollTimeout) clearTimeout(scrollTimeout);
          scrollTimeout = setTimeout(() => {
            checkAndLoadMoreLatestItems('anime', animeItemsContainer);
          }, 100);
        });
      }

      // Populate initially
      populateLatestAnimeCarousel(true);
    }

    // Setup anime movies carousel
    const animeMoviesCarousel = document.getElementById('anime-movies-carousel');
    if (animeMoviesCarousel) {
      setupLatestCarouselNavigation('anime-movies-carousel');

      // Set up infinite scroll for anime movies carousel
      const animeMoviesItemsContainer = animeMoviesCarousel.querySelector('.carousel-items');
      if (animeMoviesItemsContainer) {
        let scrollTimeout;
        animeMoviesItemsContainer.addEventListener('scroll', () => {
          if (scrollTimeout) clearTimeout(scrollTimeout);
          scrollTimeout = setTimeout(() => {
            checkAndLoadMoreLatestItems('anime-movies', animeMoviesItemsContainer);
          }, 100);
        });
      }

      // Populate initially
      populateAnimeMoviesCarousel(true);
    }
  }

  // Setup navigation for latest carousels (different from trending carousels)
  function setupLatestCarouselNavigation(carouselId) {
    const container = document.getElementById(carouselId);
    if (!container) {
      console.error(`Latest carousel container ${carouselId} not found`);
      return;
    }

    const itemsContainer = container.querySelector('.carousel-items');
    const prevBtn = container.querySelector('.carousel-nav.prev');
    const nextBtn = container.querySelector('.carousel-nav.next');

    if (!itemsContainer || !prevBtn || !nextBtn) {
      console.error(`Missing carousel elements for ${carouselId}`);
      return;
    }

    console.log(`Setting up navigation for latest carousel: ${carouselId}`);

    // Clean up existing handlers
    if (prevBtn._scrollHandler) {
      prevBtn.removeEventListener('click', prevBtn._scrollHandler);
    }
    if (nextBtn._scrollHandler) {
      nextBtn.removeEventListener('click', nextBtn._scrollHandler);
    }
    if (itemsContainer._scrollHandler) {
      itemsContainer.removeEventListener('scroll', itemsContainer._scrollHandler);
    }

    // Set up new navigation button handlers
    prevBtn._scrollHandler = () => {
      console.log(`Scroll left in ${carouselId} carousel`);
      itemsContainer.scrollBy({ left: -400, behavior: 'smooth' });
    };

    nextBtn._scrollHandler = () => {
      console.log(`Scroll right in ${carouselId} carousel`);
      itemsContainer.scrollBy({ left: 400, behavior: 'smooth' });

      // Check if we need to load more items after scrolling right
      setTimeout(() => {
        let section;
        if (carouselId.includes('series')) {
          section = 'series';
        } else if (carouselId.includes('anime-movies')) {
          section = 'anime-movies';
        } else if (carouselId.includes('anime')) {
          section = 'anime';
        }
        checkAndLoadMoreLatestItems(section, itemsContainer);
      }, 300);
    };

    prevBtn.addEventListener('click', prevBtn._scrollHandler);
    nextBtn.addEventListener('click', nextBtn._scrollHandler);

    console.log(`Navigation setup complete for ${carouselId}`);
  }

  function checkAndLoadMoreLatestItems(section, itemsContainer) {
    const scrollLeft = itemsContainer.scrollLeft;
    const scrollWidth = itemsContainer.scrollWidth;
    const clientWidth = itemsContainer.clientWidth;

    // Calculate how close we are to the end (load when 80% scrolled)
    const scrollPercentage = (scrollLeft + clientWidth) / scrollWidth;

    console.log(`${section} latest carousel scroll: ${Math.round(scrollPercentage * 100)}%`);

    // Load more items when we're 80% through the current items
    if (scrollPercentage > 0.8) {
      console.log(`Near end of ${section} latest carousel, loading more items...`);
      if (section === 'series') {
        populateLatestSeriesCarousel(false);
      } else if (section === 'anime') {
        populateLatestAnimeCarousel(false);
      } else if (section === 'anime-movies') {
        populateAnimeMoviesCarousel(false);
      }
    }
  }

  // --- Admin Grid Settings Functions ---
  async function initializeAdminGridSettings() {
    try {
      // First try to get from server if admin is logged in
      let isEnabled = true; // Default to enabled

      if (window.adminManager && window.adminManager.isAdmin && window.adminManager.token) {
        try {
          const settings = await window.adminManager.fetchDisplaySettings();
          isEnabled = settings.gridItemsEnabled;
          console.log('Fetched grid items setting from server:', isEnabled);
        } catch (error) {
          console.log('Could not fetch from server, falling back to localStorage:', error.message);
          // Fallback to localStorage
          const gridItemsEnabled = localStorage.getItem('admin_grid_items_enabled');
          isEnabled = gridItemsEnabled === null ? true : gridItemsEnabled === 'true';
        }
      } else {
        // Not logged in as admin, check localStorage
        const gridItemsEnabled = localStorage.getItem('admin_grid_items_enabled');
        isEnabled = gridItemsEnabled === null ? true : gridItemsEnabled === 'true';
      }

      // Store the setting globally
      window.adminGridItemsEnabled = isEnabled;

      console.log('Admin grid items enabled:', isEnabled);

      // Apply the initial setting
      if (!isEnabled) {
        // If disabled, hide filter controls immediately
        setTimeout(() => {
          hideFilterControls();
        }, 100);
      }

      // Listen for admin grid toggle events
      document.addEventListener('admin:gridItemsToggled', (event) => {
        const enabled = event.detail.enabled;
        window.adminGridItemsEnabled = enabled;
        console.log('Home.js: Grid items toggled:', enabled);

        // Apply the setting immediately
        if (!enabled) {
          clearAllGridItems();
          hideFilterControls();
        } else {
          showFilterControls();
          // Don't reload grid items automatically - let user navigate to trigger loading
        }
      });
    } catch (error) {
      console.error('Error initializing admin grid settings:', error);
      // Default to enabled on error
      window.adminGridItemsEnabled = true;
    }
  }

  function clearAllGridItems() {
    // Grid items have been removed from main sections
    // Only search results use grid items now
    console.log('Home.js: Grid items have been removed from main sections (movies, series, anime)');
    console.log('Home.js: Only carousels are used for content browsing in main sections');

    // Clear search results if needed
    const searchContainer = document.getElementById('search-list');
    if (searchContainer) {
      searchContainer.innerHTML = '<p>Enter a term above to search.</p>';
      console.log('Home.js: Cleared search results');
    }

    // Reset loaded sections for search only
    if (loadedSections['search']) {
      loadedSections['search'] = {};
    }
  }

  function hideFilterControls() {
    // Filter controls have been completely removed from HTML
    console.log('Home.js: Filter controls have been permanently removed from main sections');
    console.log('Home.js: Sort menus are no longer needed since only carousels are used');
  }

  function showFilterControls() {
    // Filter controls have been completely removed from HTML
    console.log('Home.js: Filter controls have been permanently removed from main sections');
    console.log('Home.js: Sort menus are no longer available since only carousels are used');
  }

  function isGridItemsEnabled() {
    // Check if admin has disabled grid items
    return window.adminGridItemsEnabled !== false;
  }

});