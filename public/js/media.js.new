// File: public/js/media.js
// COMPLETE CODE - Fix for Jikan field placement

console.log('Media.js script loaded');
document.addEventListener('DOMContentLoaded', async () => {
  try {
    const playerContainer = document.getElementById('player-container');
    let player = document.getElementById('player');
    let playerIframe = document.getElementById('player-iframe');
    const seasonsContainer = document.getElementById('seasons');
    const episodesDiv = document.getElementById('episodes');
    const providersDiv = document.getElementById('providers');
    const banner = document.getElementById('banner');
    const mediaTitle = document.getElementById('media-title');
    const synopsisElem = document.getElementById('media-synopsis');

    // --- Robust Element Checks ---
    const requiredElements = { mediaTitle, banner, seasonsContainer, episodesDiv, providersDiv, playerContainer, synopsisElem };
    const missingElements = Object.entries(requiredElements).filter(([_, el]) => !el).map(([key]) => key);
    if (missingElements.length > 0) {
        console.error('Missing critical DOM elements:', missingElements);
        document.body.innerHTML = `<p style="color: red; padding: 20px;">Error: Page structure incomplete (${missingElements.join(', ')} missing). Cannot load media details.</p>`;
        return;
    }
    // --- End Element Checks ---

    console.log('Initial playerContainer classes:', playerContainer.className);
    playerContainer.classList.add('hidden');

    // --- URL Parsing and Validation ---
    const path = window.location.pathname.split('/');
    const typeSlug = path[1]?.toLowerCase();
    const itemId = path[2];
    let itemType;

    switch (typeSlug) {
        case 'movies': itemType = 'MOVIE'; break;
        case 'series': itemType = 'SERIES'; break;
        case 'anime': itemType = 'ANIME'; break;
        case 'livetv': itemType = 'LIVETV'; break;
        default: itemType = null;
    }

    if (!itemType) {
        console.error(`Invalid item type derived from URL slug: "${typeSlug}"`);
         document.body.innerHTML = `<p style="color: red; padding: 20px;">Error: Invalid media type "${typeSlug || '(empty)'}" in URL.</p>`;
         return;
    }
    if (!itemId) {
        console.error(`Invalid item ID derived from URL: "${itemId || '(empty)'}"`);
         document.body.innerHTML = `<p style="color: red; padding: 20px;">Error: Missing media ID in URL.</p>`;
         return;
    }
    if (!/^[a-f\d]{24}$/i.test(itemId)) {
        console.warn(`Potentially invalid item ID format: "${itemId}"`);
        // Optionally display warning or error page
    }
    // --- End URL Parsing ---

    let mediaData = null;
    let hlsInstance = null;

    // --- GraphQL Helper ---
    async function fetchGraphQL(query, variables = {}) {
      // console.log('Media.js: Fetching GraphQL', { query: query.substring(0, 100) + '...', variables });
      try {
        const response = await fetch('/graphql', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json', 'Accept': 'application/json' },
          body: JSON.stringify({ query, variables }),
        });

        if (!response.ok) {
          let errorBody = `Status: ${response.status} ${response.statusText}`;
          try { const bodyText = await response.text(); errorBody += ` - Body: ${bodyText.substring(0, 200)}`; } catch(e) {}
          throw new Error(`GraphQL fetch failed: ${errorBody}`);
        }

        const responseJson = await response.json();
        const { data, errors } = responseJson;

        if (errors) {
          console.error('Media.js: GraphQL Errors:', errors);
          const combinedErrorMessage = errors.map(e => e.message + (e.path ? ` (path: ${e.path.join('.')})` : '')).join('; ');
          throw new Error(`GraphQL Error: ${combinedErrorMessage}`);
        }
        // console.log('Media.js: GraphQL fetch successful');
        return data;
      } catch (err) {
        console.error('Media.js: fetchGraphQL Error:', err);
        throw err;
      }
    }

    // --- Player Logic ---
    async function playItem(url, isSourceStream = false, method = 'GET') {
      try {
        if (!url || !playerContainer) {
          console.error('Media.js: No valid URL or player container to play:', { url, playerContainer: !!playerContainer });
          alert("Error: Cannot play video. Invalid URL provided.");
          return;
        }

        // Check if this is a video provider URL that should be handled as an iframe
        const isVideoProviderUrl = url && (
          url.includes('waaw1.tv') ||
          url.includes('do7go.com') ||
          url.includes('streamtape.com') ||
          url.includes('doodstream.com') ||
          url.includes('vidoza.net') ||
          url.includes('voe.sx') ||
          url.includes('upstream.to') ||
          url.includes('mixdrop.co') ||
          url.includes('vudeo.net') ||
          url.includes('tipfly.xyz') ||
          url.includes('lulu.st') ||
          url.includes('/e/') // Common pattern for embed URLs
        );

        console.log('Media.js: URL check for iframe:', {
          url,
          isVideoProviderUrl,
          method,
          isSourceStream
        });

        // Force iframe method for video provider URLs
        if (isVideoProviderUrl && method !== 'iframe') {
          console.log('Media.js: Detected video provider URL, forcing iframe method:', url);
          method = 'iframe';
          isSourceStream = false;
        }

        console.log('Media.js: playItem called', { url, isSourceStream, method, itemType });

        if (playerContainer.classList.contains('hidden')) {
          history.pushState({ playerOpen: true }, '', window.location.href);
        }

        playerContainer.classList.remove('hidden');
        playerContainer.style.display = 'flex';

        // Make sure player controls are visible
        const playerControls = document.getElementById('player-controls');
        if (playerControls) {
          playerControls.style.opacity = '1';
          playerControls.classList.add('active');
        }

        // Set the title in the player
        if (mediaData) {
          let title = mediaData.displayTitle || mediaData.title || 'Now Playing';

          // Add episode info if available
          // First, check if we already have a title with episode info
          const currentTitle = window.modernPlayer && typeof window.modernPlayer.getCurrentMediaTitle === 'function'
            ? window.modernPlayer.getCurrentMediaTitle()
            : '';

          // DEBUG: Log current title check
          if (window.netStreamDebug) {
            window.netStreamDebug.log('PlayItem Title Check', {
              currentTitle: currentTitle,
              hasEpisodeInfo: currentTitle && (currentTitle.includes('S') && currentTitle.includes('E')),
              timestamp: new Date().toISOString()
            });
          }

          // If we already have a title with episode info, use that instead
          if (currentTitle && currentTitle.includes('S') && currentTitle.includes('E')) {
            console.log('Media.js: Using existing title with episode info:', currentTitle);
            title = currentTitle;
          } else {
            // Otherwise, try to get episode info from the UI
            const episodeItem = document.querySelector('#episodes .grid-item[style*="border: 1px solid"]');

            // DEBUG: Log episode item
            if (window.netStreamDebug) {
              window.netStreamDebug.log('PlayItem Episode Item', {
                episodeElement: episodeItem ? true : false,
                episodeNumber: episodeItem ? episodeItem.dataset.ep : 'None',
                episodeStyle: episodeItem ? episodeItem.getAttribute('style') : 'None',
                allHighlightedEpisodes: document.querySelectorAll('#episodes .grid-item[style*="border: 1px solid"]').length,
                timestamp: new Date().toISOString()
              });
            }

            if (episodeItem && episodeItem.dataset.ep) {
              const seasonSelect = document.getElementById('season-select');
              const currentSeason = seasonSelect ? seasonSelect.value : mediaData.season || '1';

              // Format the title for series/anime
              if (itemType === 'SERIES') {
                title = `Series: ${title} - S${currentSeason}:E${episodeItem.dataset.ep}`;
              } else if (itemType === 'ANIME') {
                title = `Anime: ${title} - S${currentSeason}:E${episodeItem.dataset.ep}`;
              } else {
                title += ` - S${currentSeason}:E${episodeItem.dataset.ep}`;
              }

              console.log('Media.js: Updated player title with episode info:', title);
            }
          }

          // Add content type indicator
          if (itemType) {
            let typeLabel = '';
            switch (itemType) {
              case 'MOVIE': typeLabel = 'Movie'; break;
              case 'SERIES': typeLabel = 'Series'; break;
              case 'ANIME': typeLabel = 'Anime'; break;
              case 'LIVETV': typeLabel = 'Live TV'; break;
              default: typeLabel = '';
            }

            if (typeLabel && !title.includes(typeLabel)) {
              title = `${typeLabel}: ${title}`;
            }
          }

          // Try to set the title using the modern player
          if (window.modernPlayer && typeof window.modernPlayer.setTitle === 'function') {
            window.modernPlayer.setTitle(title);
            console.log('Media.js: Set player title using modernPlayer.setTitle to:', title);
          } else {
            console.log('Media.js: Modern player not available, setting title directly');

            // Try to set the title directly
            const playerTitle = document.getElementById('player-title');
            if (playerTitle) {
              playerTitle.textContent = title;
              console.log('Media.js: Set player title directly to:', title);
            } else {
              console.log('Media.js: Player title element not found');

              // Try again after a short delay
              setTimeout(() => {
                const delayedPlayerTitle = document.getElementById('player-title');
                if (delayedPlayerTitle) {
                  delayedPlayerTitle.textContent = title;
                  console.log('Media.js: Set player title after delay to:', title);
                } else {
                  console.error('Media.js: Player title element still not found after delay');
                }
              }, 500);
            }
          }
        } else {
          console.log('Media.js: Media data not available for title setting');
        }
      } catch (error) {
        console.error('Media.js: Error in playItem:', error);
      }
    }
  } catch (error) {
    console.error('Media.js: Error in DOMContentLoaded event:', error);
  }
});
