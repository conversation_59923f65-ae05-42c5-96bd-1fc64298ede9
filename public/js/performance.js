/**
 * Frontend Performance Optimization Module
 * Implements lazy loading, virtual scrolling, and intelligent caching
 */

class PerformanceOptimizer {
  constructor() {
    this.cache = new Map();
    this.imageCache = new Map();
    this.observedElements = new Set();
    this.pendingRequests = new Map();
    
    // Performance metrics
    this.metrics = {
      cacheHits: 0,
      cacheMisses: 0,
      imagesLoaded: 0,
      requestsSaved: 0
    };
    
    this.initializeIntersectionObserver();
    this.initializeImageLazyLoading();
    this.setupPerformanceMonitoring();
  }

  /**
   * Initialize Intersection Observer for lazy loading
   */
  initializeIntersectionObserver() {
    this.intersectionObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            this.handleElementVisible(entry.target);
          }
        });
      },
      {
        rootMargin: '100px', // Load content 100px before it becomes visible
        threshold: 0.1
      }
    );
  }

  /**
   * Initialize image lazy loading
   */
  initializeImageLazyLoading() {
    this.imageObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            this.loadImage(entry.target);
          }
        });
      },
      {
        rootMargin: '50px',
        threshold: 0.1
      }
    );
  }

  /**
   * Setup performance monitoring
   */
  setupPerformanceMonitoring() {
    // Monitor page load performance
    window.addEventListener('load', () => {
      setTimeout(() => {
        const perfData = performance.getEntriesByType('navigation')[0];
        console.log('Page Load Performance:', {
          domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
          loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
          totalTime: perfData.loadEventEnd - perfData.fetchStart
        });
      }, 0);
    });

    // Monitor cache performance
    setInterval(() => {
      const hitRate = this.metrics.cacheHits + this.metrics.cacheMisses > 0
        ? (this.metrics.cacheHits / (this.metrics.cacheHits + this.metrics.cacheMisses) * 100).toFixed(2)
        : 0;
      
      console.log('Cache Performance:', {
        hitRate: `${hitRate}%`,
        cacheSize: this.cache.size,
        imagesCached: this.imageCache.size,
        requestsSaved: this.metrics.requestsSaved
      });
    }, 60000); // Log every minute
  }

  /**
   * Optimized GraphQL query with caching
   */
  async graphqlQuery(query, variables = {}, options = {}) {
    const cacheKey = this.generateCacheKey(query, variables);
    const ttl = options.ttl || 300000; // 5 minutes default
    
    // Check cache first
    const cached = this.cache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < ttl) {
      this.metrics.cacheHits++;
      return cached.data;
    }

    // Check if request is already pending
    if (this.pendingRequests.has(cacheKey)) {
      this.metrics.requestsSaved++;
      return this.pendingRequests.get(cacheKey);
    }

    // Make new request
    const requestPromise = this.makeGraphQLRequest(query, variables);
    this.pendingRequests.set(cacheKey, requestPromise);

    try {
      const result = await requestPromise;
      
      // Cache successful result
      this.cache.set(cacheKey, {
        data: result,
        timestamp: Date.now()
      });
      
      this.metrics.cacheMisses++;
      return result;
    } finally {
      this.pendingRequests.delete(cacheKey);
    }
  }

  /**
   * Make actual GraphQL request
   */
  async makeGraphQLRequest(query, variables) {
    const response = await fetch('/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query, variables })
    });

    if (!response.ok) {
      throw new Error(`GraphQL request failed: ${response.statusText}`);
    }

    const result = await response.json();
    if (result.errors) {
      throw new Error(`GraphQL errors: ${result.errors.map(e => e.message).join(', ')}`);
    }

    return result.data;
  }

  /**
   * Generate cache key from query and variables
   */
  generateCacheKey(query, variables) {
    const queryHash = this.simpleHash(query);
    const variablesHash = this.simpleHash(JSON.stringify(variables));
    return `${queryHash}_${variablesHash}`;
  }

  /**
   * Simple hash function
   */
  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Lazy load carousel content
   */
  setupLazyCarousel(carouselElement, loadFunction) {
    const items = carouselElement.querySelectorAll('.carousel-item[data-lazy]');
    
    items.forEach(item => {
      item.dataset.loadFunction = loadFunction.name;
      this.intersectionObserver.observe(item);
      this.observedElements.add(item);
    });
  }

  /**
   * Handle element becoming visible
   */
  async handleElementVisible(element) {
    if (element.dataset.loaded === 'true') return;

    try {
      if (element.dataset.loadFunction) {
        // Load carousel item
        await this.loadCarouselItem(element);
      } else if (element.tagName === 'IMG') {
        // Load image
        await this.loadImage(element);
      }
      
      element.dataset.loaded = 'true';
      this.intersectionObserver.unobserve(element);
      this.observedElements.delete(element);
    } catch (error) {
      console.error('Error loading lazy content:', error);
    }
  }

  /**
   * Load carousel item content
   */
  async loadCarouselItem(item) {
    const loadFunctionName = item.dataset.loadFunction;
    const mediaType = item.dataset.mediaType;
    const page = parseInt(item.dataset.page) || 1;

    if (window[loadFunctionName]) {
      await window[loadFunctionName](mediaType, page);
    }
  }

  /**
   * Optimized image loading with caching
   */
  async loadImage(img) {
    const src = img.dataset.src || img.src;
    if (!src || img.dataset.loaded === 'true') return;

    // Check image cache
    if (this.imageCache.has(src)) {
      img.src = this.imageCache.get(src);
      img.dataset.loaded = 'true';
      this.metrics.cacheHits++;
      return;
    }

    // Load image with error handling
    return new Promise((resolve, reject) => {
      const tempImg = new Image();
      
      tempImg.onload = () => {
        img.src = src;
        img.dataset.loaded = 'true';
        this.imageCache.set(src, src);
        this.metrics.imagesLoaded++;
        this.metrics.cacheMisses++;
        resolve();
      };
      
      tempImg.onerror = () => {
        // Fallback to placeholder
        img.src = '/images/placeholder.jpg';
        img.dataset.loaded = 'true';
        reject(new Error(`Failed to load image: ${src}`));
      };
      
      tempImg.src = src;
    });
  }

  /**
   * Preload critical images
   */
  preloadImages(urls) {
    urls.forEach(url => {
      if (!this.imageCache.has(url)) {
        const img = new Image();
        img.onload = () => this.imageCache.set(url, url);
        img.src = url;
      }
    });
  }

  /**
   * Virtual scrolling for large lists
   */
  setupVirtualScrolling(container, itemHeight, renderFunction) {
    const viewport = container.querySelector('.virtual-viewport');
    const content = container.querySelector('.virtual-content');
    
    if (!viewport || !content) return;

    let scrollTop = 0;
    let visibleStart = 0;
    let visibleEnd = 0;
    let totalItems = 0;

    const updateVisibleItems = () => {
      const containerHeight = viewport.clientHeight;
      const itemsPerPage = Math.ceil(containerHeight / itemHeight);
      const buffer = Math.floor(itemsPerPage / 2);

      visibleStart = Math.max(0, Math.floor(scrollTop / itemHeight) - buffer);
      visibleEnd = Math.min(totalItems, visibleStart + itemsPerPage + buffer * 2);

      renderFunction(visibleStart, visibleEnd);
    };

    viewport.addEventListener('scroll', () => {
      scrollTop = viewport.scrollTop;
      requestAnimationFrame(updateVisibleItems);
    });

    return { updateVisibleItems, setTotalItems: (count) => { totalItems = count; } };
  }

  /**
   * Debounce function for performance
   */
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  /**
   * Throttle function for performance
   */
  throttle(func, limit) {
    let inThrottle;
    return function(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
    this.imageCache.clear();
    this.metrics = {
      cacheHits: 0,
      cacheMisses: 0,
      imagesLoaded: 0,
      requestsSaved: 0
    };
  }

  /**
   * Get performance metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      cacheSize: this.cache.size,
      imagesCached: this.imageCache.size,
      observedElements: this.observedElements.size
    };
  }
}

// Initialize performance optimizer
const performanceOptimizer = new PerformanceOptimizer();

// Export for global use
window.performanceOptimizer = performanceOptimizer;
