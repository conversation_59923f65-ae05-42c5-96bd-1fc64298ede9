// File: public/js/remoteControl.js
// Remote control navigation for TV boxes

document.addEventListener('DOMContentLoaded', () => {
  console.log('RemoteControl.js: Initializing remote control navigation');

  // Initialize the remote control manager
  const remoteManager = new RemoteControlManager();

  // Make it globally available
  window.remoteManager = remoteManager;

  // Initialize navigation
  remoteManager.initialize();
});

class RemoteControlManager {
  constructor() {
    // Navigation state
    this.currentSection = null;
    this.currentFocusArea = 'sidebar'; // sidebar, content, player
    this.focusedElement = null;
    this.lastContentFocus = null;
    this.lastSidebarFocus = null;

    // Navigation map for content area - updated for new structure
    this.contentAreas = {
      search: ['search-input', 'search-button', 'search-list'],
      movies: [
        'search-input',
        'movies-hero',
        'movies-sort',
        'movies-trending-carousel',
        'movies-wishlist-carousel',
        'movies-recently-watched-carousel',
        'movies-latest-carousel',
        'movies-ancien-carousel',
        'movies-genre-carousels',
        'movies-list'
      ],
      series: [
        'search-input',
        'series-hero',
        'series-sort',
        'series-trending-carousel',
        'series-wishlist-carousel',
        'series-recently-watched-carousel',
        'series-latest-carousel',
        'series-action-carousel',
        'series-genre-carousels',
        'series-list'
      ],
      anime: [
        'search-input',
        'anime-hero',
        'anime-sort',
        'anime-trending-carousel',
        'anime-wishlist-carousel',
        'anime-recently-watched-carousel',
        'anime-latest-carousel',
        'anime-movies-carousel',
        'anime-genre-carousels',
        'anime-list'
      ],
      livetv: ['livetv-search-input', 'livetv-search-button', 'livetv-categories', 'livetv-channels', 'livetv-guide']
    };

    // Bind methods
    this.handleKeyDown = this.handleKeyDown.bind(this);
    this.handleWheel = this.handleWheel.bind(this);
    this.focusElement = this.focusElement.bind(this);
    this.navigateCarousel = this.navigateCarousel.bind(this);
    this.navigateGrid = this.navigateGrid.bind(this);
    this.makeCarouselItemsFocusable = this.makeCarouselItemsFocusable.bind(this);
    this.observeCarouselChanges = this.observeCarouselChanges.bind(this);
    this.focusFirstInArea = this.focusFirstInArea.bind(this);
    this.navigateContent = this.navigateContent.bind(this);
    this.navigateContentAreas = this.navigateContentAreas.bind(this);
  }

  initialize() {
    // Add keyboard event listener
    document.addEventListener('keydown', this.handleKeyDown);

    // Add mouse wheel event listener for scrolling
    this.handleWheel = this.handleWheel.bind(this);
    document.addEventListener('wheel', this.handleWheel, { passive: false });

    // Listen for hash changes to update current section
    window.addEventListener('hashchange', () => {
      this.updateCurrentSection();

      // If we're in the sidebar, focus the corresponding sidebar item
      if (this.currentFocusArea === 'sidebar') {
        const sidebarLinks = Array.from(document.querySelectorAll('.sidebar a'));
        const currentLink = sidebarLinks.find(link => link.getAttribute('href') === `#${this.currentSection}`);
        if (currentLink) {
          this.focusElement(currentLink);
          this.lastSidebarFocus = currentLink;
        }
      } else {
        // If we're in the content area, focus the first element in the new section
        this.lastContentFocus = null;
        this.focusContent();
      }
    });

    // Check if we're on a media page
    const isMediaPage = window.location.pathname.match(/^\/(movies|series|anime|livetv)\/\w+$/);

    if (isMediaPage) {
      // Initialize media page navigation
      this.initializeMediaPage();
    } else {
      // Initialize home page navigation
      this.initializeHomePage();
    }

    // Make all carousel items focusable
    this.makeCarouselItemsFocusable();

    // Listen for DOM changes to make new carousel items focusable
    this.observeCarouselChanges();

    // Add focus visible class to body for CSS targeting
    document.body.classList.add('remote-navigation');

    console.log('RemoteControl.js: Remote navigation initialized');
  }

  makeCarouselItemsFocusable() {
    // Make all carousel items focusable
    document.querySelectorAll('.carousel-item').forEach(item => {
      if (!item.hasAttribute('tabindex')) {
        item.setAttribute('tabindex', '0');
      }
    });

    // Make all grid items focusable
    document.querySelectorAll('.grid-item').forEach(item => {
      if (!item.hasAttribute('tabindex')) {
        item.setAttribute('tabindex', '0');
      }
    });

    // Make all channel cards focusable for Live TV section
    document.querySelectorAll('.channel-card').forEach(item => {
      if (!item.hasAttribute('tabindex')) {
        item.setAttribute('tabindex', '0');
      }
    });

    // Make hero buttons focusable
    document.querySelectorAll('.hero-btn').forEach(item => {
      if (!item.hasAttribute('tabindex')) {
        item.setAttribute('tabindex', '0');
      }
    });

    // Make carousel navigation buttons focusable
    document.querySelectorAll('.carousel-nav').forEach(item => {
      if (!item.hasAttribute('tabindex')) {
        item.setAttribute('tabindex', '0');
      }
    });

    // Make carousel refresh buttons focusable
    document.querySelectorAll('.carousel-refresh-btn').forEach(item => {
      if (!item.hasAttribute('tabindex')) {
        item.setAttribute('tabindex', '0');
      }
    });

    // Make filter controls focusable
    document.querySelectorAll('.filter-controls select').forEach(item => {
      if (!item.hasAttribute('tabindex')) {
        item.setAttribute('tabindex', '0');
      }
    });

    console.log('Made all interactive elements focusable');
  }

  observeCarouselChanges() {
    // Set up a MutationObserver to watch for new carousel items and channel cards
    const observer = new MutationObserver(mutations => {
      let shouldUpdateFocusable = false;

      mutations.forEach(mutation => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Check if any of the added nodes are interactive elements or contain them
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              if (node.classList && (
                node.classList.contains('carousel-item') ||
                node.classList.contains('channel-card') ||
                node.classList.contains('grid-item') ||
                node.classList.contains('hero-btn') ||
                node.classList.contains('carousel-nav') ||
                node.classList.contains('carousel-refresh-btn')
              )) {
                shouldUpdateFocusable = true;
              } else if (node.querySelectorAll) {
                const focusableItems = node.querySelectorAll('.carousel-item, .channel-card, .grid-item, .hero-btn, .carousel-nav, .carousel-refresh-btn, .filter-controls select');
                if (focusableItems.length > 0) {
                  shouldUpdateFocusable = true;
                }
              }
            }
          });
        }
      });

      if (shouldUpdateFocusable) {
        this.makeCarouselItemsFocusable();
      }
    });

    // Observe the entire document for changes
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    console.log('Set up observer for carousel and channel card changes');
  }

  initializeHomePage() {
    // Determine initial section
    this.updateCurrentSection();

    // Set initial focus to sidebar and highlight the current section
    const sidebarLinks = Array.from(document.querySelectorAll('.sidebar a'));
    const currentLink = sidebarLinks.find(link => link.getAttribute('href') === `#${this.currentSection}`);

    if (currentLink) {
      this.lastSidebarFocus = currentLink;
    }

    this.focusSidebar();
  }

  initializeMediaPage() {
    // For media pages, we need different navigation areas
    this.currentFocusArea = 'content';

    // Define content areas for media page
    this.contentAreas = {
      media: ['return-arrow', 'media-info', 'seasons', 'episodes', 'providers']
    };

    // Set current section to media
    this.currentSection = 'media';

    // Add event listeners for media page elements
    this.setupMediaPageNavigation();

    // Focus the return arrow first
    const returnArrow = document.getElementById('return-arrow');
    if (returnArrow) {
      this.focusElement(returnArrow);
    }
  }

  setupMediaPageNavigation() {
    // Get all the media page sections
    const returnArrow = document.getElementById('return-arrow');
    const seasonsContainer = document.getElementById('seasons');
    const episodesContainer = document.getElementById('episodes');
    const providersContainer = document.getElementById('providers');

    console.log('Setting up media page navigation');

    // Make all grid items focusable
    const makeItemsFocusable = (container) => {
      if (!container) return;

      const items = container.querySelectorAll('.grid-item');
      items.forEach(item => {
        item.setAttribute('tabindex', '0');
      });
    };

    // Make all sections focusable
    makeItemsFocusable(seasonsContainer);
    makeItemsFocusable(episodesContainer);
    makeItemsFocusable(providersContainer);

    // Add navigation between sections
    this.mediaPageSections = [
      returnArrow,
      seasonsContainer,
      episodesContainer,
      providersContainer
    ].filter(Boolean);

    // Add event listener for return arrow
    if (returnArrow) {
      returnArrow.addEventListener('click', () => {
        // Get the previous page from history or default to home
        const referrer = document.referrer;
        if (referrer && referrer.includes(window.location.host)) {
          window.history.back();
        } else {
          window.location.href = '/';
        }
      });
    }

    // Add MutationObserver to watch for changes in the episodes and providers containers
    // This is needed because these containers are populated dynamically
    const observeContainer = (container) => {
      if (!container) return;

      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            // Make newly added items focusable
            makeItemsFocusable(container);
          }
        });
      });

      observer.observe(container, { childList: true, subtree: true });
    };

    observeContainer(seasonsContainer);
    observeContainer(episodesContainer);
    observeContainer(providersContainer);
  }

  updateCurrentSection() {
    // Determine current active section
    const activeSection = document.querySelector('.section.active');
    if (activeSection) {
      this.currentSection = activeSection.id;
    } else if (window.location.hash) {
      this.currentSection = window.location.hash.substring(1);
    } else {
      this.currentSection = 'movies'; // Default
    }

    console.log(`RemoteControl.js: Current section set to ${this.currentSection}`);
  }

  handleKeyDown(event) {
    // Handle key navigation
    console.log(`🎮 Remote Control: ${event.key} pressed`);
    console.log(`📍 Current focus area: ${this.currentFocusArea}`);
    console.log(`📺 Current section: ${this.currentSection}`);
    console.log(`🎯 Focused element:`, this.focusedElement?.tagName, this.focusedElement?.className, this.focusedElement?.id);

    switch (event.key) {
      case 'ArrowUp':
        event.preventDefault();
        console.log('⬆️ Navigating UP');
        this.navigateVertical(-1);
        break;
      case 'ArrowDown':
        event.preventDefault();
        console.log('⬇️ Navigating DOWN');
        this.navigateVertical(1);
        break;
      case 'ArrowLeft':
        event.preventDefault();
        console.log('⬅️ Navigating LEFT');
        this.navigateHorizontal(-1);
        break;
      case 'ArrowRight':
        event.preventDefault();
        console.log('➡️ Navigating RIGHT');
        this.navigateHorizontal(1);
        break;
      case 'Enter':
        event.preventDefault();
        console.log('✅ ENTER pressed - Activating focused element');
        this.activateFocused();
        break;
      case 'Escape':
        event.preventDefault();
        console.log('🚪 ESCAPE pressed');
        this.handleEscape();
        break;
    }
  }

  navigateVertical(direction) {
    if (this.currentFocusArea === 'sidebar') {
      this.navigateSidebar(direction);
    } else if (this.currentFocusArea === 'content') {
      this.navigateContent(direction, 0);
    } else if (this.currentFocusArea === 'player') {
      // Handle player controls navigation
      this.navigatePlayerControls();
    }
  }

  navigateHorizontal(direction) {
    if (this.currentFocusArea === 'sidebar' && direction > 0) {
      // Move from sidebar to content
      this.focusContent();
    } else if (this.currentFocusArea === 'content') {
      if (direction < 0 && this.isAtLeftEdge()) {
        // Move from content to sidebar
        this.focusSidebar();
      } else {
        // Navigate within content
        this.navigateContent(0, direction);
      }
    } else if (this.currentFocusArea === 'player') {
      // Handle player controls navigation
      this.navigatePlayerControls();
    }
  }

  isAtLeftEdge() {
    // Check if we're at the left edge of the content area
    if (!this.focusedElement) return true;

    // If in a carousel, check if at first item
    if (this.focusedElement.closest('.carousel-items')) {
      const carouselItems = this.focusedElement.closest('.carousel-items');
      const items = Array.from(carouselItems.querySelectorAll('.carousel-item'));
      const index = items.indexOf(this.focusedElement);
      return index === 0;
    }

    // If in a grid, check if at first column
    if (this.focusedElement.closest('.grid')) {
      const grid = this.focusedElement.closest('.grid');
      const items = Array.from(grid.querySelectorAll('.grid-item'));
      const rect = this.focusedElement.getBoundingClientRect();
      const leftEdge = rect.left;

      // Check if there's any item to the left
      return !items.some(item => {
        if (item === this.focusedElement) return false;
        const itemRect = item.getBoundingClientRect();
        return itemRect.right < leftEdge && Math.abs(itemRect.top - rect.top) < rect.height;
      });
    }

    // For other elements, check if it's the first focusable in its container
    const container = this.focusedElement.closest('section') || document.body;
    const focusables = this.getFocusableElements(container);
    return focusables.indexOf(this.focusedElement) === 0;
  }

  navigateSidebar(direction) {
    const sidebarLinks = Array.from(document.querySelectorAll('.sidebar a'));
    if (sidebarLinks.length === 0) return;

    let currentIndex = -1;
    if (this.focusedElement && sidebarLinks.includes(this.focusedElement)) {
      currentIndex = sidebarLinks.indexOf(this.focusedElement);
    }

    let nextIndex = currentIndex + direction;
    if (nextIndex < 0) nextIndex = 0;
    if (nextIndex >= sidebarLinks.length) nextIndex = sidebarLinks.length - 1;

    if (nextIndex !== currentIndex) {
      // Focus the new sidebar item
      this.focusElement(sidebarLinks[nextIndex]);
      this.lastSidebarFocus = sidebarLinks[nextIndex];

      // Immediately navigate to the new section
      this.activateSidebarItem(sidebarLinks[nextIndex]);
    }
  }

  activateSidebarItem(sidebarItem) {
    if (!sidebarItem) return;

    console.log('RemoteControl.js: Activating sidebar item', sidebarItem);

    // Get the href attribute
    const href = sidebarItem.getAttribute('href');
    if (!href) return;

    // Check if it's a hash link
    if (href.startsWith('#')) {
      const sectionId = href.substring(1);

      // If we're already on this section, do nothing
      if (this.currentSection === sectionId) {
        console.log('RemoteControl.js: Already on section', sectionId);
        return;
      }

      // Update the hash without triggering a page reload
      if (window.history && window.history.pushState) {
        window.history.pushState(null, null, href);
      } else {
        window.location.hash = sectionId;
      }

      // Hide all sections
      document.querySelectorAll('.section').forEach(section => {
        section.classList.remove('active');
      });

      // Show the selected section
      const selectedSection = document.getElementById(sectionId);
      if (selectedSection) {
        selectedSection.classList.add('active');
      }

      // Update current section
      this.currentSection = sectionId;

      // Update sidebar visual state
      document.querySelectorAll('.sidebar a').forEach(link => {
        link.classList.remove('active');
      });
      sidebarItem.classList.add('active');

      // Trigger the hashchange event manually
      const hashChangeEvent = new Event('hashchange');
      window.dispatchEvent(hashChangeEvent);

      // Dispatch a custom event to notify other components about the section change
      const sectionChangedEvent = new CustomEvent('sectionChanged', {
        detail: { section: sectionId }
      });
      document.dispatchEvent(sectionChangedEvent);
      console.log(`Dispatched sectionChanged event for section: ${sectionId}`);

      // Reset content focus
      this.lastContentFocus = null;

      // If there's a section-specific initialization function, call it
      if (sectionId === 'movies' && typeof loadMovies === 'function') {
        loadMovies();
      } else if (sectionId === 'series' && typeof loadSeries === 'function') {
        loadSeries();
      } else if (sectionId === 'anime' && typeof loadAnime === 'function') {
        loadAnime();
      } else if (sectionId === 'livetv' && window.liveTVManager) {
        if (typeof window.liveTVManager.initialize === 'function') {
          window.liveTVManager.initialize();
        }
      }
    } else {
      // For non-hash links, navigate to the URL
      window.location.href = href;
    }
  }

  navigateContent(vDirection, hDirection) {
    if (!this.currentSection) return;

    // Check if we're on a media page
    const isMediaPage = window.location.pathname.match(/^\/(movies|series|anime|livetv)\/\w+$/);

    if (isMediaPage) {
      this.navigateMediaPage(vDirection, hDirection);
      return;
    }

    // Handle different content areas
    if (this.focusedElement) {
      // Check if we're in a carousel
      const carousel = this.focusedElement.closest('.carousel-items');
      if (carousel) {
        console.log('Navigating within carousel');
        this.navigateCarousel(carousel, hDirection, vDirection);
        return;
      }

      // Check if we're in the Live TV channels grid
      const channelsGrid = this.focusedElement.closest('#livetv-channels');
      if (channelsGrid || this.focusedElement.classList.contains('channel-card')) {
        console.log('Navigating within Live TV channels grid');
        this.navigateChannelsGrid(channelsGrid || this.focusedElement.closest('#livetv-channels'), hDirection, vDirection);
        return;
      }

      // If we're not in a carousel but there are carousels in the current section,
      // try to focus the first carousel when navigating up/down
      if (vDirection !== 0 && !carousel) {
        const section = document.getElementById(this.currentSection);
        if (section) {
          // Get all carousel containers in the current section
          const carouselContainers = section.querySelectorAll('.trending-carousel-container, .carousel-container');

          if (carouselContainers.length > 0) {
            console.log(`Found ${carouselContainers.length} carousel containers in section ${this.currentSection}`);

            // Find the closest carousel container in the vertical direction
            let closestContainer = null;
            let closestDistance = Infinity;
            const currentRect = this.focusedElement.getBoundingClientRect();

            carouselContainers.forEach(container => {
              const rect = container.getBoundingClientRect();
              const distance = vDirection > 0 ?
                rect.top - currentRect.bottom :
                currentRect.top - rect.bottom;

              console.log(`Carousel ${container.id}: distance=${distance}, direction=${vDirection}`);

              if ((vDirection > 0 && distance > 0 && distance < closestDistance) ||
                  (vDirection < 0 && distance < 0 && Math.abs(distance) < closestDistance)) {
                closestDistance = Math.abs(distance);
                closestContainer = container;
              }
            });

            if (closestContainer) {
              console.log('Found closest carousel container:', closestContainer.id);

              // Find the carousel-items element within this container
              const carouselItems = closestContainer.querySelector('.carousel-items');
              if (carouselItems) {
                console.log('Found carousel items, navigating to it');

                // Check if there are any items in this carousel
                const items = carouselItems.querySelectorAll('.carousel-item');
                if (items.length > 0) {
                  // Focus the first item
                  this.focusElement(items[0]);
                  return;
                } else {
                  console.log('No items found in carousel');
                }
              } else {
                console.log('No carousel-items found in container');
              }
            } else {
              console.log('No suitable carousel found in the specified direction');
            }
          }
        }
      }

      // Check if we're in a grid
      const grid = this.focusedElement.closest('.grid');
      if (grid) {
        this.navigateGrid(grid, hDirection, vDirection);
        return;
      }

      // Check if we're in livetv categories
      const categories = this.focusedElement.closest('#livetv-categories');
      if (categories) {
        this.navigateCategories(categories, hDirection, vDirection);
        return;
      }
    }

    // Default content navigation between areas
    this.navigateContentAreas(vDirection);
  }

  navigateMediaPage(vDirection, hDirection) {
    console.log('🎬 Media page navigation:', { vDirection, hDirection, focusedElement: this.focusedElement?.id || this.focusedElement?.className });

    // If no focused element, focus the return arrow
    if (!this.focusedElement) {
      const returnArrow = document.getElementById('return-arrow');
      if (returnArrow) {
        console.log('🎯 Focusing return arrow (no focused element)');
        this.focusElement(returnArrow);
        return;
      }
    }

    // Define the navigation order for media pages
    const getMediaPageSections = () => {
      const sections = [];

      // Return arrow is always first
      const returnArrow = document.getElementById('return-arrow');
      if (returnArrow) {
        sections.push({ element: returnArrow, type: 'return-arrow' });
      }

      // Media info section (check if it has content and is visible)
      const mediaInfo = document.getElementById('media-info');
      if (mediaInfo && mediaInfo.style.display !== 'none' && mediaInfo.innerHTML.trim() !== '') {
        sections.push({ element: mediaInfo, type: 'media-info' });
      }

      // Hero buttons section (play/wishlist buttons)
      const mediaActions = document.querySelector('.media-actions');
      const heroPlayBtn = document.getElementById('hero-play-btn');
      const heroWishlistBtn = document.getElementById('media-wishlist-button');
      if (mediaActions && (heroPlayBtn || heroWishlistBtn)) {
        sections.push({ element: mediaActions, type: 'media-actions' });
      }

      // Episodes section - include if container exists (content may load later)
      const episodesContainer = document.getElementById('episodes');
      const episodesSection = document.querySelector('.episodes-section');
      if (episodesContainer) {
        sections.push({ element: episodesContainer, type: 'episodes' });
      } else if (episodesSection && episodesSection.style.display !== 'none') {
        sections.push({ element: episodesSection, type: 'episodes' });
      }

      // Providers section - include if container exists (content may load later)
      const providersContainer = document.getElementById('providers');
      const providersSection = document.querySelector('.providers-section');
      if (providersContainer) {
        sections.push({ element: providersContainer, type: 'providers' });
      } else if (providersSection && providersSection.style.display !== 'none') {
        sections.push({ element: providersSection, type: 'providers' });
      }

      // Seasons section (if visible)
      const seasonsContainer = document.getElementById('seasons');
      if (seasonsContainer && seasonsContainer.style.display !== 'none' && seasonsContainer.querySelector('.grid-item')) {
        sections.push({ element: seasonsContainer, type: 'seasons' });
      }

      // Fallback: If no episodes or providers were found, try to find the content-wrapper
      if (!sections.some(s => s.type === 'episodes' || s.type === 'providers')) {
        const contentWrapper = document.querySelector('.content-wrapper');
        if (contentWrapper) {
          sections.push({ element: contentWrapper, type: 'content-wrapper' });
        }
      }

      console.log('📋 Detected media page sections:', sections.map(s => s.type));
      return sections;
    };

    const sections = getMediaPageSections();
    console.log('📋 Available media page sections:', sections.map(s => s.type));

    // Debug: Log all available elements for troubleshooting
    this.debugMediaPageElements();

    // Find current section index
    let currentSectionIndex = -1;

    // First, check for specific button IDs that should take priority
    if (this.focusedElement.id === 'hero-play-btn' || this.focusedElement.id === 'media-wishlist-button') {
      // Find the media-actions section
      for (let i = 0; i < sections.length; i++) {
        if (sections[i].type === 'media-actions') {
          currentSectionIndex = i;
          break;
        }
      }
    } else {
      // Standard section detection for other elements
      for (let i = 0; i < sections.length; i++) {
        const section = sections[i];

        // Check direct element match
        if (section.element === this.focusedElement) {
          currentSectionIndex = i;
          break;
        }

        // Check if focused element is contained within this section
        if (section.element.contains(this.focusedElement)) {
          currentSectionIndex = i;
          break;
        }

        // Check by element ID if available
        if (section.element.id && this.focusedElement.closest(`#${section.element.id}`)) {
          currentSectionIndex = i;
          break;
        }
      }
    }

    console.log('📍 Current section index:', currentSectionIndex, sections[currentSectionIndex]?.type);
    console.log('🔍 Focused element details:', {
      id: this.focusedElement.id,
      className: this.focusedElement.className,
      tagName: this.focusedElement.tagName,
      closest_media_actions: this.focusedElement.closest('.media-actions'),
      parent: this.focusedElement.parentElement
    });

    // Handle vertical navigation between sections
    if (vDirection !== 0) {
      const targetIndex = currentSectionIndex + vDirection;

      if (targetIndex >= 0 && targetIndex < sections.length) {
        const targetSection = sections[targetIndex];
        console.log('🎯 Navigating to section:', targetSection.type);

        this.focusFirstInMediaSection(targetSection.element, targetSection.type);
        return;
      } else {
        console.log('🚫 No more sections in direction:', vDirection);
        return;
      }
    }

    // Handle horizontal navigation within current section
    if (hDirection !== 0) {
      this.handleMediaPageHorizontalNavigation(hDirection);
    }
  }

  focusFirstInMediaSection(sectionElement, sectionType) {
    console.log('🎯 Focusing first element in media section:', sectionType, sectionElement);

    switch (sectionType) {
      case 'return-arrow':
        this.focusElement(sectionElement);
        break;

      case 'media-info':
        // Make the media-info div focusable if it's not already
        if (!sectionElement.hasAttribute('tabindex')) {
          sectionElement.setAttribute('tabindex', '0');
        }
        this.focusElement(sectionElement);
        break;

      case 'media-actions':
        // Focus the first button in media actions
        const firstButton = sectionElement.querySelector('button');
        if (firstButton) {
          this.focusElement(firstButton);
        } else {
          this.focusElement(sectionElement);
        }
        break;

      case 'episodes':
        // Look for episodes in multiple possible locations
        let firstEpisode = sectionElement.querySelector('.grid-item, .episode-card');
        if (!firstEpisode && sectionElement.id !== 'episodes') {
          // If we're in the episodes section, look inside the episodes container
          const episodesContainer = sectionElement.querySelector('#episodes');
          if (episodesContainer) {
            firstEpisode = episodesContainer.querySelector('.grid-item, .episode-card');
          }
        }

        if (firstEpisode) {
          console.log('🎯 Found first episode:', firstEpisode);
          this.focusElement(firstEpisode);
        } else {
          console.log('🎯 No episodes found yet, making container focusable and setting up observer');
          // Make the section focusable even if empty
          if (!sectionElement.hasAttribute('tabindex')) {
            sectionElement.setAttribute('tabindex', '0');
          }

          // Add some visual indication that this is focusable
          sectionElement.style.minHeight = '50px';
          sectionElement.style.border = '2px dashed rgba(255,255,255,0.3)';
          sectionElement.style.borderRadius = '8px';
          sectionElement.style.padding = '20px';
          sectionElement.style.textAlign = 'center';
          sectionElement.style.color = 'rgba(255,255,255,0.7)';
          if (!sectionElement.textContent.trim()) {
            sectionElement.innerHTML = '<p>Episodes will appear here...</p>';
          }

          this.focusElement(sectionElement);

          // Set up observer to watch for content being added
          this.setupContentObserver(sectionElement, 'episodes');
        }
        break;

      case 'providers':
        // Look for providers in multiple possible locations
        let firstProvider = sectionElement.querySelector('.grid-item, .provider-card');
        if (!firstProvider && sectionElement.id !== 'providers') {
          // If we're in the providers section, look inside the providers container
          const providersContainer = sectionElement.querySelector('#providers');
          if (providersContainer) {
            firstProvider = providersContainer.querySelector('.grid-item, .provider-card');
          }
        }

        if (firstProvider) {
          console.log('🎯 Found first provider:', firstProvider);
          this.focusElement(firstProvider);
        } else {
          console.log('🎯 No providers found yet, making container focusable and setting up observer');
          // Make the section focusable even if empty
          if (!sectionElement.hasAttribute('tabindex')) {
            sectionElement.setAttribute('tabindex', '0');
          }

          // Add some visual indication that this is focusable
          sectionElement.style.minHeight = '50px';
          sectionElement.style.border = '2px dashed rgba(255,255,255,0.3)';
          sectionElement.style.borderRadius = '8px';
          sectionElement.style.padding = '20px';
          sectionElement.style.textAlign = 'center';
          sectionElement.style.color = 'rgba(255,255,255,0.7)';
          if (!sectionElement.textContent.trim()) {
            sectionElement.innerHTML = '<p>Providers will appear here...</p>';
          }

          this.focusElement(sectionElement);

          // Set up observer to watch for content being added
          this.setupContentObserver(sectionElement, 'providers');
        }
        break;

      case 'seasons':
        const firstSeason = sectionElement.querySelector('.grid-item');
        if (firstSeason) {
          this.focusElement(firstSeason);
        } else {
          // Make the section focusable
          if (!sectionElement.hasAttribute('tabindex')) {
            sectionElement.setAttribute('tabindex', '0');
          }
          this.focusElement(sectionElement);
        }
        break;

      case 'content-wrapper':
        // For content-wrapper fallback, try to find any focusable element inside
        const firstFocusable = sectionElement.querySelector('.grid-item, .episode-card, .provider-card, button, [tabindex="0"]');
        if (firstFocusable) {
          console.log('🎯 Found focusable element in content-wrapper:', firstFocusable);
          this.focusElement(firstFocusable);
        } else {
          console.log('🎯 Making content-wrapper focusable');
          // Make the content-wrapper focusable
          if (!sectionElement.hasAttribute('tabindex')) {
            sectionElement.setAttribute('tabindex', '0');
          }
          this.focusElement(sectionElement);
        }
        break;

      default:
        // Make the element focusable if it's not already
        if (!sectionElement.hasAttribute('tabindex')) {
          sectionElement.setAttribute('tabindex', '0');
        }
        this.focusElement(sectionElement);
    }
  }

  setupContentObserver(container, contentType) {
    console.log(`🔍 Setting up content observer for ${contentType}:`, container);

    // Create a mutation observer to watch for content being added
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Check if any of the added nodes are episodes or providers
          const hasContent = Array.from(mutation.addedNodes).some(node => {
            return node.nodeType === Node.ELEMENT_NODE &&
                   (node.classList.contains('grid-item') ||
                    node.classList.contains('episode-card') ||
                    node.classList.contains('provider-card') ||
                    node.querySelector('.grid-item, .episode-card, .provider-card'));
          });

          if (hasContent) {
            console.log(`🎉 Content loaded in ${contentType} container!`);
            // Make all new items focusable
            this.makeItemsFocusable(container);
            // Disconnect this observer since content is now loaded
            observer.disconnect();
          }
        }
      });
    });

    // Start observing
    observer.observe(container, {
      childList: true,
      subtree: true
    });

    // Store observer reference for cleanup
    if (!this.contentObservers) {
      this.contentObservers = [];
    }
    this.contentObservers.push(observer);
  }

  makeItemsFocusable(container) {
    const items = container.querySelectorAll('.grid-item, .episode-card, .provider-card');
    items.forEach(item => {
      if (!item.hasAttribute('tabindex')) {
        item.setAttribute('tabindex', '0');
      }
    });
    console.log(`Made ${items.length} items focusable in container:`, container);
  }

  handleEmptyContainerActivation(containerType) {
    console.log(`🔄 Handling empty container activation for: ${containerType}`);

    // Wait a bit and check if content has been loaded
    setTimeout(() => {
      const container = containerType === 'episodes' ?
        document.getElementById('episodes') :
        document.getElementById('providers');

      if (container) {
        const items = container.querySelectorAll('.grid-item, .episode-card, .provider-card');
        if (items.length > 0) {
          console.log(`✅ Content found in ${containerType} after activation, focusing first item`);
          this.makeItemsFocusable(container);
          this.focusElement(items[0]);
        } else {
          console.log(`⏳ No content yet in ${containerType}, will wait for observer`);
        }
      }
    }, 500); // Give content time to load
  }

  debugMediaPageElements() {
    console.log('🔍 DEBUG: Media page elements:');
    console.log('- Return arrow:', document.getElementById('return-arrow'));
    console.log('- Media info:', document.getElementById('media-info'));
    console.log('- Media actions:', document.querySelector('.media-actions'));
    console.log('- Episodes container:', document.getElementById('episodes'));
    console.log('- Episodes section:', document.querySelector('.episodes-section'));
    console.log('- Providers container:', document.getElementById('providers'));
    console.log('- Providers section:', document.querySelector('.providers-section'));
    console.log('- Seasons container:', document.getElementById('seasons'));

    // Check for episode items
    const episodesContainer = document.getElementById('episodes');
    if (episodesContainer) {
      const episodeItems = episodesContainer.querySelectorAll('.grid-item, .episode-card');
      console.log('- Episode items in #episodes:', episodeItems.length);
    }

    // Check for provider items
    const providersContainer = document.getElementById('providers');
    if (providersContainer) {
      const providerItems = providersContainer.querySelectorAll('.grid-item, .provider-card');
      console.log('- Provider items in #providers:', providerItems.length);
    }
  }

  handleMediaPageHorizontalNavigation(hDirection) {
    console.log('↔️ Handling horizontal navigation in media page:', hDirection);

    // Check what type of element we're currently focused on
    const currentElement = this.focusedElement;

    // Handle horizontal navigation within media actions (play/wishlist buttons)
    if (currentElement.closest('.media-actions')) {
      const buttons = Array.from(document.querySelectorAll('.media-actions button'));
      const currentIndex = buttons.indexOf(currentElement);

      if (currentIndex !== -1) {
        const nextIndex = currentIndex + hDirection;
        if (nextIndex >= 0 && nextIndex < buttons.length) {
          this.focusElement(buttons[nextIndex]);
          return;
        }
      }
    }

    // Handle horizontal navigation within episodes
    const episodesContainer = document.getElementById('episodes');
    const episodesSection = document.querySelector('.episodes-section');

    if (currentElement.closest('#episodes') || currentElement.closest('.episodes-section')) {
      // Look for episode items in both possible containers
      let episodeItems = [];
      if (episodesContainer) {
        episodeItems = Array.from(episodesContainer.querySelectorAll('.grid-item, .episode-card'));
      }
      if (episodeItems.length === 0 && episodesSection) {
        episodeItems = Array.from(episodesSection.querySelectorAll('.grid-item, .episode-card'));
      }

      const currentIndex = episodeItems.indexOf(currentElement);
      if (currentIndex !== -1) {
        const nextIndex = currentIndex + hDirection;
        if (nextIndex >= 0 && nextIndex < episodeItems.length) {
          this.focusElement(episodeItems[nextIndex]);
          return;
        }
      }
    }

    // Handle horizontal navigation within providers
    const providersContainer = document.getElementById('providers');
    const providersSection = document.querySelector('.providers-section');

    if (currentElement.closest('#providers') || currentElement.closest('.providers-section')) {
      const isSourceButton = currentElement.classList.contains('source-link');

      if (isSourceButton) {
        // Navigate between source buttons
        let sourceButtons = [];
        if (providersContainer) {
          sourceButtons = Array.from(providersContainer.querySelectorAll('.source-link'));
        }
        if (sourceButtons.length === 0 && providersSection) {
          sourceButtons = Array.from(providersSection.querySelectorAll('.source-link'));
        }

        const currentIndex = sourceButtons.indexOf(currentElement);
        if (currentIndex !== -1) {
          const nextIndex = currentIndex + hDirection;
          if (nextIndex >= 0 && nextIndex < sourceButtons.length) {
            this.focusElement(sourceButtons[nextIndex]);
            return;
          }
        }
      } else {
        // Navigate between provider items
        let providerItems = [];
        if (providersContainer) {
          providerItems = Array.from(providersContainer.querySelectorAll('.grid-item, .provider-card'));
        }
        if (providerItems.length === 0 && providersSection) {
          providerItems = Array.from(providersSection.querySelectorAll('.grid-item, .provider-card'));
        }

        const currentIndex = providerItems.indexOf(currentElement);
        if (currentIndex !== -1) {
          const nextIndex = currentIndex + hDirection;
          if (nextIndex >= 0 && nextIndex < providerItems.length) {
            this.focusElement(providerItems[nextIndex]);
            return;
          }
        }
      }
    }

    // Handle horizontal navigation within seasons
    const seasonsContainer = document.getElementById('seasons');
    if (seasonsContainer && currentElement.closest('#seasons')) {
      const seasonItems = Array.from(seasonsContainer.querySelectorAll('.grid-item'));
      const currentIndex = seasonItems.indexOf(currentElement);

      if (currentIndex !== -1) {
        const nextIndex = currentIndex + hDirection;
        if (nextIndex >= 0 && nextIndex < seasonItems.length) {
          this.focusElement(seasonItems[nextIndex]);
          return;
        }
      }
    }
  }



  navigateContentAreas(vDirection) {
    const areas = this.contentAreas[this.currentSection] || [];
    if (areas.length === 0) return;

    console.log(`Navigating content areas in section ${this.currentSection}, direction: ${vDirection}`);
    console.log(`Current focus: ${this.focusedElement ? this.focusedElement.id || this.focusedElement.className : 'none'}`);
    console.log(`Available areas: ${areas.join(', ')}`);

    // Direct navigation to carousels from sort dropdown
    if (this.focusedElement && this.focusedElement.tagName === 'SELECT' && this.focusedElement.id.endsWith('-sort')) {
      console.log('Navigating from sort dropdown');

      // If navigating down from sort dropdown, find the first visible carousel with content
      if (vDirection > 0) {
        const section = document.getElementById(this.currentSection);
        if (section) {
          // Get all carousel containers and find the first one with visible content
          const allCarousels = Array.from(section.querySelectorAll('.trending-carousel-container, .carousel-container, .genre-carousel'));

          for (const carouselContainer of allCarousels) {
            // Check if carousel is visible
            const style = window.getComputedStyle(carouselContainer);
            if (style.display === 'none' || style.visibility === 'hidden') {
              continue;
            }

            // Check if carousel has items
            const carouselItems = carouselContainer.querySelector('.carousel-items');
            if (carouselItems && carouselItems.children.length > 0) {
              const firstItem = carouselItems.querySelector('.carousel-item');
              if (firstItem) {
                console.log('Focusing first item in first visible carousel with content');
                this.focusElement(firstItem);
                return;
              }
            }
          }

          // If no carousels with items, try to find a grid item
          const grid = section.querySelector('.grid');
          if (grid) {
            const firstGridItem = grid.querySelector('.grid-item');
            if (firstGridItem) {
              console.log('Focusing first grid item from sort (no carousels available)');
              this.focusElement(firstGridItem);
              return;
            }
          }
        }
      }

      // If navigating up from sort dropdown, go to search bar
      if (vDirection < 0) {
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
          console.log('Navigating to search input');
          this.focusElement(searchInput);
          return;
        }
      }
    }

    // If we're in a carousel and navigating down, try to find the next carousel or grid
    if (this.focusedElement && this.focusedElement.closest('.carousel-items') && vDirection > 0) {
      // Find the current carousel container - handle both trending-carousel-container and carousel-container
      const currentCarousel = this.focusedElement.closest('.trending-carousel-container') ||
                              this.focusedElement.closest('.carousel-container');

      if (currentCarousel) {
        console.log('Navigating down from carousel:', currentCarousel.id);

        // Get all carousel containers in the section (including genre carousels)
        const section = document.getElementById(this.currentSection);
        if (section) {
          const allCarousels = Array.from(section.querySelectorAll('.trending-carousel-container, .carousel-container, .genre-carousel'));
          const currentIndex = allCarousels.indexOf(currentCarousel);

          if (currentIndex !== -1) {
            // Look for the next visible carousel with content
            for (let i = currentIndex + 1; i < allCarousels.length; i++) {
              const nextCarousel = allCarousels[i];

              // Check if carousel is visible
              const style = window.getComputedStyle(nextCarousel);
              if (style.display === 'none' || style.visibility === 'hidden') {
                continue;
              }

              // Check if carousel has items
              const carouselItems = nextCarousel.querySelector('.carousel-items');
              if (carouselItems && carouselItems.children.length > 0) {
                const firstItem = carouselItems.querySelector('.carousel-item');
                if (firstItem) {
                  console.log('Focusing first item in next visible carousel:', nextCarousel.id);
                  this.focusElement(firstItem);
                  return;
                }
              }
            }

            // If no next visible carousel found, try to find the grid
          } else {
            // We're at the last carousel, try to find the grid

            // First try to find the grid list with ID format: section-list (e.g., movies-list)
            const gridListId = `${this.currentSection}-list`;
            const gridList = document.getElementById(gridListId);

            if (gridList) {
              const firstGridItem = gridList.querySelector('.grid-item');
              if (firstGridItem) {
                console.log(`Found grid list with ID ${gridListId}`);
                this.focusElement(firstGridItem);
                return;
              }
            }

            // Fallback to any grid in the section
            const grid = section.querySelector('.grid');
            if (grid) {
              const firstGridItem = grid.querySelector('.grid-item');
              if (firstGridItem) {
                console.log('Focusing first grid item');
                this.focusElement(firstGridItem);
                return;
              }
            }
          }
        }
      }
    }

    // If we're in a carousel and navigating up, try to find the previous carousel or sort dropdown
    if (this.focusedElement && this.focusedElement.closest('.carousel-items') && vDirection < 0) {
      // Find the current carousel container - handle both trending-carousel-container and carousel-container
      const currentCarousel = this.focusedElement.closest('.trending-carousel-container') ||
                              this.focusedElement.closest('.carousel-container');

      if (currentCarousel) {
        console.log('Navigating up from carousel:', currentCarousel.id);

        // Get all carousel containers in the section (including genre carousels)
        const section = document.getElementById(this.currentSection);
        if (section) {
          const allCarousels = Array.from(section.querySelectorAll('.trending-carousel-container, .carousel-container, .genre-carousel'));
          const currentIndex = allCarousels.indexOf(currentCarousel);

          if (currentIndex > 0) {
            // Look for the previous visible carousel with content
            for (let i = currentIndex - 1; i >= 0; i--) {
              const prevCarousel = allCarousels[i];

              // Check if carousel is visible
              const style = window.getComputedStyle(prevCarousel);
              if (style.display === 'none' || style.visibility === 'hidden') {
                continue;
              }

              // Check if carousel has items
              const carouselItems = prevCarousel.querySelector('.carousel-items');
              if (carouselItems && carouselItems.children.length > 0) {
                const firstItem = carouselItems.querySelector('.carousel-item');
                if (firstItem) {
                  console.log('Focusing first item in previous visible carousel:', prevCarousel.id);
                  this.focusElement(firstItem);
                  return;
                }
              }
            }

            // If no previous visible carousel found, try to find the sort dropdown
          } else {
            // We're at the first carousel, try to find the sort dropdown
            const filterControls = section.querySelector('.filter-controls');
            if (filterControls) {
              const sortDropdown = filterControls.querySelector('select');
              if (sortDropdown) {
                console.log('Focusing sort dropdown');
                this.focusElement(sortDropdown);
                return;
              }
            }
          }
        }
      }
    }

    // Standard area-based navigation as fallback
    let currentAreaIndex = -1;

    if (this.focusedElement) {
      // Find which area contains the focused element
      for (let i = 0; i < areas.length; i++) {
        const area = document.getElementById(areas[i]);
        if (area && (area === this.focusedElement || area.contains(this.focusedElement))) {
          currentAreaIndex = i;
          break;
        }
      }
    }

    console.log(`Current area index: ${currentAreaIndex}`);

    // If no area is found or we're moving vertically
    if (currentAreaIndex === -1 || vDirection !== 0) {
      let nextAreaIndex = currentAreaIndex + vDirection;
      if (nextAreaIndex < 0) nextAreaIndex = 0;
      if (nextAreaIndex >= areas.length) nextAreaIndex = areas.length - 1;

      console.log(`Next area index: ${nextAreaIndex}`);

      if (nextAreaIndex !== currentAreaIndex) {
        const nextArea = document.getElementById(areas[nextAreaIndex]);
        if (nextArea) {
          console.log(`Focusing first element in area: ${areas[nextAreaIndex]}`);
          this.focusFirstInArea(nextArea);
        }
      }
    }
  }

  navigateCarousel(carousel, hDirection, vDirection) {
    if (vDirection !== 0) {
      // Move to next/previous content area
      this.navigateContentAreas(vDirection);
      return;
    }

    // Find the carousel container - handle trending-carousel-container, carousel-container, and genre-carousel classes
    const carouselContainer = carousel.closest('.trending-carousel-container') ||
                              carousel.closest('.carousel-container') ||
                              carousel.closest('.genre-carousel');
    if (!carouselContainer) {
      console.log('Could not find carousel container for:', carousel);
      console.log('Parent elements:', carousel.parentElement, carousel.parentElement?.parentElement);

      // Try to find the carousel container by ID pattern
      const carouselId = carousel.id;
      if (carouselId) {
        const possibleContainerId = carouselId.replace('-items', '');
        const containerByID = document.getElementById(possibleContainerId);
        if (containerByID) {
          console.log('Found carousel container by ID:', possibleContainerId);
          carouselContainer = containerByID;
        }
      }

      if (!carouselContainer) {
        console.error('Could not find carousel container');
        return;
      }
    }

    // Get all carousel items
    const items = Array.from(carousel.querySelectorAll('.carousel-item'));
    if (items.length === 0) {
      console.error('No carousel items found');
      return;
    }

    console.log(`Found ${items.length} carousel items in ${carouselContainer.id || 'unnamed carousel'}`);

    // Make all carousel items focusable
    items.forEach(item => {
      if (!item.hasAttribute('tabindex')) {
        item.setAttribute('tabindex', '0');
      }
    });

    // Find the current index
    let currentIndex = -1;
    if (this.focusedElement && items.includes(this.focusedElement)) {
      currentIndex = items.indexOf(this.focusedElement);
    } else {
      // If no item is focused, focus the first visible item
      const visibleItems = items.filter(item => {
        const rect = item.getBoundingClientRect();
        return rect.width > 0 && rect.height > 0 &&
               rect.left >= 0 && rect.right <= window.innerWidth;
      });

      if (visibleItems.length > 0) {
        this.focusElement(visibleItems[0]);
        currentIndex = items.indexOf(visibleItems[0]);
        return;
      } else if (items.length > 0) {
        // If no visible items, focus the first item and scroll it into view
        this.focusElement(items[0]);
        items[0].scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'start' });
        return;
      }
    }

    // Calculate the next index
    let nextIndex = currentIndex + hDirection;
    if (nextIndex < 0) nextIndex = 0;
    if (nextIndex >= items.length) nextIndex = items.length - 1;

    if (nextIndex !== currentIndex) {
      // Log the navigation
      console.log(`Carousel navigation: from index ${currentIndex} to ${nextIndex}`);
      if (items[nextIndex]) {
        console.log(`Item data:`, {
          id: items[nextIndex].dataset.id,
          type: items[nextIndex].dataset.type
        });
      }

      // Focus the next item
      this.focusElement(items[nextIndex]);

      // Ensure the item is visible by scrolling the carousel
      if (hDirection > 0) {
        items[nextIndex].scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'end' });
      } else {
        items[nextIndex].scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'start' });
      }

      // Also trigger the carousel navigation buttons if needed
      if (carouselContainer) {
        if (hDirection > 0 && nextIndex > items.length - 3) {
          const nextBtn = carouselContainer.querySelector('.carousel-nav.next');
          if (nextBtn) {
            console.log('Clicking next button');
            nextBtn.click();
          }
        } else if (hDirection < 0 && nextIndex < 2) {
          const prevBtn = carouselContainer.querySelector('.carousel-nav.prev');
          if (prevBtn) {
            console.log('Clicking prev button');
            prevBtn.click();
          }
        }
      }
    }
  }

  navigateGrid(grid, hDirection, vDirection) {
    const items = Array.from(grid.querySelectorAll('.grid-item'));
    if (items.length === 0) return;

    if (!this.focusedElement || !items.includes(this.focusedElement)) {
      // Focus first item if none is focused
      this.focusElement(items[0]);
      return;
    }

    // Get the current item's position
    const currentRect = this.focusedElement.getBoundingClientRect();

    if (hDirection !== 0) {
      // Find the next item in the horizontal direction
      let bestMatch = null;
      let bestDistance = Infinity;

      for (const item of items) {
        if (item === this.focusedElement) continue;

        const rect = item.getBoundingClientRect();
        const horizontalDistance = hDirection > 0 ? rect.left - currentRect.right : currentRect.left - rect.right;
        const verticalOverlap = Math.min(rect.bottom, currentRect.bottom) - Math.max(rect.top, currentRect.top);

        // Check if item is in the correct direction and has vertical overlap
        if (horizontalDistance > 0 && verticalOverlap > 0 && hDirection > 0) {
          const distance = horizontalDistance + Math.abs(rect.top - currentRect.top) * 0.1;
          if (distance < bestDistance) {
            bestDistance = distance;
            bestMatch = item;
          }
        } else if (horizontalDistance > 0 && verticalOverlap > 0 && hDirection < 0) {
          const distance = horizontalDistance + Math.abs(rect.top - currentRect.top) * 0.1;
          if (distance < bestDistance) {
            bestDistance = distance;
            bestMatch = item;
          }
        }
      }

      if (bestMatch) {
        this.focusElement(bestMatch);
        bestMatch.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'nearest' });
      } else if (hDirection < 0 && this.isAtLeftEdge()) {
        // If at left edge and trying to go left, move to sidebar
        this.focusSidebar();
      }
    } else if (vDirection !== 0) {
      // Improved vertical navigation using row detection

      // Get all item positions and sort them by vertical position
      const itemPositions = items.map(item => {
        const rect = item.getBoundingClientRect();
        return {
          item,
          rect,
          center: {
            x: rect.left + rect.width / 2,
            y: rect.top + rect.height / 2
          }
        };
      });

      // Group items into rows based on their vertical position
      const rows = [];
      let currentRow = [];
      let lastTop = -1;

      // Sort items by their vertical position
      const sortedItems = [...itemPositions].sort((a, b) => a.rect.top - b.rect.top);

      // Group items into rows based on their vertical position
      // Use a more flexible approach to detect rows
      const rowThreshold = 20; // Increased threshold to better detect rows

      sortedItems.forEach(pos => {
        // If this is a new row (allowing for variations in alignment)
        if (lastTop === -1 || Math.abs(pos.rect.top - lastTop) > rowThreshold) {
          if (currentRow.length > 0) {
            // Sort the current row by horizontal position before adding it
            currentRow.sort((a, b) => a.rect.left - b.rect.left);
            rows.push(currentRow);
          }
          currentRow = [pos];
          lastTop = pos.rect.top;
        } else {
          currentRow.push(pos);
        }
      });

      // Add the last row
      if (currentRow.length > 0) {
        // Sort the last row by horizontal position
        currentRow.sort((a, b) => a.rect.left - b.rect.left);
        rows.push(currentRow);
      }

      console.log(`Grid navigation: Found ${rows.length} rows with ${rows.map(r => r.length).join(', ')} items per row`);

      // Find the current row index and item index within the row
      let currentRowIndex = -1;
      let currentItemInRow = null;
      let currentItemIndexInRow = -1;

      for (let i = 0; i < rows.length; i++) {
        for (let j = 0; j < rows[i].length; j++) {
          if (rows[i][j].item === this.focusedElement) {
            currentRowIndex = i;
            currentItemInRow = rows[i][j];
            currentItemIndexInRow = j;
            break;
          }
        }
        if (currentRowIndex !== -1) break;
      }

      if (currentRowIndex === -1 || !currentItemInRow) {
        console.error('Could not find current row');
        return;
      }

      console.log(`Current position: row ${currentRowIndex}, item ${currentItemIndexInRow}`);

      // Calculate the target row index
      const targetRowIndex = currentRowIndex + vDirection;
      if (targetRowIndex < 0 || targetRowIndex >= rows.length) {
        // No more rows in this direction, try to navigate to the next/previous content area
        console.log(`Navigating out of grid: direction ${vDirection}`);
        this.navigateContentAreas(vDirection);
        return;
      }

      // Try to find the item at the same position in the target row
      let targetItem = null;

      // First try to get the item at the same index in the target row
      if (currentItemIndexInRow < rows[targetRowIndex].length) {
        targetItem = rows[targetRowIndex][currentItemIndexInRow].item;
        console.log(`Found item at same position in target row ${targetRowIndex}`);
      }

      // If that fails, find the closest item horizontally
      if (!targetItem) {
        const currentX = currentItemInRow.center.x;
        let closestItem = null;
        let minDistance = Infinity;

        for (const pos of rows[targetRowIndex]) {
          const distance = Math.abs(pos.center.x - currentX);
          if (distance < minDistance) {
            minDistance = distance;
            closestItem = pos.item;
          }
        }

        targetItem = closestItem;
        console.log(`Found closest item in target row ${targetRowIndex}`);
      }

      if (targetItem) {
        console.log(`Focusing item in row ${targetRowIndex}`);
        this.focusElement(targetItem);
        targetItem.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'nearest' });
      } else {
        // If no match found, try to navigate to next/previous content area
        console.log('No target item found, navigating out of grid');
        this.navigateContentAreas(vDirection);
      }
    }
  }

  navigateCategories(categories, hDirection, vDirection) {
    if (vDirection !== 0) {
      // Move to next/previous content area
      this.navigateContentAreas(vDirection);
      return;
    }

    const items = Array.from(categories.querySelectorAll('.category-item'));
    if (items.length === 0) return;

    let currentIndex = -1;
    if (this.focusedElement && items.includes(this.focusedElement)) {
      currentIndex = items.indexOf(this.focusedElement);
    }

    let nextIndex = currentIndex + hDirection;
    if (nextIndex < 0) nextIndex = 0;
    if (nextIndex >= items.length) nextIndex = items.length - 1;

    if (nextIndex !== currentIndex) {
      this.focusElement(items[nextIndex]);

      // Ensure the item is visible by scrolling
      if (hDirection > 0) {
        items[nextIndex].scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'end' });
      } else {
        items[nextIndex].scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'start' });
      }
    }
  }

  navigateChannelsGrid(channelsGrid, hDirection, vDirection) {
    if (!channelsGrid) {
      console.error('Channel grid not found');
      return;
    }

    // If navigating vertically out of the grid
    if (vDirection !== 0) {
      const currentRect = this.focusedElement.getBoundingClientRect();

      if (vDirection < 0) {
        // Try to navigate to categories when going up
        const categoriesContainer = document.getElementById('livetv-categories');
        if (categoriesContainer) {
          const categoryItems = categoriesContainer.querySelectorAll('.category-item');
          if (categoryItems.length > 0) {
            // Find the category item that's most aligned horizontally with the current channel card
            let bestMatch = categoryItems[0];
            let bestDistance = Infinity;

            Array.from(categoryItems).forEach(item => {
              const rect = item.getBoundingClientRect();
              const horizontalDistance = Math.abs(rect.left - currentRect.left);
              if (horizontalDistance < bestDistance) {
                bestDistance = horizontalDistance;
                bestMatch = item;
              }
            });

            this.focusElement(bestMatch);
            return;
          }
        }

        // If no categories found, try to navigate to search input
        const searchInput = document.getElementById('livetv-search-input');
        if (searchInput) {
          this.focusElement(searchInput);
          return;
        }
      } else if (vDirection > 0) {
        // Try to navigate to guide when going down
        const guideContainer = document.getElementById('livetv-guide');
        if (guideContainer) {
          this.focusElement(guideContainer);
          return;
        }
      }

      // If we couldn't navigate vertically, use the standard content area navigation
      this.navigateContentAreas(vDirection);
      return;
    }

    // Get all channel cards
    const channelCards = Array.from(channelsGrid.querySelectorAll('.channel-card'));
    if (channelCards.length === 0) {
      console.log('No channel cards found in the grid');
      return;
    }

    console.log(`Found ${channelCards.length} channel cards in the grid`);

    // Make all channel cards focusable
    channelCards.forEach(card => {
      if (!card.hasAttribute('tabindex')) {
        card.setAttribute('tabindex', '0');
      }
    });

    // If no card is currently focused, focus the first one
    if (!this.focusedElement || !channelCards.includes(this.focusedElement)) {
      this.focusElement(channelCards[0]);
      return;
    }

    // Get the current position in the grid
    const currentIndex = channelCards.indexOf(this.focusedElement);
    if (currentIndex === -1) {
      console.log('Current element not found in channel cards');
      this.focusElement(channelCards[0]);
      return;
    }

    // Calculate the next index based on direction
    let nextIndex = currentIndex + hDirection;
    if (nextIndex < 0) nextIndex = 0;
    if (nextIndex >= channelCards.length) nextIndex = channelCards.length - 1;

    if (nextIndex !== currentIndex) {
      console.log(`Navigating from channel card ${currentIndex} to ${nextIndex}`);
      this.focusElement(channelCards[nextIndex]);

      // Ensure the card is visible
      channelCards[nextIndex].scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: hDirection > 0 ? 'end' : 'start'
      });
    }
  }

  navigatePlayerControls() {
    const player = document.getElementById('player');
    if (!player) return;

    // For now, just focus the player itself
    this.focusElement(player);

    // In a more advanced implementation, you could navigate between
    // custom player controls (play/pause, volume, etc.)
  }

  focusSidebar() {
    this.currentFocusArea = 'sidebar';

    // Focus the last focused sidebar element or the first one
    if (this.lastSidebarFocus) {
      this.focusElement(this.lastSidebarFocus);
    } else {
      const sidebarLinks = document.querySelectorAll('.sidebar a');
      if (sidebarLinks.length > 0) {
        this.focusElement(sidebarLinks[0]);
        this.lastSidebarFocus = sidebarLinks[0];
      }
    }
  }

  focusContent() {
    this.currentFocusArea = 'content';

    // Always focus the first element in the current section when coming from sidebar
    // This ensures we start at the top of the newly selected section
    const section = document.getElementById(this.currentSection);
    if (section) {
      this.focusFirstInArea(section);
    } else if (this.lastContentFocus && document.body.contains(this.lastContentFocus)) {
      // Fallback to last focused element if section not found
      this.focusElement(this.lastContentFocus);
    }
  }

  focusFirstInArea(area) {
    if (!area) return;

    console.log(`Focusing first element in area: ${area.id || area.className}`);

    // Special handling for Live TV sections
    if (area.id === 'livetv-search-button') {
      console.log('Focusing Live TV search button');
      this.focusElement(area);
      return;
    }

    if (area.id === 'livetv-categories') {
      const categoryItems = area.querySelectorAll('.category-item');
      if (categoryItems.length > 0) {
        console.log('Focusing first category item');
        this.focusElement(categoryItems[0]);
        return;
      }
    }

    if (area.id === 'livetv-channels') {
      const channelCards = area.querySelectorAll('.channel-card');
      if (channelCards.length > 0) {
        console.log('Focusing first channel card');
        this.focusElement(channelCards[0]);
        return;
      }
    }

    // Special handling for carousel areas
    if (area.id && (area.id.includes('carousel') || area.classList.contains('trending-carousel-container'))) {
      // For carousel areas, directly focus the first carousel item
      const carouselItems = area.querySelector('.carousel-items');
      if (carouselItems) {
        const firstItem = carouselItems.querySelector('.carousel-item');
        if (firstItem) {
          console.log('Directly focusing first carousel item');
          this.focusElement(firstItem);
          return;
        }
      }
    }

    // Special handling for trending carousel containers
    if (area.classList.contains('trending-carousel-container') || area.classList.contains('carousel-container')) {
      const carouselItems = area.querySelector('.carousel-items');
      if (carouselItems) {
        const firstItem = carouselItems.querySelector('.carousel-item');
        if (firstItem) {
          console.log('Directly focusing first carousel item in trending container');
          this.focusElement(firstItem);
          return;
        }
      }
    }

    // Special handling for grid areas
    if (area.classList.contains('grid')) {
      const firstGridItem = area.querySelector('.grid-item');
      if (firstGridItem) {
        console.log('Directly focusing first grid item');
        this.focusElement(firstGridItem);
        return;
      }
    }

    // Special handling for hero sections
    if (area.id && area.id.includes('hero')) {
      const heroButtons = area.querySelectorAll('.hero-btn');
      if (heroButtons.length > 0) {
        console.log('Focusing first hero button');
        this.focusElement(heroButtons[0]);
        return;
      }
    }

    // Special handling for filter controls
    if (area.id && area.id.includes('sort')) {
      const sortSelect = area.querySelector('select');
      if (sortSelect) {
        console.log('Focusing sort select');
        this.focusElement(sortSelect);
        return;
      }
    }

    // If the area is a section, try to find the first interactive element within it
    if (area.classList.contains('section')) {
      // Special handling for Live TV section
      if (area.id === 'livetv') {
        const searchInput = document.getElementById('livetv-search-input');
        if (searchInput) {
          console.log('Focusing Live TV search input');
          this.focusElement(searchInput);
          return;
        }
      }

      // First try to find hero buttons
      const heroButtons = area.querySelectorAll('.hero-btn');
      if (heroButtons.length > 0) {
        console.log('Focusing first hero button in section');
        this.focusElement(heroButtons[0]);
        return;
      }

      // Then try to find filter controls
      const filterControls = area.querySelector('.filter-controls select');
      if (filterControls) {
        console.log('Focusing filter controls in section');
        this.focusElement(filterControls);
        return;
      }

      // Then try to find a visible carousel with content
      const allCarousels = Array.from(area.querySelectorAll('.trending-carousel-container, .carousel-container, .genre-carousel'));
      for (const carouselContainer of allCarousels) {
        // Check if carousel is visible
        const style = window.getComputedStyle(carouselContainer);
        if (style.display === 'none' || style.visibility === 'hidden') {
          continue;
        }

        // Check if carousel has items
        const carouselItems = carouselContainer.querySelector('.carousel-items');
        if (carouselItems && carouselItems.children.length > 0) {
          const firstItem = carouselItems.querySelector('.carousel-item');
          if (firstItem) {
            console.log('Focusing first item in first visible carousel with content');
            this.focusElement(firstItem);
            return;
          }
        }
      }

      // If no carousel, try to find a grid
      const grid = area.querySelector('.grid');
      if (grid) {
        const firstGridItem = grid.querySelector('.grid-item');
        if (firstGridItem) {
          console.log('Focusing first grid item in section');
          this.focusElement(firstGridItem);
          return;
        }
      }

      // For Live TV section, try to find channel cards
      const channelsGrid = area.querySelector('#livetv-channels');
      if (channelsGrid) {
        const channelCards = channelsGrid.querySelectorAll('.channel-card');
        if (channelCards.length > 0) {
          console.log('Focusing first channel card in Live TV section');
          this.focusElement(channelCards[0]);
          return;
        }
      }
    }

    // Try to find the first focusable element in the area
    const focusables = this.getFocusableElements(area);

    console.log(`Found ${focusables.length} focusable elements in area`);

    if (focusables.length > 0) {
      console.log(`Focusing element: ${focusables[0].id || focusables[0].className}`);
      this.focusElement(focusables[0]);
    } else {
      console.log('No focusable elements found in area');
    }
  }

  getFocusableElements(container) {
    // Get all focusable elements in a container
    const selector = 'a, button, input, select, textarea, [tabindex]:not([tabindex="-1"]), .grid-item, .carousel-item, .category-item, .source-link, .channel-card, .hero-btn, .carousel-nav, .carousel-refresh-btn';
    return Array.from(container.querySelectorAll(selector)).filter(el => {
      return el.offsetWidth > 0 && el.offsetHeight > 0 && !el.disabled;
    });
  }

  focusElement(element) {
    if (!element) return;

    // Remove focus from current element
    if (this.focusedElement) {
      this.focusedElement.classList.remove('remote-focus');
      if (this.focusedElement.blur) {
        this.focusedElement.blur();
      }
    }

    // Add focus to new element
    this.focusedElement = element;
    this.focusedElement.classList.add('remote-focus');

    // Scroll into view if needed
    const rect = element.getBoundingClientRect();
    const isInViewport = (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <= window.innerHeight &&
      rect.right <= window.innerWidth
    );

    if (!isInViewport) {
      element.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }

    // Update last focus references
    if (this.currentFocusArea === 'sidebar') {
      this.lastSidebarFocus = element;
    } else if (this.currentFocusArea === 'content') {
      this.lastContentFocus = element;
    }

    // Try to focus the element for screen readers
    if (element.focus) {
      try {
        element.focus({ preventScroll: true });
      } catch (e) {
        // Ignore focus errors
      }
    }
  }

  activateFocused() {
    if (!this.focusedElement) return;

    // Special handling for sidebar links
    if (this.currentFocusArea === 'sidebar' && this.focusedElement.tagName === 'A' && this.focusedElement.closest('.sidebar')) {
      // For sidebar links, we already handle navigation on focus, so just ensure it's activated
      this.activateSidebarItem(this.focusedElement);
      return;
    }

    // Special handling for different element types before simulating click
    if (this.focusedElement.tagName === 'INPUT') {
      // For search inputs, focus them properly
      this.focusedElement.focus();
      return;
    } else if (this.focusedElement.classList.contains('channel-card')) {
      // For channel cards, use the LiveTV manager to handle the click
      const channelId = this.focusedElement.dataset.id;
      if (channelId && window.liveTVManager) {
        console.log(`Activating channel card with ID: ${channelId}`);

        // Find the channel in the LiveTV manager
        const channel = window.liveTVManager.channels.find(c => c.id === channelId);
        if (channel && channel.isOnline) {
          console.log('Channel found, playing it');
          window.liveTVManager.handleChannelClick(channel);
        } else {
          console.log('Channel not found or offline');
          // Still click the element to trigger any UI feedback
          this.focusedElement.click();
        }
        return;
      }
    } else if (this.focusedElement.classList.contains('grid-item')) {
      // For grid items, navigate to the item page directly without simulating click
      const id = this.focusedElement.dataset.id;
      const type = this.focusedElement.dataset.type;

      // Check if we're on a media page and this is an episode grid item
      const isMediaPage = window.location.pathname.match(/^\/(movies|series|anime|livetv)\/\w+$/);
      const isEpisodeItem = this.focusedElement.closest('#episodes');

      if (isMediaPage && isEpisodeItem) {
        // Handle episode selection on media page
        if (this.focusedElement.classList.contains('grid-item') || this.focusedElement.classList.contains('episode-card')) {
          console.log('Selecting episode:', this.focusedElement.dataset.ep);
          this.focusedElement.click();
        } else {
          // We're on the empty container, try to trigger content loading
          console.log('Activating empty episodes container - checking for content');
          this.handleEmptyContainerActivation('episodes');
        }
        return;
      }

      // Handle provider selection on media page
      const isProviderItem = this.focusedElement.closest('#providers');
      if (isMediaPage && isProviderItem) {
        console.log('Selecting provider:', this.focusedElement);

        // Check if we're on an actual provider item or just the container
        if (this.focusedElement.classList.contains('grid-item') || this.focusedElement.classList.contains('provider-card')) {
          // If we're on a provider item but not a source button, try to focus the source button
          if (!this.focusedElement.classList.contains('source-link')) {
            const sourceButton = this.focusedElement.querySelector('.source-link');
            if (sourceButton) {
              console.log('Focusing source button within provider');
              this.focusElement(sourceButton);
              return;
            }
          }

          // Otherwise click the element
          this.focusedElement.click();
        } else {
          // We're on the empty container, try to trigger content loading
          console.log('Activating empty providers container - checking for content');
          this.handleEmptyContainerActivation('providers');
        }
        return;
      }

      // Handle season selection on media page
      const isSeasonItem = this.focusedElement.closest('#seasons');
      if (isMediaPage && isSeasonItem) {
        console.log('Selecting season:', this.focusedElement);
        this.focusedElement.click();
        return;
      }

      // Regular grid item navigation to media page
      if (id && type) {
        console.log(`Navigating to ${type}/${id} from grid item`);

        // Directly navigate to the media page without using any event handlers
        // This bypasses any click handlers that might be causing the wrong redirection
        setTimeout(() => {
          window.location.href = `/${type}/${id}`;
        }, 0);
        return;
      }
    } else if (this.focusedElement.classList.contains('carousel-item')) {
      // For carousel items, navigate to the item page directly without simulating click
      const id = this.focusedElement.dataset.id;
      const type = this.focusedElement.dataset.type;
      if (id && type) {
        console.log(`Navigating to ${type}/${id} from carousel item`);

        // Directly navigate to the media page without using any event handlers
        // This bypasses any click handlers that might be causing the wrong redirection
        setTimeout(() => {
          window.location.href = `/${type}/${id}`;
        }, 0);
        return;
      }
    } else if (this.focusedElement.classList.contains('category-item')) {
      // For category items, filter the channels
      const category = this.focusedElement.dataset.category;
      if (category && window.liveTVManager) {
        console.log(`Selecting category: ${category}`);
        window.liveTVManager.handleCategoryClick(category);
        return;
      }
    }

    // For all other elements, simulate a click
    console.log('Clicking element:', this.focusedElement);
    this.focusedElement.click();
  }

  handleWheel(event) {
    // Handle mouse wheel scrolling

    // Don't interfere with normal scrolling if we're not in a carousel or grid
    if (!this.focusedElement) return;

    // Check if we're in a carousel
    const carousel = this.focusedElement.closest('.carousel-items');
    if (carousel) {
      // For carousels, we want to handle horizontal scrolling with the wheel
      // Prevent the default vertical scrolling
      event.preventDefault();

      // Determine scroll direction and amount
      const isHorizontalScroll = Math.abs(event.deltaX) > Math.abs(event.deltaY);
      const delta = isHorizontalScroll ? event.deltaX : event.deltaY;
      const scrollAmount = 200 * Math.sign(delta); // Adjust scroll speed

      // Scroll the carousel
      carousel.scrollBy({
        left: scrollAmount,
        behavior: 'smooth'
      });

      return;
    }

    // Check if we're in a grid
    const grid = this.focusedElement.closest('.grid');
    if (grid) {
      // For grids, we want to allow normal scrolling but also handle focus changes
      // Don't prevent default to allow normal scrolling

      // If the wheel event is significant enough, change focus
      if (Math.abs(event.deltaY) > 50) {
        const direction = Math.sign(event.deltaY);
        this.navigateGrid(grid, 0, direction);
      }

      return;
    }
  }

  handleEscape() {
    // Check if player is open
    const playerContainer = document.getElementById('player-container');
    if (playerContainer && !playerContainer.classList.contains('hidden')) {
      // Close the player
      if (typeof hidePlayer === 'function') {
        hidePlayer();
      } else if (window.liveTVManager && typeof window.liveTVManager.hidePlayer === 'function') {
        window.liveTVManager.hidePlayer();
      } else {
        playerContainer.classList.add('hidden');
        playerContainer.style.display = 'none';
      }
      return;
    }

    // If in content area, go back to sidebar
    if (this.currentFocusArea === 'content') {
      this.focusSidebar();
    }
  }

  cleanup() {
    // Remove event listeners
    document.removeEventListener('keydown', this.handleKeyDown);
    document.removeEventListener('wheel', this.handleWheel);

    // Disconnect observer
    if (this.observer) {
      this.observer.disconnect();
    }

    // Disconnect content observers
    if (this.contentObservers) {
      this.contentObservers.forEach(observer => observer.disconnect());
      this.contentObservers = [];
    }
  }
}
