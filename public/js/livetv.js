// File: public/js/livetv.js
// Lightweight LiveTV implementation with two-part design

document.addEventListener('DOMContentLoaded', async () => {
  console.log('LiveTV.js script loaded');

  // Initialize LiveTV manager
  const liveTVManager = new LiveTVManager();
  window.liveTVManager = liveTVManager;

  // Initialize when navigating to LiveTV
  window.addEventListener('hashchange', async () => {
    if (window.location.hash === '#livetv') {
      await liveTVManager.initialize();
    } else {
      // Restore main UI when leaving LiveTV
      liveTVManager.showMainUI();
    }
  });

  // Initialize if already on LiveTV
  if (window.location.hash === '#livetv') {
    await liveTVManager.initialize();
  }
});

class LiveTVManager {
  constructor() {
    // DOM elements
    this.playerVideo = document.getElementById('livetv-player-video');
    this.playerIframe = document.getElementById('livetv-player-iframe');
    this.channelSelector = document.getElementById('livetv-channel-selector');
    this.favoritesList = document.getElementById('livetv-favorites-list');
    this.favoritesBack = document.getElementById('livetv-favorites-back');

    // Channel cards
    this.channelCards = {
      'prev-2': document.getElementById('livetv-prev-2'),
      'prev-1': document.getElementById('livetv-prev-1'),
      'current': document.getElementById('livetv-current'),
      'next-1': document.getElementById('livetv-next-1'),
      'next-2': document.getElementById('livetv-next-2')
    };

    // Favorites cards
    this.favoritesCards = {
      'prev-2': document.getElementById('livetv-fav-prev-2'),
      'prev-1': document.getElementById('livetv-fav-prev-1'),
      'current': document.getElementById('livetv-fav-current'),
      'next-1': document.getElementById('livetv-fav-next-1'),
      'next-2': document.getElementById('livetv-fav-next-2')
    };

    // Controls
    this.playPauseBtn = document.getElementById('livetv-play-pause');
    this.fullscreenBtn = document.getElementById('livetv-fullscreen-btn');
    this.sidebarTrigger = document.getElementById('livetv-sidebar-trigger');



    // State
    this.channels = [];
    this.currentChannelIndex = -1;
    this.filteredChannels = [];
    this.showFavoritesOnly = false;
    this.favorites = this.loadFavorites();
    this.currentHlsInstance = null;
    this.isInFavoritesList = false;
    this.currentFavoriteIndex = 0;
    this.isInControlsMode = false;
    this.currentControlIndex = 0;
    this.controlButtons = [];
    this.isChannelSelectorActivated = false; // New state to track activation
    this.isSidebarExplicitlyShown = false; // Track if sidebar is intentionally shown

    this.setupEventListeners();
  }

  setupEventListeners() {

    // Favorite icons click handlers
    this.setupFavoriteIconListeners();

    // Favorites list navigation
    this.favoritesBack?.addEventListener('click', () => this.hideFavoritesList());



    // Play/Pause control
    this.playPauseBtn?.addEventListener('click', () => this.togglePlayPause());

    // Fullscreen control
    this.fullscreenBtn?.addEventListener('click', () => this.toggleFullscreen());

    // Listen for fullscreen changes to update button icon
    document.addEventListener('fullscreenchange', () => this.updateFullscreenIcon());
    document.addEventListener('webkitfullscreenchange', () => this.updateFullscreenIcon());
    document.addEventListener('msfullscreenchange', () => this.updateFullscreenIcon());

    // Sidebar hover functionality
    this.setupSidebarHover();

    // Ensure LiveTV maintains focus when needed
    document.addEventListener('focusin', (e) => {
      // If we're in LiveTV and something outside tries to get focus, redirect to LiveTV
      if (window.location.hash === '#livetv' && document.body.classList.contains('livetv-focus-locked')) {
        const liveTVSection = document.getElementById('livetv');
        const sidebar = document.querySelector('.sidebar');

        // Only redirect focus from sidebar if it's NOT explicitly shown
        if (sidebar?.contains(e.target) && liveTVSection && !this.isSidebarExplicitlyShown) {
          e.preventDefault();
          liveTVSection.focus();
        }
      }
    });

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
      if (window.location.hash === '#livetv') {
        // If sidebar is explicitly shown and user presses right arrow, return to channel selector
        if (this.isSidebarExplicitlyShown && e.key === 'ArrowRight') {
          e.preventDefault();
          this.hideSidebar();
          const liveTVSection = document.getElementById('livetv');
          if (liveTVSection) {
            liveTVSection.focus();
          }
          return;
        }

        // If sidebar is explicitly shown, completely step aside for remote control manager
        if (this.isSidebarExplicitlyShown) {
          // Reset auto-hide timer on any interaction
          this.setupSidebarAutoHide();

          // Don't prevent default or handle the event - let it bubble to remote control manager
          // The remote control manager will handle sidebar navigation
          return;
        }
        if (this.isInControlsMode) {
          // Navigation within TV controls
          if (e.key === 'ArrowLeft') {
            e.preventDefault();
            this.previousControl();
          } else if (e.key === 'ArrowRight') {
            e.preventDefault();
            this.nextControl();
          } else if (e.key === 'Enter') {
            e.preventDefault();
            this.activateCurrentControl();
          } else if (e.key === 'Escape' || e.key === 'ArrowUp') {
            e.preventDefault();
            this.exitControlsMode();
          }
        } else if (this.isInFavoritesList) {
          // Navigation within favorites list
          if (e.key === 'ArrowUp') {
            e.preventDefault();
            this.previousFavorite();
          } else if (e.key === 'ArrowDown') {
            e.preventDefault();
            this.nextFavorite();
          } else if (e.key === 'ArrowLeft' || e.key === 'Escape') {
            e.preventDefault();
            this.hideFavoritesList();
          } else if (e.key === 'Enter') {
            e.preventDefault();
            this.selectFavoriteChannel();
          }
        } else {
          // Check if channel selector is activated first
          if (!this.isChannelSelectorActivated) {
            // Only allow activation via right arrow or click
            if (e.key === 'ArrowRight') {
              e.preventDefault();
              this.activateChannelSelector();
            } else if (e.key === 'ArrowLeft') {
              e.preventDefault();
              // Show sidebar on left arrow
              this.showSidebarExplicitly();
            } else {
              // Show activation hint for other keys
              this.showActivationHint();
            }
          } else {
            // Navigation within main channel selector (after activation)
            if (e.key === 'ArrowUp') {
              e.preventDefault();
              this.previousChannel();
            } else if (e.key === 'ArrowDown') {
              e.preventDefault();
              if (e.shiftKey) {
                // Shift + Down arrow to access controls
                this.enterControlsMode();
              } else {
                this.nextChannel();
              }
            } else if (e.key === 'ArrowRight') {
              e.preventDefault();
              // Show favorites list
              this.showFavoritesList();
            } else if (e.key === 'ArrowLeft') {
              e.preventDefault();
              // Show sidebar on left arrow and allow navigation
              this.showSidebarExplicitly();
            } else if (e.key === 'Escape') {
              this.deactivateChannelSelector();
            } else if (e.key === ' ') {
              e.preventDefault();
              this.togglePlayPause();
            } else if (e.key === 'Enter') {
              e.preventDefault();
              // Toggle favorite for current channel on Enter
              if (this.channelSelector?.classList.contains('show') && this.currentChannelIndex >= 0) {
                const currentChannel = this.filteredChannels[this.currentChannelIndex];
                if (currentChannel) {
                  this.toggleChannelFavorite(currentChannel.id);
                }
              }
            }
          }
        }
      }
    });

    // Auto-hide selector after delay
    this.selectorTimeout = null;
    this.autoHideSelector = () => {
      clearTimeout(this.selectorTimeout);
      this.selectorTimeout = setTimeout(() => {
        this.hideChannelSelector();
        this.hideTVControls(); // Hide TV controls with selector
      }, 5000);
    };

    // Show selector on mouse activity and reset timer
    let mouseMoveTimeout;
    document.addEventListener('mousemove', () => {
      if (window.location.hash === '#livetv') {
        // Debounce mouse move events
        clearTimeout(mouseMoveTimeout);
        mouseMoveTimeout = setTimeout(() => {
          this.showChannelSelector();
          this.autoHideSelector();
        }, 100);
      }
    });

    // Activate channel selector on any click in LiveTV area
    document.addEventListener('click', (e) => {
      if (window.location.hash === '#livetv') {
        this.activateChannelSelector();
      }
    });

    // Listen for hash changes to detect when leaving LiveTV
    window.addEventListener('hashchange', () => {
      if (window.location.hash !== '#livetv') {
        console.log('LiveTV: Detected navigation away from LiveTV section');
        this.cleanup();
      }
    });

    // Also listen for page unload/beforeunload to cleanup
    window.addEventListener('beforeunload', () => {
      if (window.location.hash === '#livetv') {
        this.stopCurrentStream();
      }
    });
  }

  async initialize() {
    try {
      // Hide main UI elements (search bar and sidebar)
      this.hideMainUI();

      // Initialize control buttons array
      this.controlButtons = [
        document.getElementById('livetv-play-pause'),
        document.getElementById('livetv-fullscreen-btn')
      ].filter(btn => btn !== null);

      await this.fetchChannels();
      this.updateChannelSelector();
      // Don't show channel selector by default - require activation
      if (this.filteredChannels.length > 0) {
        this.selectChannel(0); // Select first channel
      }

      // Ensure LiveTV section has focus but don't lock it yet
      this.focusLiveTV();

      // Show activation hint initially
      this.showActivationHint();
    } catch (error) {
      console.error('LiveTV: Failed to initialize:', error);
      this.showError('Failed to load channels');
    }
  }

  focusLiveTV() {
    // Set focus to LiveTV section
    const liveTVSection = document.getElementById('livetv');
    if (liveTVSection) {
      liveTVSection.focus();
      liveTVSection.scrollIntoView({ behavior: 'instant' });
    }

    // Also ensure we're not in any other navigation mode
    this.isInFavoritesList = false;
    this.isInControlsMode = false;

    // Hide sidebar
    this.hideSidebar();

    // Only lock focus if channel selector is activated
    if (this.isChannelSelectorActivated) {
      this.lockFocusToLiveTV();
    }
  }

  lockFocusToLiveTV() {
    // Only disable sidebar keyboard navigation when not explicitly shown
    document.body.classList.add('livetv-focus-locked');

    // Only make sidebar links non-focusable if sidebar is not explicitly shown
    if (!this.isSidebarExplicitlyShown) {
      const sidebar = document.querySelector('.sidebar');
      if (sidebar) {
        const sidebarLinks = sidebar.querySelectorAll('a, button, [tabindex]');
        sidebarLinks.forEach(link => {
          link.setAttribute('data-original-tabindex', link.tabIndex || '0');
          link.tabIndex = -1;
        });
      }
    }
  }

  unlockFocusFromLiveTV() {
    // Re-enable sidebar navigation when leaving LiveTV
    document.body.classList.remove('livetv-focus-locked');

    // Restore sidebar functionality
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
      sidebar.style.pointerEvents = '';
      // Restore sidebar links focusability
      const sidebarLinks = sidebar.querySelectorAll('a, button, [tabindex]');
      sidebarLinks.forEach(link => {
        const originalTabIndex = link.getAttribute('data-original-tabindex');
        if (originalTabIndex !== null) {
          link.tabIndex = parseInt(originalTabIndex);
          link.removeAttribute('data-original-tabindex');
        }
      });
    }
  }

  hideMainUI() {
    // Add class to body to hide main search bar and sidebar
    document.body.classList.add('livetv-active');

    // Also directly hide search bar
    const searchBar = document.querySelector('.search-bar');
    if (searchBar) {
      searchBar.style.display = 'none';
    }
  }

  showMainUI() {
    // Remove class from body to show main search bar and sidebar
    document.body.classList.remove('livetv-active');

    // Also directly show search bar
    const searchBar = document.querySelector('.search-bar');
    if (searchBar) {
      searchBar.style.display = '';
    }

    // Unlock focus from LiveTV
    this.unlockFocusFromLiveTV();
  }

  setupSidebarHover() {
    // Show sidebar on hover over trigger area
    this.sidebarTrigger?.addEventListener('mouseenter', () => {
      this.showSidebar();
    });

    // Hide sidebar when mouse leaves both trigger and sidebar
    let hideTimeout;
    const hideSidebar = () => {
      hideTimeout = setTimeout(() => {
        this.hideSidebar();
      }, 500);
    };

    const cancelHide = () => {
      clearTimeout(hideTimeout);
    };

    this.sidebarTrigger?.addEventListener('mouseleave', hideSidebar);

    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
      sidebar.addEventListener('mouseenter', cancelHide);
      sidebar.addEventListener('mouseleave', hideSidebar);
    }
  }

  showSidebar() {
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
      sidebar.classList.add('show');
    }
  }

  showSidebarExplicitly() {
    this.isSidebarExplicitlyShown = true;
    this.showSidebar();

    // Temporarily restore sidebar focusability
    this.restoreSidebarFocusability();

    // Notify remote control manager to take over sidebar navigation
    if (window.remoteManager) {
      window.remoteManager.currentFocusArea = 'sidebar';
      window.remoteManager.focusSidebar();

      // Set up listener for section changes
      this.setupSectionChangeListener();
    } else {
      // Fallback: Focus on the first sidebar link
      const sidebar = document.querySelector('.sidebar');
      const firstLink = sidebar?.querySelector('a, button');
      if (firstLink) {
        firstLink.focus();
      }
    }

    // Auto-hide after some time of inactivity
    this.setupSidebarAutoHide();
  }

  hideSidebar() {
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
      sidebar.classList.remove('show');
    }
    this.isSidebarExplicitlyShown = false;

    // Notify remote control manager that we're leaving sidebar
    if (window.remoteManager) {
      window.remoteManager.currentFocusArea = 'content';
    }

    // Re-apply focus lock if channel selector is activated
    if (this.isChannelSelectorActivated) {
      this.lockFocusToLiveTV();
    }
  }

  stopCurrentStream() {
    // Stop HLS stream if active
    if (this.currentHlsInstance) {
      console.log('LiveTV: Stopping HLS stream');
      this.currentHlsInstance.destroy();
      this.currentHlsInstance = null;
    }

    // Stop video element
    const videoElement = document.getElementById('livetv-player-video');
    if (videoElement) {
      videoElement.pause();
      videoElement.src = '';
      videoElement.load(); // Reset the video element
    }

    // Hide iframe if active
    const iframeElement = document.getElementById('livetv-player-iframe');
    if (iframeElement) {
      iframeElement.src = 'about:blank'; // Clear iframe content
      iframeElement.style.display = 'none';
    }

    // Show video element by default
    if (videoElement) {
      videoElement.style.display = 'block';
    }

    console.log('LiveTV: Stream stopped and cleaned up');
  }

  cleanup() {
    // Stop current stream
    this.stopCurrentStream();

    // Reset all states
    this.isChannelSelectorActivated = false;
    this.isSidebarExplicitlyShown = false;
    this.isInFavoritesList = false;
    this.isInControlsMode = false;

    // Hide all UI elements
    this.hideChannelSelector();
    this.hideFavoritesList();
    this.hideTVControls();
    this.hideActivationHint();

    // Clear timeouts
    clearTimeout(this.selectorTimeout);
    clearTimeout(this.sidebarAutoHideTimeout);

    // Restore main UI
    this.showMainUI();

    console.log('LiveTV: Cleanup completed');
  }

  restoreSidebarFocusability() {
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
      const sidebarLinks = sidebar.querySelectorAll('a, button, [tabindex]');
      sidebarLinks.forEach(link => {
        const originalTabIndex = link.getAttribute('data-original-tabindex');
        if (originalTabIndex !== null) {
          link.tabIndex = parseInt(originalTabIndex);
        }
      });
    }
  }

  setupSidebarAutoHide() {
    // Clear any existing timeout
    clearTimeout(this.sidebarAutoHideTimeout);

    // Hide sidebar after 10 seconds of no interaction
    this.sidebarAutoHideTimeout = setTimeout(() => {
      this.hideSidebar();
      // Return focus to LiveTV
      const liveTVSection = document.getElementById('livetv');
      if (liveTVSection) {
        liveTVSection.focus();
      }
    }, 10000);
  }

  setupSectionChangeListener() {
    // Listen for clicks on sidebar links to detect section changes
    const sidebarLinks = document.querySelectorAll('.sidebar a');
    sidebarLinks.forEach(link => {
      // Remove any existing listeners to avoid duplicates
      link.removeEventListener('click', this.handleSectionChange);
      // Add new listener
      link.addEventListener('click', this.handleSectionChange.bind(this));
    });
  }

  handleSectionChange(event) {
    const targetSection = event.target.dataset.section;
    console.log(`LiveTV: Section change detected to ${targetSection}`);

    // If navigating away from LiveTV, cleanup
    if (targetSection && targetSection !== 'livetv') {
      console.log('LiveTV: Cleaning up due to section change');
      this.cleanup();
    }
  }

  enterControlsMode() {
    this.isInControlsMode = true;
    this.currentControlIndex = 0;
    this.showTVControls();
    this.updateControlFocus();
  }

  exitControlsMode() {
    this.isInControlsMode = false;
    this.currentControlIndex = 0;
    this.clearControlFocus();
    this.hideTVControls();
  }

  showTVControls() {
    const controls = document.querySelector('.tv-controls');
    if (controls) {
      controls.classList.add('show');
    }
  }

  hideTVControls() {
    const controls = document.querySelector('.tv-controls');
    if (controls) {
      controls.classList.remove('show');
    }
  }

  previousControl() {
    if (this.controlButtons.length === 0) return;
    this.currentControlIndex = this.currentControlIndex <= 0 ?
      this.controlButtons.length - 1 : this.currentControlIndex - 1;
    this.updateControlFocus();
  }

  nextControl() {
    if (this.controlButtons.length === 0) return;
    this.currentControlIndex = this.currentControlIndex >= this.controlButtons.length - 1 ?
      0 : this.currentControlIndex + 1;
    this.updateControlFocus();
  }

  updateControlFocus() {
    // Clear all focus states
    this.controlButtons.forEach(btn => btn?.classList.remove('focused'));

    // Add focus to current button
    const currentBtn = this.controlButtons[this.currentControlIndex];
    if (currentBtn) {
      currentBtn.classList.add('focused');
    }
  }

  clearControlFocus() {
    this.controlButtons.forEach(btn => btn?.classList.remove('focused'));
  }

  activateCurrentControl() {
    const currentBtn = this.controlButtons[this.currentControlIndex];
    if (currentBtn) {
      currentBtn.click();
    }
  }

  activateChannelSelector() {
    this.isChannelSelectorActivated = true;
    this.showChannelSelector();
    this.autoHideSelector();
    this.lockFocusToLiveTV();
    this.hideActivationHint();
  }

  deactivateChannelSelector() {
    this.isChannelSelectorActivated = false;
    this.hideChannelSelector();
    this.hideTVControls();
    this.unlockFocusFromLiveTV();
    this.showActivationHint();
  }

  showActivationHint() {
    // Show a subtle hint about how to activate channel selector
    const hint = document.getElementById('livetv-activation-hint');
    if (hint) {
      hint.classList.add('show');
      // Auto-hide hint after 3 seconds
      setTimeout(() => {
        this.hideActivationHint();
      }, 3000);
    }
  }

  hideActivationHint() {
    const hint = document.getElementById('livetv-activation-hint');
    if (hint) {
      hint.classList.remove('show');
    }
  }

  toggleFullscreen() {
    const playerContainer = document.querySelector('.livetv-player-container');

    if (!playerContainer) {
      console.error('Player container not found');
      return;
    }

    if (!document.fullscreenElement && !document.webkitFullscreenElement && !document.msFullscreenElement) {
      // Enter fullscreen
      if (playerContainer.requestFullscreen) {
        playerContainer.requestFullscreen().catch(err => {
          console.error('Error entering fullscreen:', err);
        });
      } else if (playerContainer.webkitRequestFullscreen) {
        playerContainer.webkitRequestFullscreen();
      } else if (playerContainer.msRequestFullscreen) {
        playerContainer.msRequestFullscreen();
      } else {
        console.warn('Fullscreen API not supported');
        return;
      }
    } else {
      // Exit fullscreen
      if (document.exitFullscreen) {
        document.exitFullscreen().catch(err => {
          console.error('Error exiting fullscreen:', err);
        });
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
    }
  }

  updateFullscreenIcon() {
    const icon = this.fullscreenBtn?.querySelector('i');
    if (!icon) return;

    const isFullscreen = !!(document.fullscreenElement || document.webkitFullscreenElement || document.msFullscreenElement);

    if (isFullscreen) {
      icon.className = 'fas fa-compress';
    } else {
      icon.className = 'fas fa-expand';
    }
  }

  async fetchChannels() {
    const query = `
      query GetLiveTV($limit: Int) {
        liveTV(limit: $limit) {
          id title displayTitle thumbnail image
          streamingUrls {
            id url provider language sourceStreamUrl lastChecked method
          }
          metadata { genre origin duration }
        }
      }
    `;

    const response = await fetch('/graphql', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query, variables: { limit: 100 } })
    });

    const data = await response.json();
    if (data.errors) throw new Error(data.errors[0].message);

    this.channels = (data.data.liveTV || []).map((channel, index) => ({
      ...channel,
      number: index + 1,
      isOnline: this.isChannelOnline(channel),
      category: this.getChannelCategory(channel),
      isFavorite: this.favorites.includes(channel.id)
    }));

    this.filteredChannels = this.channels.filter(c => c.isOnline);
    console.log(`LiveTV: Fetched ${this.channels.length} channels, ${this.filteredChannels.length} online`);
  }

  isChannelOnline(channel) {
    // Check if channel has valid streaming URLs
    const hasStreamingUrls = channel.streamingUrls && channel.streamingUrls.length > 0;

    // Additional check: ensure at least one streaming URL has a valid URL
    if (hasStreamingUrls) {
      return channel.streamingUrls.some(streamUrl =>
        streamUrl && streamUrl.url && streamUrl.url.trim() !== ''
      );
    }

    return false;
  }

  getChannelCategory(channel) {
    // Extract category from channel data
    if (channel.metadata && channel.metadata.genre) {
      const genres = Array.isArray(channel.metadata.genre)
        ? channel.metadata.genre
        : channel.metadata.genre.split(',').map(g => g.trim());

      return genres[0] || 'Uncategorized';
    }

    // Try to determine category from title
    const title = channel.title.toLowerCase();
    if (title.includes('news')) return 'News';
    if (title.includes('sport')) return 'Sports';
    if (title.includes('movie')) return 'Movies';
    if (title.includes('music')) return 'Music';
    if (title.includes('kids') || title.includes('children')) return 'Kids';

    return 'Uncategorized';
  }

  updateChannelSelector() {
    if (!this.channelCards || this.filteredChannels.length === 0) return;

    const positions = ['prev-2', 'prev-1', 'current', 'next-1', 'next-2'];
    const offsets = [-2, -1, 0, 1, 2];

    positions.forEach((position, i) => {
      const card = this.channelCards[position];
      if (!card) return;

      const channelIndex = this.currentChannelIndex + offsets[i];
      const channel = this.getChannelAtIndex(channelIndex);

      if (channel) {
        const numberEl = card.querySelector('.channel-number');
        const nameEl = card.querySelector('.channel-name');
        const categoryEl = card.querySelector('.channel-category');
        const favoriteIcon = card.querySelector('.favorite-icon');

        if (numberEl) numberEl.textContent = channel.number;
        if (nameEl) nameEl.textContent = channel.displayTitle || channel.title;
        if (categoryEl && position === 'current') {
          categoryEl.textContent = channel.category;
        }

        // Update favorite icon
        if (favoriteIcon) {
          const icon = favoriteIcon.querySelector('i');
          if (icon) {
            if (channel.isFavorite) {
              icon.className = 'fas fa-star';
              favoriteIcon.classList.add('active');
            } else {
              icon.className = 'far fa-star';
              favoriteIcon.classList.remove('active');
            }
          }
          // Store channel ID for favorite toggle
          favoriteIcon.dataset.channelId = channel.id;
        }

        card.style.opacity = '1';
      } else {
        card.style.opacity = '0.3';
        const numberEl = card.querySelector('.channel-number');
        const nameEl = card.querySelector('.channel-name');
        const favoriteIcon = card.querySelector('.favorite-icon');

        if (numberEl) numberEl.textContent = '--';
        if (nameEl) nameEl.textContent = 'No Channel';
        if (favoriteIcon) {
          favoriteIcon.dataset.channelId = '';
          const icon = favoriteIcon.querySelector('i');
          if (icon) {
            icon.className = 'far fa-star';
            favoriteIcon.classList.remove('active');
          }
        }
      }
    });
  }

  getChannelAtIndex(index) {
    if (index < 0) {
      return this.filteredChannels[this.filteredChannels.length + (index % this.filteredChannels.length)];
    }
    return this.filteredChannels[index % this.filteredChannels.length];
  }

  showChannelSelector() {
    this.channelSelector?.classList.add('show');
    this.showTVControls(); // Show TV controls with selector
    // Clear any existing timeout when manually showing
    clearTimeout(this.selectorTimeout);
  }

  hideChannelSelector() {
    this.channelSelector?.classList.remove('show');
    // Clear timeout when manually hiding
    clearTimeout(this.selectorTimeout);
  }

  showFavoritesList() {
    if (this.favorites.length === 0) {
      console.log('No favorites to show');
      return;
    }

    this.isInFavoritesList = true;
    this.currentFavoriteIndex = 0;
    this.updateFavoritesList();
    this.favoritesList?.classList.add('show');
    // Hide main selector when showing favorites
    this.channelSelector?.classList.remove('show');
  }

  hideFavoritesList() {
    this.isInFavoritesList = false;
    this.favoritesList?.classList.remove('show');
    // Show main selector when hiding favorites
    this.showChannelSelector();
    this.autoHideSelector();
  }

  updateFavoritesList() {
    if (!this.favoritesCards) return;

    const favoriteChannels = this.channels.filter(channel =>
      this.favorites.includes(channel.id)
    );

    const positions = ['prev-2', 'prev-1', 'current', 'next-1', 'next-2'];
    const offsets = [-2, -1, 0, 1, 2];

    if (favoriteChannels.length === 0) {
      // Show "No favorites" message in center card
      const currentCard = this.favoritesCards.current;
      if (currentCard) {
        const numberEl = currentCard.querySelector('.channel-number');
        const nameEl = currentCard.querySelector('.channel-name');
        const categoryEl = currentCard.querySelector('.channel-category');
        const favoriteIcon = currentCard.querySelector('.favorite-icon');

        if (numberEl) numberEl.textContent = '--';
        if (nameEl) nameEl.textContent = 'No Favorites';
        if (categoryEl) categoryEl.textContent = 'Add some favorites first';
        if (favoriteIcon) {
          favoriteIcon.dataset.channelId = '';
          const icon = favoriteIcon.querySelector('i');
          if (icon) {
            icon.className = 'far fa-star';
            favoriteIcon.classList.remove('active');
          }
        }
      }

      // Hide other cards
      positions.forEach(position => {
        if (position !== 'current') {
          const card = this.favoritesCards[position];
          if (card) card.style.opacity = '0.3';
        }
      });
      return;
    }

    positions.forEach((position, i) => {
      const card = this.favoritesCards[position];
      if (!card) return;

      const channelIndex = this.currentFavoriteIndex + offsets[i];
      const channel = this.getFavoriteChannelAtIndex(channelIndex, favoriteChannels);

      if (channel) {
        const numberEl = card.querySelector('.channel-number');
        const nameEl = card.querySelector('.channel-name');
        const categoryEl = card.querySelector('.channel-category');
        const favoriteIcon = card.querySelector('.favorite-icon');

        if (numberEl) numberEl.textContent = channel.number;
        if (nameEl) nameEl.textContent = channel.displayTitle || channel.title;
        if (categoryEl && position === 'current') {
          categoryEl.textContent = channel.category;
        }

        // Update favorite icon (always active since these are favorites)
        if (favoriteIcon) {
          const icon = favoriteIcon.querySelector('i');
          if (icon) {
            icon.className = 'fas fa-star';
            favoriteIcon.classList.add('active');
          }
          favoriteIcon.dataset.channelId = channel.id;
        }

        card.style.opacity = '1';
      } else {
        card.style.opacity = '0.3';
        const numberEl = card.querySelector('.channel-number');
        const nameEl = card.querySelector('.channel-name');
        const favoriteIcon = card.querySelector('.favorite-icon');

        if (numberEl) numberEl.textContent = '--';
        if (nameEl) nameEl.textContent = 'Channel';
        if (favoriteIcon) {
          favoriteIcon.dataset.channelId = '';
          const icon = favoriteIcon.querySelector('i');
          if (icon) {
            icon.className = 'far fa-star';
            favoriteIcon.classList.remove('active');
          }
        }
      }
    });
  }

  getFavoriteChannelAtIndex(index, favoriteChannels) {
    if (favoriteChannels.length === 0) return null;

    if (index < 0) {
      return favoriteChannels[favoriteChannels.length + (index % favoriteChannels.length)];
    }
    return favoriteChannels[index % favoriteChannels.length];
  }

  previousFavorite() {
    const favoriteChannels = this.channels.filter(channel =>
      this.favorites.includes(channel.id)
    );

    if (favoriteChannels.length === 0) return;

    // Hide sidebar when navigating favorites
    this.hideSidebar();

    this.currentFavoriteIndex = this.currentFavoriteIndex <= 0 ?
      favoriteChannels.length - 1 : this.currentFavoriteIndex - 1;

    // Update the display
    this.updateFavoritesList();

    // Actually play the selected favorite channel
    const selectedChannel = favoriteChannels[this.currentFavoriteIndex];
    if (selectedChannel) {
      this.selectChannelById(selectedChannel.id);
    }
  }

  nextFavorite() {
    const favoriteChannels = this.channels.filter(channel =>
      this.favorites.includes(channel.id)
    );

    if (favoriteChannels.length === 0) return;

    // Hide sidebar when navigating favorites
    this.hideSidebar();

    this.currentFavoriteIndex = this.currentFavoriteIndex >= favoriteChannels.length - 1 ?
      0 : this.currentFavoriteIndex + 1;

    // Update the display
    this.updateFavoritesList();

    // Actually play the selected favorite channel
    const selectedChannel = favoriteChannels[this.currentFavoriteIndex];
    if (selectedChannel) {
      this.selectChannelById(selectedChannel.id);
    }
  }

  selectFavoriteChannel() {
    const favoriteChannels = this.channels.filter(channel =>
      this.favorites.includes(channel.id)
    );

    if (favoriteChannels.length === 0 || this.currentFavoriteIndex < 0) return;

    const selectedChannel = favoriteChannels[this.currentFavoriteIndex];
    if (selectedChannel) {
      this.selectChannelById(selectedChannel.id);
      this.hideFavoritesList();
    }
  }

  selectChannelById(channelId) {
    const channelIndex = this.filteredChannels.findIndex(channel => channel.id === channelId);
    if (channelIndex >= 0) {
      this.selectChannel(channelIndex);
    } else {
      // If channel not found in filtered channels, find it in all channels
      const allChannelIndex = this.channels.findIndex(channel => channel.id === channelId);
      if (allChannelIndex >= 0) {
        const channel = this.channels[allChannelIndex];
        // Temporarily add to filtered channels and select
        this.currentChannelIndex = this.filteredChannels.length;
        this.filteredChannels.push(channel);
        this.updateChannelSelector();
        this.playChannel(channel);
      }
    }
  }

  showSearch() {
    this.searchOverlay.style.display = 'flex';
    this.searchInput?.focus();
    this.performSearch();
  }

  hideSearch() {
    this.searchOverlay.style.display = 'none';
  }

  togglePlayPause() {
    const player = document.getElementById('player');
    const livetv_video = this.playerVideo;

    // Check if we're using the main player or livetv player
    const activePlayer = (player && player.style.display !== 'none') ? player : livetv_video;

    if (activePlayer) {
      if (activePlayer.paused) {
        activePlayer.play().catch(console.error);
        this.updatePlayPauseIcon(false);
      } else {
        activePlayer.pause();
        this.updatePlayPauseIcon(true);
      }
    }
  }

  updatePlayPauseIcon(isPaused) {
    if (this.playPauseBtn) {
      const icon = this.playPauseBtn.querySelector('i');
      if (icon) {
        icon.className = isPaused ? 'fas fa-play' : 'fas fa-pause';
      }
    }
  }

  performSearch() {
    const searchTerm = this.searchInput?.value.toLowerCase() || '';

    const results = this.channels.filter(channel => {
      const matchesSearch = !searchTerm ||
        channel.title.toLowerCase().includes(searchTerm) ||
        channel.displayTitle?.toLowerCase().includes(searchTerm);

      return channel.isOnline && matchesSearch;
    });

    this.renderSearchResults(results);
  }

  renderSearchResults(results) {
    if (!this.searchResults) return;

    this.searchResults.innerHTML = results.map(channel => `
      <div class="search-result-item" data-id="${channel.id}">
        <div class="channel-number">${channel.number}</div>
        <div class="channel-name">${channel.displayTitle || channel.title}</div>
        <div class="channel-category">${channel.category}</div>
      </div>
    `).join('');

    // Add click listeners
    this.searchResults.querySelectorAll('.search-result-item').forEach(item => {
      item.addEventListener('click', () => {
        const channelId = item.dataset.id;
        const channelIndex = this.filteredChannels.findIndex(c => c.id === channelId);
        if (channelIndex !== -1) {
          this.selectChannel(channelIndex);
          this.hideSearch();
        }
      });
    });
  }

  setupFavoriteIconListeners() {
    // Add click listeners to all favorite icons
    document.addEventListener('click', (e) => {
      if (e.target.closest('.favorite-icon') && window.location.hash === '#livetv') {
        e.preventDefault();
        e.stopPropagation();

        const favoriteIcon = e.target.closest('.favorite-icon');
        const channelId = favoriteIcon.dataset.channelId;

        if (channelId) {
          this.toggleChannelFavorite(channelId);
        }
      }
    });
  }

  toggleChannelFavorite(channelId) {
    // Find the channel and toggle its favorite status
    const channel = this.channels.find(c => c.id === channelId);
    if (!channel) return;

    channel.isFavorite = !channel.isFavorite;

    // Update favorites array
    if (channel.isFavorite) {
      if (!this.favorites.includes(channelId)) {
        this.favorites.push(channelId);
      }
    } else {
      const index = this.favorites.indexOf(channelId);
      if (index > -1) {
        this.favorites.splice(index, 1);
      }
    }

    // Save to localStorage
    this.saveFavorites();

    // Update the channel selector display
    this.updateChannelSelector();

    // If we're in favorites-only mode and this channel is no longer a favorite,
    // we need to refilter
    if (this.showFavoritesOnly) {
      this.filterChannels();
    }
  }



  filterChannels() {
    this.filteredChannels = this.channels.filter(channel => {
      const matchesFavorites = !this.showFavoritesOnly || channel.isFavorite;
      return channel.isOnline && matchesFavorites;
    });

    // Reset to first channel if current is filtered out
    if (this.currentChannelIndex >= this.filteredChannels.length) {
      this.currentChannelIndex = 0;
    }

    this.updateChannelSelector();
  }

  selectChannel(index) {
    if (index < 0 || index >= this.filteredChannels.length) return;

    this.currentChannelIndex = index;
    const channel = this.filteredChannels[index];

    this.updateChannelSelector();
    this.playChannel(channel);
  }

  previousChannel() {
    if (this.filteredChannels.length === 0) return;

    // Ensure we stay focused on LiveTV
    this.focusLiveTV();

    // Show selector when navigating
    this.showChannelSelector();
    this.autoHideSelector();

    const newIndex = this.currentChannelIndex <= 0 ?
      this.filteredChannels.length - 1 : this.currentChannelIndex - 1;
    this.selectChannel(newIndex);
  }

  nextChannel() {
    if (this.filteredChannels.length === 0) return;

    // Ensure we stay focused on LiveTV
    this.focusLiveTV();

    // Show selector when navigating
    this.showChannelSelector();
    this.autoHideSelector();

    const newIndex = this.currentChannelIndex >= this.filteredChannels.length - 1 ?
      0 : this.currentChannelIndex + 1;
    this.selectChannel(newIndex);
  }

  async playChannel(channel) {
    if (!channel.isOnline) return;

    // Find the best streaming URL
    const streamingUrl = this.getBestStreamingUrl(channel);
    if (!streamingUrl) {
      console.error('No valid streaming source found for this channel.');
      return;
    }

    // Set currently playing channel
    this.currentlyPlaying = channel;

    // Get the WITV_BASE from the config
    const witvBase = window.config?.witvBase || 'witv.skin';
    const isWitvSkin = (streamingUrl.url && (streamingUrl.url.includes(witvBase) || streamingUrl.url.includes('play.witv'))) ||
                      (streamingUrl.sourceStreamUrl && (streamingUrl.sourceStreamUrl.includes(witvBase) || streamingUrl.sourceStreamUrl.includes('play.witv')));

    // Determine if we should use direct stream or iframe
    let isSourceStream = isWitvSkin ? false : !!streamingUrl.sourceStreamUrl;
    let url = streamingUrl.sourceStreamUrl || streamingUrl.url;
    let method = isWitvSkin ? 'GET' : (streamingUrl.method || 'GET');

    // For witv.skin URLs, use the proxy-video endpoint with proper M3U8 handling
    if (isWitvSkin) {
      // Extract channel ID from the URL
      const channelIdMatch = streamingUrl.url.match(/\/(\d+)\.m3u8/);
      if (channelIdMatch) {
        const channelId = channelIdMatch[1];

        // Use the proxy-video endpoint to get the proper M3U8 playlist
        url = `/proxy-video?url=${encodeURIComponent(streamingUrl.url)}`;
        isSourceStream = true; // This is an HLS stream
        method = 'GET';

        console.log('Using proxy-video endpoint for witv.skin channel:', channelId, 'URL:', url);
      } else {
        console.error('Could not extract channel ID from witv.skin URL:', streamingUrl.url);
        // Fallback to original URL
        url = streamingUrl.url;
      }
    } else {
      // Check if we have custom headers for non-witv URLs
      if (streamingUrl.headers) {
        // Add headers to the URL as a query parameter for the proxy
        const headersParam = encodeURIComponent(JSON.stringify(streamingUrl.headers));

        // Check if the URL already has query parameters
        if (url.includes('?')) {
          url = `${url}&headers=${headersParam}`;
        } else {
          url = `${url}?headers=${headersParam}`;
        }
      }

      // For non-witv URLs, use the proxy if needed
      if (streamingUrl.sourceStreamUrl && !url.startsWith('/proxy-video')) {
        // Use the current origin (same port as the main application)
        url = `${window.location.origin}/proxy-video?url=${encodeURIComponent(url)}`;
      }
    }

    // Store the URL for potential retries
    this.currentStreamUrl = url;
    this.currentStreamingUrl = streamingUrl;
    this.isWitvSkin = isWitvSkin;
    this.isSourceStream = isSourceStream;

    // Set the title in the modern player if available
    if (window.modernPlayer) {
      window.modernPlayer.setTitle(channel.displayTitle || channel.title);
    }

    // Always use the LiveTV player container instead of popup player
    console.log('Using LiveTV player container for playback');
    this.liveTVPlayback(url, isSourceStream);
  }

  getBestStreamingUrl(channel) {
    if (!channel.streamingUrls || channel.streamingUrls.length === 0) {
      return null;
    }

    // First, check for witv.skin URLs in all URLs
    const witvUrls = channel.streamingUrls.filter(url =>
      (url.sourceStreamUrl && (url.sourceStreamUrl.includes('witv.skin') || url.sourceStreamUrl.includes('play.witv'))) ||
      (url.url && (url.url.includes('witv.skin') || url.url.includes('play.witv')))
    );

    // If we have witv.skin URLs, prioritize them as they're likely to be more reliable for LiveTV
    if (witvUrls.length > 0) {
      // Make sure the witv.skin URL is marked for direct streaming
      const witvUrl = witvUrls[0];

      // Force sourceStreamUrl to be set for witv.skin URLs to ensure direct streaming
      if (!witvUrl.sourceStreamUrl && witvUrl.url) {
        witvUrl.sourceStreamUrl = witvUrl.url;
      }

      return witvUrl;
    }

    // If no witv.skin URLs, prioritize streaming URLs with sourceStreamUrl
    const withSourceStream = channel.streamingUrls.filter(url => url.sourceStreamUrl);

    if (withSourceStream.length > 0) {
      return withSourceStream[0];
    }

    return channel.streamingUrls[0];
  }

  liveTVPlayback(url, isSourceStream) {
    // Use the LiveTV player elements directly
    const playerVideo = this.playerVideo;
    const playerIframe = this.playerIframe;

    if (!playerVideo && !playerIframe) {
      console.error('LiveTV player elements not available');
      return;
    }

    console.log('Playing in LiveTV container:', { url, isSourceStream });

    if (isSourceStream && playerVideo) {
      console.log('Playing with LiveTV video element');
      playerVideo.style.display = 'block';

      // Check if this is an HLS stream
      if (url.includes('.m3u8') && typeof Hls !== 'undefined' && Hls.isSupported()) {
        console.log('Using HLS.js for m3u8 stream');

        // Destroy any existing HLS instance
        if (this.currentHlsInstance) {
          try {
            this.currentHlsInstance.destroy();
          } catch (e) {
            console.error('Error destroying HLS instance:', e);
          }
        }

        // Create a new HLS instance with optimized settings
        this.currentHlsInstance = new Hls({
          debug: false,
          fragLoadingTimeOut: 60000,
          manifestLoadingTimeOut: 30000,
          fragLoadingMaxRetry: 6,
          manifestLoadingMaxRetry: 4,
          fragLoadingRetryDelay: 500,
          manifestLoadingRetryDelay: 500,
          lowLatencyMode: true,
          maxBufferLength: 30,
          maxMaxBufferLength: 60
        });

        // Load the source
        this.currentHlsInstance.loadSource(url);

        // Attach to the LiveTV player
        this.currentHlsInstance.attachMedia(playerVideo);

        // Play when manifest is parsed
        this.currentHlsInstance.on(Hls.Events.MANIFEST_PARSED, () => {
          console.log('HLS manifest parsed, starting playback');
          playerVideo.play().catch(err => {
            console.error('Error playing video after HLS initialization:', err);

            // If this is a witv.skin URL, try to refresh the token
            if (this.isWitvSkin) {
              this.handlePlayerError({ target: playerVideo });
            }
          });
        });

        // Handle errors
        this.currentHlsInstance.on(Hls.Events.ERROR, (event, data) => {
          console.error('HLS error:', data);

          if (data.fatal) {
            switch(data.type) {
              case Hls.ErrorTypes.NETWORK_ERROR:
                console.log('Fatal network error, trying to recover');
                this.currentHlsInstance.startLoad();
                break;
              case Hls.ErrorTypes.MEDIA_ERROR:
                console.log('Fatal media error, trying to recover');
                this.currentHlsInstance.recoverMediaError();
                break;
              default:
                console.error('Fatal error, cannot recover');
                // If this is a witv.skin URL, try to refresh the token
                if (this.isWitvSkin) {
                  this.handlePlayerError({ target: playerVideo });
                } else {
                  // Fall back to direct playback
                  playerVideo.src = url;
                  playerVideo.play().catch(err => {
                    console.error('Error playing video after HLS error:', err);
                  });
                }
                break;
            }
          }
        });

        // Add error event listener for token expiration
        if (this.isWitvSkin) {
          playerVideo.addEventListener('error', this.handlePlayerError.bind(this));
        }
      } else {
        // Direct playback for non-HLS streams
        playerVideo.src = url;

        // Add error event listener for token expiration
        if (this.isWitvSkin) {
          playerVideo.addEventListener('error', this.handlePlayerError.bind(this));
        }

        playerVideo.play().catch(err => {
          console.error('Error playing video:', err);

          // If this is a witv.skin URL, try to refresh the token
          if (this.isWitvSkin) {
            this.handlePlayerError({ target: playerVideo });
          }
        });
      }

      if (playerIframe) {
        playerIframe.style.display = 'none';
        playerIframe.src = 'about:blank';
      }
    } else if (playerIframe) {
      console.log('Playing with LiveTV iframe element');
      playerIframe.style.display = 'block';
      playerIframe.src = url;

      // Add error event listener for iframe
      if (this.isWitvSkin) {
        playerIframe.addEventListener('error', this.handlePlayerError.bind(this));
      }

      if (playerVideo) {
        playerVideo.style.display = 'none';
        playerVideo.pause();
        playerVideo.src = '';
      }
    }

    // Update play/pause button
    this.updatePlayPauseIcon(false);
  }

  // Handle player errors, especially for token expiration
  async handlePlayerError(event) {
    // Only handle errors for witv.skin URLs
    if (!this.isWitvSkin || !this.currentStreamingUrl) {
      return;
    }

    // Check if we're in a cooldown period to prevent infinite loops
    const now = Date.now();
    if (this.lastTokenRefresh && (now - this.lastTokenRefresh) < 5000) {
      console.log('Token refresh on cooldown, skipping');
      return;
    }

    // Set the last token refresh time
    this.lastTokenRefresh = now;

    console.log('Refreshing token for witv.skin URL');

    try {
      // Remove the error event listener to avoid duplicate handling
      const player = document.getElementById('player');
      if (player) {
        player.removeEventListener('error', this.handlePlayerError.bind(this));
      }

      // Extract the channel ID from the URL
      let channelId = null;
      const channelIdMatch = this.currentStreamingUrl.url.match(/\/(\d+)\.m3u8/);
      if (channelIdMatch) {
        channelId = channelIdMatch[1];
      }

      if (!channelId) {
        throw new Error('Could not extract channel ID from URL');
      }

      // Use the direct token endpoint
      const baseUrl = window.location.origin;
      const directAuthUrl = `${baseUrl}/direct-token/${channelId}`;
      console.log(`Using direct token endpoint: ${directAuthUrl}`);

      try {
        // Add a timeout to the fetch request
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const directResponse = await fetch(directAuthUrl, {
          signal: controller.signal
        }).finally(() => clearTimeout(timeoutId));

        if (directResponse.ok) {
          const tokenUrl = await directResponse.text();
          console.log(`Got token URL: ${tokenUrl}`);

          if (tokenUrl && tokenUrl.includes('token=')) {
            // Update the stream URL with the token
            this.currentStreamingUrl.url = tokenUrl;
            if (this.currentStreamingUrl.sourceStreamUrl) {
              this.currentStreamingUrl.sourceStreamUrl = tokenUrl;
            }

            // Get the updated URL
            let url = this.currentStreamingUrl.sourceStreamUrl || this.currentStreamingUrl.url;

            // Always use the proxy for witv.skin URLs
            if (!url.startsWith('/proxy-video')) {
              url = `${window.location.origin}/proxy-video?url=${encodeURIComponent(url)}`;
            }

            // Update the current stream URL
            this.currentStreamUrl = url;

            // Retry playback
            this.retryPlayback();

            return;
          }
        }
      } catch (directError) {
        console.error(`Error with direct token endpoint: ${directError.message}`);
      }

      // If all approaches failed, throw an error
      throw new Error('Token refresh failed');
    } catch (error) {
      console.error(`Error refreshing token: ${error.message}`);
    }
  }

  // Method to retry playback with the current stream URL
  retryPlayback() {
    if (!this.currentlyPlaying || !this.currentStreamUrl) {
      console.error('Cannot retry playback: no current stream URL or channel');
      return;
    }

    // Use the playItem function if available
    if (typeof playItem === 'function') {
      // Create a temporary object that mimics the mediaData structure expected by playItem
      const tempMediaData = {
        id: this.currentlyPlaying.id,
        displayTitle: this.currentlyPlaying.displayTitle,
        title: this.currentlyPlaying.title,
        thumbnail: this.currentlyPlaying.thumbnail,
        image: this.currentlyPlaying.image,
        type: 'LIVETV'
      };

      // Store the temporary media data in the window object so playItem can access it
      window._tempMediaData = tempMediaData;

      // Create a wrapper function that sets the mediaData before calling playItem
      const originalPlayItem = playItem;
      const wrappedPlayItem = (url, isSourceStream, method) => {
        // Store the original mediaData
        const originalMediaData = window.mediaData;

        // Set the temporary mediaData
        window.mediaData = window._tempMediaData;

        // Call the original playItem function
        originalPlayItem(url, isSourceStream, method);

        // Restore the original mediaData
        window.mediaData = originalMediaData;

        // Clean up
        delete window._tempMediaData;

        // Add error event listener to the player for token expiration
        const player = document.getElementById('player');
        if (this.isWitvSkin && player) {
          player.addEventListener('error', this.handlePlayerError.bind(this));
        }
      };

      // Call the wrapped playItem function
      wrappedPlayItem(this.currentStreamUrl, this.isSourceStream, 'GET');
    } else {
      // Fallback: handle playback directly
      this.fallbackPlayback(this.currentStreamUrl, this.isSourceStream);
    }
  }

  loadFavorites() {
    try {
      return JSON.parse(localStorage.getItem('livetv-favorites') || '[]');
    } catch {
      return [];
    }
  }

  saveFavorites() {
    try {
      localStorage.setItem('livetv-favorites', JSON.stringify(this.favorites));
    } catch (error) {
      console.error('Failed to save favorites:', error);
    }
  }

  showError(message) {
    console.error('LiveTV Error:', message);
    // Show error in current channel card
    const currentCard = this.channelCards?.current;
    if (currentCard) {
      const nameEl = currentCard.querySelector('.channel-name');
      if (nameEl) nameEl.textContent = message;
    }
  }
}
