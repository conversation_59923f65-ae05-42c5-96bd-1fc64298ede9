// Simple Netflix-Style Media UI

document.addEventListener('DOMContentLoaded', () => {
  console.log('Netflix-style Media UI: Initializing...');

  // Setup hero buttons
  setupHeroButtons();

  // Setup season dropdown
  setupSeasonDropdown();

  console.log('Netflix-style Media UI: Ready');
});

function setupHeroButtons() {
  const playBtn = document.getElementById('hero-play-btn');
  const infoBtn = document.getElementById('hero-info-btn');

  if (playBtn) {
    playBtn.addEventListener('click', () => {
      // Find first episode or provider
      const firstEpisode = document.querySelector('#episodes .grid-item');
      if (firstEpisode) {
        firstEpisode.click();
      } else {
        const firstProvider = document.querySelector('#providers .grid-item .source-link');
        if (firstProvider) {
          firstProvider.click();
        }
      }
    });
  }

  if (infoBtn) {
    infoBtn.addEventListener('click', () => {
      document.querySelector('.details-section').scrollIntoView({
        behavior: 'smooth'
      });
    });
  }
}

function setupSeasonDropdown() {
  const seasonSelect = document.getElementById('season-select');
  const languageSelect = document.getElementById('language-select');

  if (seasonSelect) {
    seasonSelect.addEventListener('change', () => {
      // This will be handled by the existing media.js logic
      const event = new Event('change', { bubbles: true });
      seasonSelect.dispatchEvent(event);
    });
  }

  if (languageSelect) {
    languageSelect.addEventListener('change', (e) => {
      filterProvidersByLanguage(e.target.value);
    });
  }
}

function filterProvidersByLanguage(selectedLanguage) {
  const providers = document.querySelectorAll('#providers .grid-item');

  providers.forEach(provider => {
    const providerName = provider.querySelector('.provider-name');
    if (!providerName) return;

    const providerText = providerName.textContent;
    const hasLanguage = providerText.includes('(') && providerText.includes(')');

    if (selectedLanguage === 'all') {
      provider.style.display = 'block';
    } else if (hasLanguage) {
      const languageMatch = providerText.match(/\(([^)]+)\)/);
      const providerLanguage = languageMatch ? languageMatch[1] : '';

      if (providerLanguage === selectedLanguage) {
        provider.style.display = 'block';
      } else {
        provider.style.display = 'none';
      }
    } else {
      provider.style.display = 'none';
    }
  });
}

// Function to update hero content when media data loads
function updateHeroContent(mediaData) {
  console.log('Updating hero content with:', mediaData);

  // Update synopsis
  const heroSynopsis = document.getElementById('hero-synopsis');
  if (heroSynopsis && mediaData) {
    const synopsis = mediaData.metadata?.synopsis ||
                    mediaData.tmdb?.overview ||
                    mediaData.jikan?.synopsis ||
                    'No synopsis available.';
    heroSynopsis.textContent = synopsis;
  }

  // Update rating
  const heroRating = document.getElementById('hero-rating');
  if (heroRating && mediaData) {
    const ratingSpan = heroRating.querySelector('span');
    let rating = 'N/A';

    if (mediaData.tmdb?.vote_average) {
      rating = mediaData.tmdb.vote_average.toFixed(1);
    } else if (mediaData.jikan?.score) {
      rating = mediaData.jikan.score.toFixed(1);
    }

    if (ratingSpan) {
      ratingSpan.textContent = rating;
    }
  }

  // Show/hide season dropdown
  const seasonDropdown = document.getElementById('season-dropdown');
  if (seasonDropdown && mediaData) {
    const hasEpisodes = mediaData.episodes && mediaData.episodes.length > 0;
    const isSeriesOrAnime = mediaData.__typename === 'Series' || mediaData.__typename === 'Anime';

    if (hasEpisodes && isSeriesOrAnime) {
      // Group episodes by season
      const seasons = {};
      mediaData.episodes.forEach(episode => {
        const seasonNum = episode.season || '1';
        if (!seasons[seasonNum]) {
          seasons[seasonNum] = [];
        }
        seasons[seasonNum].push(episode);
      });

      const seasonNumbers = Object.keys(seasons).sort((a, b) => parseInt(a) - parseInt(b));

      if (seasonNumbers.length > 1) {
        seasonDropdown.style.display = 'block';
        const seasonSelect = document.getElementById('season-select');
        if (seasonSelect) {
          seasonSelect.innerHTML = seasonNumbers.map(seasonNum =>
            `<option value="${seasonNum}">Season ${seasonNum}</option>`
          ).join('');
        }
      } else {
        seasonDropdown.style.display = 'none';
      }
    } else {
      seasonDropdown.style.display = 'none';
    }
  }
}

// Make it available globally
window.updateHeroContent = updateHeroContent;
