/**
 * OpenSubtitles API Service
 * Handles communication with the OpenSubtitles API
 * API Documentation: https://opensubtitles.stoplight.io/docs/opensubtitles-api
 */

class OpenSubtitlesService {
  constructor() {
    this.apiKey = 'jsKzaEKhnj5sQ5q6415QVsyjXrEgQGSF';
    this.apiBaseUrl = 'https://api.opensubtitles.com/api/v1';
    this.username = 'netstream';
    this.token = null;
    this.isLoggedIn = false;

    // Rate limiting parameters
    this.isRateLimited = false;
    this.rateLimitResetTime = null;
    this.lastRequestTime = 0;
    this.minRequestInterval = 1000; // Minimum 1 second between requests

    // Cache for search results
    this.searchCache = {};
    this.cacheExpiry = 30 * 60 * 1000; // 30 minutes

    // Try to load rate limit status from localStorage
    const rateLimitData = localStorage.getItem('opensubtitles_rate_limit');
    if (rateLimitData) {
      try {
        const data = JSON.parse(rateLimitData);
        if (data.resetTime && new Date(data.resetTime) > new Date()) {
          this.isRateLimited = true;
          this.rateLimitResetTime = new Date(data.resetTime);
          console.log('OpenSubtitles: Rate limited until', this.rateLimitResetTime);
        } else {
          // Reset rate limit if it's expired
          localStorage.removeItem('opensubtitles_rate_limit');
        }
      } catch (e) {
        console.error('OpenSubtitles: Error parsing rate limit data', e);
        localStorage.removeItem('opensubtitles_rate_limit');
      }
    }
  }

  /**
   * Initialize the service
   * @returns {Promise<boolean>} Success status
   */
  async initialize() {
    try {
      // First, check if there's a rate limit in localStorage
      const rateLimitData = localStorage.getItem('opensubtitles_rate_limit');
      if (rateLimitData) {
        try {
          const data = JSON.parse(rateLimitData);

          // Check if the rate limit data has a setAt timestamp
          if (data.setAt) {
            const setAt = new Date(data.setAt);
            const now = new Date();

            // If the rate limit was set more than 1 hour ago, it's expired
            if (now.getTime() - setAt.getTime() > 3600000) {
              console.log('OpenSubtitles: Rate limit expired based on setAt timestamp');
              this.clearRateLimit();
            }
          }

          // Check if the resetTime is in the future but more than 1 day away
          if (data.resetTime) {
            const resetTime = new Date(data.resetTime);
            const now = new Date();

            // If the reset time is more than 24 hours in the future, it's likely incorrect
            if (resetTime > now && resetTime.getTime() - now.getTime() > 24 * 3600000) {
              console.log(`OpenSubtitles: Detected abnormal rate limit reset time (${resetTime}), resetting to 1 hour`);
              this.clearRateLimit();
              this.setRateLimit(3600000); // 1 hour
              return false;
            }
          }
        } catch (e) {
          console.error('OpenSubtitles: Error parsing rate limit data', e);
          localStorage.removeItem('opensubtitles_rate_limit');
        }
      }

      // Check if we're rate limited
      if (this.isRateLimited) {
        const now = new Date();
        if (this.rateLimitResetTime && this.rateLimitResetTime > now) {
          const minutesRemaining = Math.ceil((this.rateLimitResetTime - now) / 60000);

          // If the rate limit is more than 1 hour, it's likely incorrect - reset it
          if (minutesRemaining > 60) {
            console.log(`OpenSubtitles: Detected abnormal rate limit (${minutesRemaining} minutes), resetting to 60 minutes`);
            this.clearRateLimit();
            this.setRateLimit(3600000); // 1 hour
            return false;
          }

          console.log(`OpenSubtitles: Rate limited for ${minutesRemaining} more minutes`);
          return false;
        } else {
          // Reset rate limit if it's expired
          this.clearRateLimit();
        }
      }

      // Check if we have a token in localStorage
      const savedToken = localStorage.getItem('opensubtitles_token');
      if (savedToken) {
        this.token = savedToken;
        this.isLoggedIn = true;
        console.log('OpenSubtitles: Using saved token');
        return true;
      }

      // If no token, try to login as guest
      return await this.loginAsGuest();
    } catch (error) {
      console.error('OpenSubtitles: Error initializing service', error);
      return false;
    }
  }

  /**
   * Clear rate limit status
   */
  clearRateLimit() {
    this.isRateLimited = false;
    this.rateLimitResetTime = null;
    localStorage.removeItem('opensubtitles_rate_limit');
    sessionStorage.removeItem('opensubtitles_rate_limit_alert');
    sessionStorage.removeItem('opensubtitles_download_limit_alert');
    console.log('OpenSubtitles: Rate limit cleared');

    // Also clear any saved token as it might be invalid
    this.token = null;
    this.isLoggedIn = false;
    localStorage.removeItem('opensubtitles_token');
  }

  /**
   * Set rate limit status
   * @param {number} resetTimeMs - Reset time in milliseconds from now
   */
  setRateLimit(resetTimeMs = 3600000) { // Default to 1 hour
    this.isRateLimited = true;

    // Ensure the reset time is not more than 1 hour from now
    // This is a more reasonable limit for most API rate limits
    const maxResetTime = 3600000; // 1 hour in milliseconds
    const actualResetTime = Math.min(resetTimeMs, maxResetTime);

    // Force the reset time to be exactly 1 hour from now
    // This prevents any issues with incorrect dates like 2025
    const now = new Date();
    const oneHourFromNow = new Date(now.getTime() + actualResetTime);

    // Sanity check - if the reset time is more than 24 hours from now, something is wrong
    if (oneHourFromNow.getTime() - now.getTime() > 24 * 3600000) {
      console.error('OpenSubtitles: Invalid reset time detected, forcing to 1 hour');
      this.rateLimitResetTime = new Date(now.getTime() + 3600000);
    } else {
      this.rateLimitResetTime = oneHourFromNow;
    }

    // Save to localStorage with a fixed expiry
    localStorage.setItem('opensubtitles_rate_limit', JSON.stringify({
      resetTime: this.rateLimitResetTime.toISOString(),
      setAt: now.toISOString()
    }));

    // Calculate minutes for better logging
    const minutesUntilReset = Math.ceil(actualResetTime / 60000);
    console.log(`OpenSubtitles: Rate limited for ${minutesUntilReset} minutes (until ${this.rateLimitResetTime})`);

    // Clear any existing rate limit after the reset time
    setTimeout(() => {
      this.clearRateLimit();
    }, actualResetTime);
  }

  /**
   * Check if we need to throttle requests
   * @returns {Promise<void>}
   */
  async throttleRequest() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.minRequestInterval) {
      const waitTime = this.minRequestInterval - timeSinceLastRequest;
      console.log(`OpenSubtitles: Throttling request for ${waitTime}ms`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    this.lastRequestTime = Date.now();
  }

  /**
   * Login as guest to get a token
   * @returns {Promise<boolean>} Success status
   */
  async loginAsGuest() {
    try {
      // Check if we're rate limited
      if (this.isRateLimited) {
        return false;
      }

      // Throttle requests to avoid rate limiting
      await this.throttleRequest();

      console.log('OpenSubtitles: Attempting to login as guest');

      // For the OpenSubtitles API, we need to include a User-Agent header
      // and we should use the API key for authentication
      // We'll try to search without login first, as that's supported for basic operations
      const headers = {
        'Content-Type': 'application/json',
        'Api-Key': this.apiKey,
        'User-Agent': 'NetStream v1.0 (https://github.com/yourusername/netstream)',
        'Accept': 'application/json'
      };

      // Test if we can access the API without login by making a simple request
      // to the /infos/formats endpoint which doesn't require authentication
      const testResponse = await fetch(`${this.apiBaseUrl}/infos/formats`, {
        method: 'GET',
        headers: headers
      });

      // If we can access the API without login, we're good to go
      if (testResponse.ok) {
        console.log('OpenSubtitles: API accessible without login');
        this.isLoggedIn = true; // We can use the API with just the API key
        return true;
      }

      // If we need to login, try with the username
      console.log('OpenSubtitles: Attempting to login with username');
      const response = await fetch(`${this.apiBaseUrl}/login`, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify({
          username: this.username
        })
      });

      // Log the response status for debugging
      console.log('OpenSubtitles: Login response status:', response.status);

      // Handle different response statuses
      if (response.status === 401) {
        console.error('OpenSubtitles: Unauthorized - Invalid API key');
        return false;
      } else if (response.status === 403) {
        console.error('OpenSubtitles: Forbidden - API key may be rate limited');
        // Set rate limit for 1 hour
        this.setRateLimit(3600000);
        return false;
      } else if (response.status === 429) {
        console.error('OpenSubtitles: Too Many Requests - Rate limited');
        // Set rate limit for 1 hour
        this.setRateLimit(3600000);
        return false;
      } else if (!response.ok) {
        const errorText = await response.text();

        // Check if the error message contains rate limit information
        if (errorText.includes('rate limit') || errorText.includes('too many requests')) {
          console.error('OpenSubtitles: Rate limited based on error message');
          this.setRateLimit(3600000);
          return false;
        }

        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      console.log('OpenSubtitles: Login response data:', data);

      if (data.token) {
        this.token = data.token;
        this.isLoggedIn = true;

        // Save token to localStorage
        localStorage.setItem('opensubtitles_token', this.token);

        console.log('OpenSubtitles: Logged in successfully');
        return true;
      } else {
        console.error('OpenSubtitles: No token received');
        return false;
      }
    } catch (error) {
      console.error('OpenSubtitles: Error logging in:', error);

      // Check if the error message suggests rate limiting
      if (error.message && (
          error.message.includes('rate limit') ||
          error.message.includes('too many requests') ||
          error.message.includes('429')
      )) {
        console.error('OpenSubtitles: Rate limited based on error');
        this.setRateLimit(3600000);
      }

      return false;
    }
  }

  /**
   * Search for subtitles
   * @param {Object} params - Search parameters
   * @returns {Promise<Array>} Subtitles array
   */
  async searchSubtitles(params) {
    try {
      // Check if we're rate limited
      if (this.isRateLimited) {
        const now = new Date();
        if (this.rateLimitResetTime && this.rateLimitResetTime > now) {
          const minutesRemaining = Math.ceil((this.rateLimitResetTime - now) / 60000);
          console.log(`OpenSubtitles: Rate limited for ${minutesRemaining} more minutes`);

          // Show a user-friendly message
          const message = `OpenSubtitles API is currently rate limited. Please try again in ${minutesRemaining} minutes.`;
          if (typeof window !== 'undefined') {
            // Only show alert once per session
            if (!sessionStorage.getItem('opensubtitles_rate_limit_alert')) {
              alert(message);
              sessionStorage.setItem('opensubtitles_rate_limit_alert', 'true');
            }
          }

          return this.getFallbackResults(params);
        }
      }

      // Check cache first
      const cacheKey = this.getCacheKey(params);
      const cachedResults = this.getFromCache(cacheKey);
      if (cachedResults) {
        console.log('OpenSubtitles: Using cached results for', params.query);
        return cachedResults;
      }

      if (!this.isLoggedIn) {
        const loginSuccess = await this.initialize();
        if (!loginSuccess) {
          console.error('OpenSubtitles: Failed to initialize before search');
          return this.getFallbackResults(params);
        }
      }

      // Throttle requests to avoid rate limiting
      await this.throttleRequest();

      // Build query string from params
      const queryParams = new URLSearchParams();

      // Map the media title to the query parameter
      if (params.query) {
        queryParams.append('query', params.query);
      }

      // Add languages if not specified (comma-separated list of language codes)
      if (!params.languages) {
        queryParams.append('languages', 'en,fr');
      } else {
        queryParams.append('languages', params.languages);
      }

      // Add other optional parameters if provided
      if (params.moviehash) queryParams.append('moviehash', params.moviehash);
      if (params.imdb_id) queryParams.append('imdb_id', params.imdb_id);
      if (params.tmdb_id) queryParams.append('tmdb_id', params.tmdb_id);
      if (params.type) queryParams.append('type', params.type); // movie, episode, all
      if (params.season_number) queryParams.append('season_number', params.season_number);
      if (params.episode_number) queryParams.append('episode_number', params.episode_number);
      if (params.year) queryParams.append('year', params.year);

      // Add pagination parameters
      queryParams.append('page', params.page || 1);

      console.log('OpenSubtitles: Searching with params:', queryParams.toString());

      // Prepare headers with User-Agent which is required by OpenSubtitles API
      const headers = {
        'Content-Type': 'application/json',
        'Api-Key': this.apiKey,
        'User-Agent': 'NetStream v1.0 (https://github.com/yourusername/netstream)',
        'Accept': 'application/json'
      };

      // Add Authorization header if we have a token
      if (this.token) {
        headers['Authorization'] = `Bearer ${this.token}`;
      }

      const response = await fetch(`${this.apiBaseUrl}/subtitles?${queryParams.toString()}`, {
        method: 'GET',
        headers: headers
      });

      // Log the response status for debugging
      console.log('OpenSubtitles: Search response status:', response.status);

      // Handle different response statuses
      if (response.status === 401) {
        console.error('OpenSubtitles: Unauthorized - Token may be expired');
        // Try to re-login and search again
        if (await this.loginAsGuest()) {
          return this.searchSubtitles(params); // Retry with new token
        }
        return this.getFallbackResults(params);
      } else if (response.status === 403) {
        console.error('OpenSubtitles: Forbidden - API key may be rate limited');
        this.setRateLimit(3600000); // 1 hour
        return this.getFallbackResults(params);
      } else if (response.status === 429) {
        console.error('OpenSubtitles: Too Many Requests - Rate limited');
        this.setRateLimit(3600000); // 1 hour
        return this.getFallbackResults(params);
      } else if (!response.ok) {
        const errorText = await response.text();

        // Check if the error message contains rate limit information
        if (errorText.includes('rate limit') || errorText.includes('too many requests')) {
          console.error('OpenSubtitles: Rate limited based on error message');
          this.setRateLimit(3600000);
          return this.getFallbackResults(params);
        }

        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      console.log('OpenSubtitles: Search found', data.data?.length || 0, 'results');

      // Cache the results
      if (data.data && data.data.length > 0) {
        this.saveToCache(cacheKey, data.data);
      }

      return data.data || [];
    } catch (error) {
      console.error('OpenSubtitles: Error searching subtitles', error);

      // Check if the error message suggests rate limiting
      if (error.message && (
          error.message.includes('rate limit') ||
          error.message.includes('too many requests') ||
          error.message.includes('429')
      )) {
        console.error('OpenSubtitles: Rate limited based on error');
        this.setRateLimit(3600000);
      }

      return this.getFallbackResults(params);
    }
  }

  /**
   * Get a cache key for search parameters
   * @param {Object} params - Search parameters
   * @returns {string} Cache key
   */
  getCacheKey(params) {
    // Create a stable key from the search parameters
    const key = {
      query: params.query || '',
      languages: params.languages || 'en,fr',
      type: params.type || '',
      season: params.season_number || '',
      episode: params.episode_number || '',
      year: params.year || ''
    };

    return JSON.stringify(key);
  }

  /**
   * Get results from cache
   * @param {string} key - Cache key
   * @returns {Array|null} Cached results or null
   */
  getFromCache(key) {
    const cacheItem = this.searchCache[key];
    if (!cacheItem) return null;

    // Check if cache is expired
    if (Date.now() - cacheItem.timestamp > this.cacheExpiry) {
      delete this.searchCache[key];
      return null;
    }

    return cacheItem.data;
  }

  /**
   * Save results to cache
   * @param {string} key - Cache key
   * @param {Array} data - Results to cache
   */
  saveToCache(key, data) {
    this.searchCache[key] = {
      timestamp: Date.now(),
      data: data
    };
  }

  /**
   * Get fallback results when API is unavailable
   * @param {Object} params - Search parameters
   * @returns {Array} Empty array or mock results
   */
  getFallbackResults(params) {
    // For now, just return an empty array
    // In the future, could return mock results or locally stored subtitles
    return [];
  }

  /**
   * Download subtitle
   * @param {string} fileId - Subtitle file ID
   * @returns {Promise<Object>} Download link and information
   */
  async downloadSubtitle(fileId) {
    try {
      // Check if we're rate limited
      if (this.isRateLimited) {
        const now = new Date();
        if (this.rateLimitResetTime && this.rateLimitResetTime > now) {
          const minutesRemaining = Math.ceil((this.rateLimitResetTime - now) / 60000);
          console.log(`OpenSubtitles: Rate limited for ${minutesRemaining} more minutes`);

          // Show a user-friendly message
          const message = `OpenSubtitles API is currently rate limited. Please try again in ${minutesRemaining} minutes.`;
          if (typeof window !== 'undefined') {
            // Only show alert once per session
            if (!sessionStorage.getItem('opensubtitles_download_limit_alert')) {
              alert(message);
              sessionStorage.setItem('opensubtitles_download_limit_alert', 'true');
            }
          }

          return null;
        }
      }

      if (!this.isLoggedIn) {
        const loginSuccess = await this.initialize();
        if (!loginSuccess) {
          console.error('OpenSubtitles: Failed to initialize before download');
          return null;
        }
      }

      // Throttle requests to avoid rate limiting
      await this.throttleRequest();

      console.log('OpenSubtitles: Downloading subtitle with file ID:', fileId);

      // Prepare headers with User-Agent which is required by OpenSubtitles API
      const headers = {
        'Content-Type': 'application/json',
        'Api-Key': this.apiKey,
        'User-Agent': 'NetStream v1.0 (https://github.com/yourusername/netstream)',
        'Accept': 'application/json'
      };

      // Add Authorization header if we have a token
      if (this.token) {
        headers['Authorization'] = `Bearer ${this.token}`;
      }

      const response = await fetch(`${this.apiBaseUrl}/download`, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify({
          file_id: fileId
        })
      });

      // Log the response status for debugging
      console.log('OpenSubtitles: Download response status:', response.status);

      // Handle different response statuses
      if (response.status === 401) {
        console.error('OpenSubtitles: Unauthorized - Token may be expired');
        // Try to re-login and download again
        if (await this.loginAsGuest()) {
          return this.downloadSubtitle(fileId); // Retry with new token
        }
        return null;
      } else if (response.status === 403) {
        console.error('OpenSubtitles: Forbidden - API key may be rate limited or download quota exceeded');
        this.setRateLimit(3600000); // 1 hour
        alert('Download quota exceeded. Please try again later.');
        return null;
      } else if (response.status === 406) {
        console.error('OpenSubtitles: Not Acceptable - Daily download limit reached');
        this.setRateLimit(3600000); // 1 hour
        alert('Daily download limit reached. Please try again tomorrow.');
        return null;
      } else if (response.status === 429) {
        console.error('OpenSubtitles: Too Many Requests - Rate limited');
        this.setRateLimit(3600000); // 1 hour
        alert('Too many requests. Please try again later.');
        return null;
      } else if (response.status === 404) {
        console.error('OpenSubtitles: File not found');
        alert('Subtitle file not found. Please try another subtitle.');
        return null;
      } else if (!response.ok) {
        const errorText = await response.text();

        // Check if the error message contains rate limit information
        if (errorText.includes('rate limit') || errorText.includes('too many requests')) {
          console.error('OpenSubtitles: Rate limited based on error message');
          this.setRateLimit(3600000);
          alert('API rate limit reached. Please try again later.');
          return null;
        }

        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const data = await response.json();
      console.log('OpenSubtitles: Download info received:', data);

      // Check if we have a valid download link
      if (!data.link) {
        console.error('OpenSubtitles: No download link received');
        alert('No download link received. Please try another subtitle.');
        return null;
      }

      return data;
    } catch (error) {
      console.error('OpenSubtitles: Error downloading subtitle', error);

      // Check if the error message suggests rate limiting
      if (error.message && (
          error.message.includes('rate limit') ||
          error.message.includes('too many requests') ||
          error.message.includes('429')
      )) {
        console.error('OpenSubtitles: Rate limited based on error');
        this.setRateLimit(3600000);
        alert('API rate limit reached. Please try again later.');
      } else {
        // Generic error message
        alert('Error downloading subtitle. Please try again later.');
      }

      return null;
    }
  }

  /**
   * Fetch subtitle content
   * @param {string} url - Subtitle download URL
   * @returns {Promise<string>} Subtitle content
   */
  async fetchSubtitleContent(url) {
    try {
      console.log('OpenSubtitles: Fetching subtitle content from URL:', url);

      // Use the subtitle proxy service if available
      if (window.subtitleProxyService && typeof window.subtitleProxyService.downloadSubtitle === 'function') {
        console.log('OpenSubtitles: Using subtitle proxy service');
        return await window.subtitleProxyService.downloadSubtitle(url);
      }

      // Direct fetch as fallback
      console.log('OpenSubtitles: Using direct fetch (no proxy available)');
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'NetStream v1.0 (https://github.com/yourusername/netstream)',
          'Accept': 'text/plain, application/octet-stream'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Check content type
      const contentType = response.headers.get('content-type');
      console.log('OpenSubtitles: Content type:', contentType);

      // Handle different content types
      if (contentType) {
        if (contentType.includes('application/x-gzip') ||
            contentType.includes('application/gzip') ||
            contentType.includes('application/x-compressed') ||
            url.endsWith('.gz')) {
          // Handle gzipped content
          console.error('OpenSubtitles: Gzipped content not supported in browser without proxy');
          alert('Gzipped subtitles require server-side processing. Please try another subtitle.');
          return null;
        } else if (contentType.includes('application/zip') || url.endsWith('.zip')) {
          // Handle zipped content
          console.error('OpenSubtitles: Zipped content not supported in browser without proxy');
          alert('Zipped subtitles require server-side processing. Please try another subtitle.');
          return null;
        } else if (contentType.includes('application/octet-stream') &&
                  (url.endsWith('.srt') || url.endsWith('.vtt') || url.endsWith('.sub') || url.endsWith('.ssa') || url.endsWith('.ass'))) {
          // Likely a subtitle file with incorrect content type
          console.log('OpenSubtitles: Detected subtitle file with octet-stream content type');
        } else if (!contentType.includes('text/plain') &&
                  !contentType.includes('text/srt') &&
                  !contentType.includes('text/vtt') &&
                  !contentType.includes('application/x-subrip')) {
          // Unknown content type, but we'll try anyway
          console.warn('OpenSubtitles: Unknown content type:', contentType);
        }
      }

      // Get the text content
      const text = await response.text();

      // Basic validation - check if it looks like a subtitle file
      if (text.length < 10) {
        console.error('OpenSubtitles: Subtitle content too short');
        return null;
      }

      // For SRT files, check for timestamp format
      if (url.endsWith('.srt') || text.includes(' --> ')) {
        if (!text.match(/\d\d:\d\d:\d\d,\d\d\d --> \d\d:\d\d:\d\d,\d\d\d/)) {
          console.warn('OpenSubtitles: Content doesn\'t look like a valid SRT file, but will try anyway');
        }
      }

      console.log('OpenSubtitles: Successfully fetched subtitle content, length:', text.length);
      return text;
    } catch (error) {
      console.error('OpenSubtitles: Error fetching subtitle content', error);
      return null;
    }
  }
}

// Create a singleton instance
const openSubtitlesService = new OpenSubtitlesService();

// Export the service
window.openSubtitlesService = openSubtitlesService;
