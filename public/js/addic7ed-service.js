/**
 * Addic7ed Service
 * Handles fetching subtitles from Addic7ed.com
 *
 * This service communicates with the server-side Addic7ed scraper
 * to search for and download subtitles from Addic7ed.com.
 *
 * The server-side scraper handles all the complexities of:
 * - Finding the correct show page
 * - Extracting subtitle information
 * - Handling download links
 * - Dealing with CORS issues
 */

class Addic7edService {
  constructor() {
    this.apiBaseUrl = '/api/addic7ed';
    this.subtitleCache = new Map(); // Cache for subtitle search results
  }

  /**
   * Search for subtitles for a specific show, season, and episode
   * @param {string} show - Show name
   * @param {number} season - Season number
   * @param {number} episode - Episode number
   * @param {string} language - Language to filter by (optional)
   * @param {boolean} useGemini - Whether to use Gemini AI for enhanced matching (optional)
   * @returns {Promise<Array>} Array of subtitle info
   */
  async searchSubtitles(show, season, episode, language = '', useGemini = false) {
    try {
      // Create a cache key
      const cacheKey = `${show}_${season}_${episode}_${language}_${useGemini}`;

      // Check cache first
      if (this.subtitleCache.has(cacheKey)) {
        console.log('Addic7ed: Using cached subtitle search results');
        return this.subtitleCache.get(cacheKey);
      }

      console.log(`Addic7ed: Searching for subtitles - ${show} S${season}E${episode} [${language || 'all languages'}]${useGemini ? ' with Gemini AI' : ''}`);

      // Build the query URL
      const queryParams = new URLSearchParams({
        show: show,
        season: season,
        episode: episode
      });

      if (language) {
        queryParams.append('language', language);
      }

      // Add Gemini flag if enabled
      if (useGemini) {
        queryParams.append('useGemini', 'true');
      }

      // Make the API request
      const response = await fetch(`${this.apiBaseUrl}/subtitles?${queryParams.toString()}`);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Unknown error');
      }

      // Cache the results
      this.subtitleCache.set(cacheKey, data.subtitles);

      console.log(`Addic7ed: Found ${data.subtitles.length} subtitles`);
      return data.subtitles;
    } catch (error) {
      console.error('Addic7ed: Error searching for subtitles', error);
      throw error;
    }
  }

  /**
   * Get a download URL for a subtitle
   * @param {string} originalUrl - Original Addic7ed download URL
   * @returns {string} Proxied download URL
   */
  getProxiedDownloadUrl(originalUrl) {
    return `/api/addic7ed/download?url=${encodeURIComponent(originalUrl)}`;
  }

  /**
   * Download a subtitle file
   * @param {string} url - Addic7ed download URL
   * @returns {Promise<string>} Subtitle content
   */
  async downloadSubtitle(url) {
    try {
      console.log('Addic7ed: Downloading subtitle', url);

      // Use the proxy endpoint to download the subtitle
      const proxyUrl = this.getProxiedDownloadUrl(url);

      const response = await fetch(proxyUrl);

      if (!response.ok) {
        throw new Error(`Failed to download subtitle: ${response.status} ${response.statusText}`);
      }

      const content = await response.text();
      return content;
    } catch (error) {
      console.error('Addic7ed: Error downloading subtitle', error);
      throw error;
    }
  }

  /**
   * Parse show title, season, and episode from a media title
   * @param {string} mediaTitle - Media title (e.g., "Show Name S01E02", "Show Name - 1x02")
   * @returns {Object|null} Parsed info or null if not parseable
   */
  parseMediaTitle(mediaTitle) {
    if (!mediaTitle) return null;

    console.log('Addic7ed: Parsing media title:', mediaTitle);

    // Try different regex patterns for various formats

    // Format: "Series: Show Title - S01:E02"
    let match = mediaTitle.match(/(?:Series|Anime):\s+(.*?)\s+-\s+S(\d+):E(\d+)/i);
    if (match) {
      console.log('Addic7ed: Matched NetStream series format');
      return {
        show: this.normalizeShowTitle(match[1].trim()),
        season: parseInt(match[2], 10),
        episode: parseInt(match[3], 10)
      };
    }

    // Format: "Show Name S01E02"
    match = mediaTitle.match(/^(.*?)\s+S(\d+)[\s-]*E(\d+)/i);
    if (match) {
      console.log('Addic7ed: Matched S01E02 format');
      return {
        show: this.normalizeShowTitle(match[1].trim()),
        season: parseInt(match[2], 10),
        episode: parseInt(match[3], 10)
      };
    }

    // Format: "Show Name - 1x02"
    match = mediaTitle.match(/^(.*?)\s*[-:]\s*(\d+)x(\d+)/i);
    if (match) {
      console.log('Addic7ed: Matched 1x02 format');
      return {
        show: this.normalizeShowTitle(match[1].trim()),
        season: parseInt(match[2], 10),
        episode: parseInt(match[3], 10)
      };
    }

    // Format: "Show Name - Season 1 Episode 2"
    match = mediaTitle.match(/^(.*?)\s*[-:]\s*Season\s+(\d+)\s+Episode\s+(\d+)/i);
    if (match) {
      console.log('Addic7ed: Matched Season/Episode format');
      return {
        show: this.normalizeShowTitle(match[1].trim()),
        season: parseInt(match[2], 10),
        episode: parseInt(match[3], 10)
      };
    }

    // Format: "Show Name.S01E02" or "Show.Name.S01E02"
    match = mediaTitle.match(/^(.*?)\.S(\d+)E(\d+)/i);
    if (match) {
      console.log('Addic7ed: Matched dot-separated format');
      // Replace dots with spaces in the show name
      const showName = match[1].replace(/\./g, ' ').trim();
      return {
        show: this.normalizeShowTitle(showName),
        season: parseInt(match[2], 10),
        episode: parseInt(match[3], 10)
      };
    }

    // Try to extract from filename-like format
    // Format: "Show.Name.S01E02.1080p.WEB.H264-GROUP"
    match = mediaTitle.match(/^(.*?)\.S(\d+)E(\d+)\./i);
    if (match) {
      console.log('Addic7ed: Matched filename format');
      // Replace dots with spaces in the show name
      const showName = match[1].replace(/\./g, ' ').trim();
      return {
        show: this.normalizeShowTitle(showName),
        season: parseInt(match[2], 10),
        episode: parseInt(match[3], 10)
      };
    }

    // Last resort: try to find any season and episode numbers
    match = mediaTitle.match(/S(\d+)[\s\.\-_]*E(\d+)/i);
    if (match) {
      console.log('Addic7ed: Matched partial S01E02 format');
      // Try to extract show name from everything before the S01E02 pattern
      const showNameMatch = mediaTitle.match(/^(.*?)S\d+[\s\.\-_]*E\d+/i);
      const showName = showNameMatch ?
        showNameMatch[1].replace(/\./g, ' ').trim() :
        'Unknown Show';

      return {
        show: this.normalizeShowTitle(showName),
        season: parseInt(match[1], 10),
        episode: parseInt(match[2], 10)
      };
    }

    console.log('Addic7ed: Could not parse media title');
    return null;
  }

  /**
   * Normalize show title to improve matching with Addic7ed
   * @param {string} title - Original show title
   * @returns {string} Normalized show title
   */
  normalizeShowTitle(title) {
    if (!title) return '';

    let normalizedTitle = title;

    // Store the original title for logging
    const originalTitle = title;

    // Common prefixes that might need to be removed
    const prefixes = [
      'Star Wars:'
    ];

    // Check if the title starts with any of the prefixes
    for (const prefix of prefixes) {
      if (normalizedTitle.startsWith(prefix)) {
        normalizedTitle = normalizedTitle.substring(prefix.length).trim();
        console.log(`Addic7ed: Removed prefix "${prefix}" from title`);
        break;
      }
    }

    // Remove special characters and extra spaces
    normalizedTitle = normalizedTitle
      .replace(/[^\w\s]/g, ' ') // Replace special characters with spaces
      .replace(/\s+/g, ' ')     // Replace multiple spaces with a single space
      .trim();

    // If the title changed, log it
    if (normalizedTitle !== originalTitle) {
      console.log(`Addic7ed: Normalized title from "${originalTitle}" to "${normalizedTitle}"`);
    }

    return normalizedTitle;
  }

  /**
   * Search for subtitles with fallback to Gemini AI
   * @param {string} show - Show name
   * @param {number} season - Season number
   * @param {number} episode - Episode number
   * @param {string} language - Language to filter by (optional)
   * @returns {Promise<Array>} Array of subtitle info
   */
  async searchSubtitlesWithFallback(show, season, episode, language = '') {
    try {
      // Try with the original show name first
      console.log(`Addic7ed: Searching for subtitles with original title: ${show}`);
      let subtitles = await this.searchSubtitles(show, season, episode, language);

      // If subtitles were found, return them
      if (subtitles && subtitles.length > 0) {
        console.log(`Addic7ed: Found ${subtitles.length} subtitles with original title`);
        return subtitles;
      }

      // Skip alternative names - going straight to episode titles or Gemini AI

      // Try with known episode titles for specific shows
      const episodeTitle = this.getKnownEpisodeTitle(show, season, episode);
      if (episodeTitle) {
        console.log(`Addic7ed: Trying with known episode title: ${episodeTitle}`);

        // Try direct URL with the original show name and episode title
        try {
          // Construct the URL for Addic7ed with the episode title
          const formattedShowName = show.replace(/\s+/g, '_');
          const directUrl = `/api/addic7ed/subtitles?show=${encodeURIComponent(formattedShowName)}&season=${season}&episode=${episode}&episodeTitle=${encodeURIComponent(episodeTitle)}`;

          console.log(`Addic7ed: Trying direct URL with episode title: ${directUrl}`);

          const response = await fetch(directUrl);
          if (response.ok) {
            const data = await response.json();
            if (data.success && data.subtitles && data.subtitles.length > 0) {
              console.log(`Addic7ed: Found ${data.subtitles.length} subtitles with episode title`);
              return data.subtitles;
            }
          }
        } catch (error) {
          console.warn(`Addic7ed: Error with episode title approach: ${error.message}`);
        }

        // Skip alternative names with episode title - we'll use Gemini AI instead
      }

      // Special handling for shows that might have season numbering issues
      if (season > 1) {
        console.log(`Addic7ed: No results for season ${season}, checking if show exists with season 1`);

        // Check if the show exists with season 1 (to verify the show exists on Addic7ed)
        const season1Check = await this.searchSubtitles(show, 1, 1, language);

        if (season1Check && season1Check.length > 0) {
          console.log(`Addic7ed: Found season 1 subtitles, show exists but season ${season} might not be available yet`);

          // Try with season 1 but the current episode number as a fallback
          console.log(`Addic7ed: Trying with season 1, episode ${episode} as fallback`);
          const fallbackSubtitles = await this.searchSubtitles(show, 1, episode, language);

          if (fallbackSubtitles && fallbackSubtitles.length > 0) {
            console.log(`Addic7ed: Found ${fallbackSubtitles.length} subtitles with season 1, episode ${episode}`);
            return fallbackSubtitles;
          }
        }

        // Skip alternative names with season 1 - we'll use Gemini AI instead
      }

      // Try Gemini AI for better matching
      console.log(`Addic7ed: Trying Gemini AI for intelligent title matching`);
      try {
        // Show a message to the user that Gemini AI is being used
        const subtitleContainer = document.getElementById('online-subtitles-container');
        if (subtitleContainer) {
          subtitleContainer.innerHTML = '<div class="subtitle-loading">Using Gemini AI to find the best match for this title...</div>';
        }

        const geminiSubtitles = await this.searchSubtitles(show, season, episode, language, true);
        if (geminiSubtitles && geminiSubtitles.length > 0) {
          console.log(`Addic7ed: Found ${geminiSubtitles.length} subtitles with Gemini AI`);
          return geminiSubtitles;
        }
      } catch (geminiError) {
        console.warn(`Addic7ed: Error using Gemini AI: ${geminiError.message}`);
      }

      // If still no subtitles found, return empty array
      console.log(`Addic7ed: No subtitles found for ${show} S${season}E${episode} after trying all methods`);
      return [];
    } catch (error) {
      console.error('Addic7ed: Error searching for subtitles with fallback', error);
      throw error;
    }
  }

  /**
   * Generate alternative names for a show
   * @param {string} showName - Original show name
   * @returns {Array<string>} Array of alternative names
   * @deprecated This function is no longer used as we rely on Gemini AI for better matching
   */
  generateAlternativeNames(showName) {
    // Return empty array as we're no longer using alternative names
    // We'll rely on Gemini AI for better matching instead
    return [];
  }

  /**
   * Get episode title from Addic7ed for specific shows
   * @param {string} show - Show name
   * @param {number} season - Season number
   * @param {number} episode - Episode number
   * @returns {string|null} Episode title if known, null otherwise
   */
  getKnownEpisodeTitle(show, season, episode) {
    // Known episode titles for specific shows
    const episodeTitles = {
      'andor': {
        '2-1': 'One_Year_Later',
        '2-2': 'Sagrona_Teema',
        '2-3': 'Harvest',
        '2-4': 'Ever_Been_to_Ghorman?',
        '2-5': 'I_Have_Friends_Everywhere',
        '2-6': 'What_a_Festive_Evening',
        '2-7': 'Messenger',
        '2-8': 'Who_Are_You?',
        '2-9': 'Welcome_to_the_Rebellion',
        '2-10': 'Make_It_Stop',
        '2-11': 'Who_Else_Knows?',
        '2-12': 'Jedha,_Kyber,_Erso'
      },
      'the last of us': {
        '2-1': 'Future_Days',
        '2-2': 'Infected',
        '2-3': 'Wasteland',
        '2-4': 'Please_Hold_My_Hand',
        '2-5': 'Feel_Her_Love',
        '2-6': 'Left_Behind',
        '2-7': 'Kin'
      }
    };

    // Normalize show name for lookup
    const normalizedShow = show.toLowerCase().replace(/[^\w\s]/g, '').trim();

    // Check if we have episode titles for this show
    if (episodeTitles[normalizedShow]) {
      const key = `${season}-${episode}`;
      return episodeTitles[normalizedShow][key] || null;
    }

    return null;
  }
}

// Create a singleton instance
const addic7edService = new Addic7edService();

// Export the service
window.addic7edService = addic7edService;
