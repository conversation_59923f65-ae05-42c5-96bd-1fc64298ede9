/* Modern Media Page - Clean Layout */

/* Media Header */
.media-header {
  display: flex;
  gap: 30px;
  padding: 30px;
  background: #1a1a1a;
  border-radius: 12px;
  margin-bottom: 30px;
}

.media-poster {
  flex-shrink: 0;
}

.poster-image {
  width: 300px;
  height: 450px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0,0,0,0.4);
  background-color: #2a2a2a;
}

.media-info {
  flex: 1;
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.media-info h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 15px 0;
  line-height: 1.2;
}

.media-meta {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.rating-badge {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  background: rgba(255,255,255,0.15);
  padding: 6px 12px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
}

.rating-badge i {
  color: #ffd700;
}

.media-meta span:not(.rating-badge) {
  color: rgba(255,255,255,0.8);
  font-size: 1rem;
}

.media-synopsis {
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0 0 25px 0;
  color: rgba(255,255,255,0.9);
  max-width: 600px;
}

.media-actions {
  display: flex;
  gap: 15px;
  align-items: center;
}

.btn-play {
  background: white;
  color: black;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.btn-play:hover {
  background: rgba(255,255,255,0.9);
  transform: scale(1.05);
}

.btn-wishlist {
  background: rgba(109,109,110,0.7);
  color: white;
  border: 1px solid rgba(255,255,255,0.3);
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  min-width: 120px;
  justify-content: center;
}

.btn-wishlist:hover {
  background: rgba(109,109,110,0.9);
  border-color: rgba(255,255,255,0.6);
  transform: scale(1.05);
}

.btn-wishlist.active {
  background: rgba(142, 36, 170, 0.2);
  border-color: #8e24aa;
  color: #8e24aa;
}

.btn-wishlist.active:hover {
  background: rgba(142, 36, 170, 0.3);
  border-color: #8e24aa;
  color: #8e24aa;
}

.content-wrapper {
  background: transparent;
  padding: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 20px;
}

.section-header h2 {
  color: white;
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0;
}

.episode-controls {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.season-dropdown,
.language-filter {
  display: flex;
  align-items: center;
  gap: 8px;
}

.season-dropdown label,
.language-filter label {
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
}

.season-dropdown select,
.language-filter select {
  background: #333;
  color: white;
  border: 1px solid #555;
  padding: 8px 15px;
  border-radius: 4px;
  font-size: 1rem;
  min-width: 120px;
}

.season-dropdown select:focus,
.language-filter select:focus {
  outline: none;
  border-color: #e50914;
}

.episodes-section {
  margin-bottom: 60px;
}

/* Episodes Container - Same as TMDB */
.episodes-grid,
#episodes.grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)) !important;
  gap: 15px !important;
  margin-bottom: 40px !important;
  padding: 0 !important;
}

/* EXACT COPY FROM tmdb-seasons.css - WORKING VERSION */
#episodes .grid-item,
#episodes.episodes-grid .grid-item,
#episodes.grid .grid-item,
.episodes-grid .grid-item,
.simple-episode-card {
  background-color: #2a2a2a !important;
  border-radius: 8px !important;
  padding: 15px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
  transition: transform 0.2s, box-shadow 0.2s !important;
  cursor: pointer !important;
  border: none !important;
  margin: 0 !important;
  display: block !important;
  font-size: inherit !important;
  height: auto !important;
  min-height: auto !important;
  aspect-ratio: auto !important;
  font-weight: normal !important;
  position: relative !important;
  overflow: visible !important;
  align-items: stretch !important;
  justify-content: flex-start !important;
}

#episodes .grid-item:hover,
#episodes.episodes-grid .grid-item:hover,
#episodes.grid .grid-item:hover,
.episodes-grid .grid-item:hover,
.simple-episode-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
}

/* Remove the ::before pseudo-element from styles.css */
#episodes .grid-item::before,
#episodes.episodes-grid .grid-item::before,
#episodes.grid .grid-item::before,
.episodes-grid .grid-item::before {
  display: none !important;
}

#episodes .grid-item h5,
#episodes.episodes-grid .grid-item h5,
#episodes.grid .grid-item h5,
.episodes-grid .grid-item h5,
.simple-episode-card h5 {
  margin-top: 0 !important;
  margin-bottom: 10px !important;
  font-size: 1.1em !important;
  color: #00bcd4 !important;
}

#episodes .grid-item .tmdb-episode-meta,
#episodes.episodes-grid .grid-item .tmdb-episode-meta,
#episodes.grid .grid-item .tmdb-episode-meta,
.episodes-grid .grid-item .tmdb-episode-meta,
.simple-episode-card .tmdb-episode-meta {
  display: flex !important;
  justify-content: space-between !important;
  margin-bottom: 10px !important;
  font-size: 0.9em !important;
  color: #aaa !important;
}

#episodes .grid-item .tmdb-episode-overview,
#episodes.episodes-grid .grid-item .tmdb-episode-overview,
#episodes.grid .grid-item .tmdb-episode-overview,
.episodes-grid .grid-item .tmdb-episode-overview,
.simple-episode-card .tmdb-episode-overview {
  margin-top: 10px !important;
}

#episodes .grid-item .tmdb-episode-overview p,
#episodes.episodes-grid .grid-item .tmdb-episode-overview p,
#episodes.grid .grid-item .tmdb-episode-overview p,
.episodes-grid .grid-item .tmdb-episode-overview p,
.simple-episode-card .tmdb-episode-overview p {
  margin-bottom: 10px !important;
  font-size: 0.9em !important;
  line-height: 1.4 !important;
}

#episodes .grid-item .play-episode-btn,
#episodes.episodes-grid .grid-item .play-episode-btn,
#episodes.grid .grid-item .play-episode-btn,
.episodes-grid .grid-item .play-episode-btn,
.simple-episode-card .play-episode-btn {
  background-color: #00bcd4 !important;
  color: #fff !important;
  border: none !important;
  border-radius: 4px !important;
  padding: 5px 10px !important;
  cursor: pointer !important;
  font-size: 0.9em !important;
  transition: background-color 0.2s !important;
}

#episodes .grid-item .play-episode-btn:hover,
#episodes.episodes-grid .grid-item .play-episode-btn:hover,
#episodes.grid .grid-item .play-episode-btn:hover,
.episodes-grid .grid-item .play-episode-btn:hover,
.simple-episode-card .play-episode-btn:hover {
  background-color: #00a0b7 !important;
}

#episodes .grid-item .no-streams,
#episodes.episodes-grid .grid-item .no-streams,
#episodes.grid .grid-item .no-streams,
.episodes-grid .grid-item .no-streams,
.simple-episode-card .no-streams {
  color: #888 !important;
  font-style: italic !important;
  font-size: 0.9em !important;
}

/* Style provider buttons container */
.tmdb-episode-card .provider-buttons {
  display: flex !important;
  gap: 8px !important;
  margin-top: 8px !important;
}

/* Style provider stream button */
.tmdb-episode-card .provider-stream-btn {
  background-color: #e50914 !important;
  color: #fff !important;
  border: none !important;
  border-radius: 4px !important;
  padding: 8px 12px !important;
  cursor: pointer !important;
  font-size: 0.9em !important;
  font-weight: 500 !important;
  transition: background-color 0.2s !important;
  flex: 1 !important;
}

.tmdb-episode-card .provider-stream-btn:hover {
  background-color: #f40612 !important;
}

/* Style source-link buttons in tmdb-episode-card providers */
.tmdb-episode-card .source-link {
  background-color: #00bcd4 !important;
  color: #fff !important;
  border: none !important;
  border-radius: 4px !important;
  padding: 8px 12px !important;
  cursor: pointer !important;
  font-size: 0.9em !important;
  font-weight: 500 !important;
  transition: background-color 0.2s !important;
  flex: 1 !important;
}

.tmdb-episode-card .source-link:hover {
  background-color: #00a0b7 !important;
}

/* Update providers container to use grid layout like episodes */
.providers-container {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)) !important;
  gap: 15px !important;
}

/* Simple Badges */
.provider-badge,
.episode-languages,
.stream-count,
.no-streams,
.extra-info {
  background: #333;
  color: #ccc;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  text-decoration: none;
  white-space: nowrap;
}

.provider-badge:hover {
  background: #444;
  color: #fff;
}

.stream-count {
  background: #0d7377;
  color: #fff;
}

.episode-languages {
  background: #1976d2;
  color: #fff;
}

.no-streams {
  background: #666;
  color: #999;
}

.play-episode-btn {
  background: #e50914;
  color: #fff;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}

.play-episode-btn:hover {
  background: #f40612;
}

.providers-section {
  margin-bottom: 60px;
}

.providers-section h2 {
  color: white;
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 30px 0;
}

.providers-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.providers-container .grid-item {
  background: #2a2a2a;
  border-radius: 8px;
  padding: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  color: white;
}

.providers-container .grid-item:hover {
  background: #333;
  border-color: #e50914;
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.5);
}

.provider-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
  text-align: center;
  color: white;
}

.source-stream-url button {
  width: 100%;
  background: #e50914;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.source-stream-url button:hover {
  background: #f40612;
  transform: scale(1.05);
}

.details-section {
  margin-bottom: 60px;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.detail-card {
  background: #2a2a2a;
  border-radius: 8px;
  padding: 30px;
  color: white;
}

.detail-card h3 {
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 20px 0;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 10px 0;
  border-bottom: 1px solid #444;
  gap: 15px;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row.full-width {
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.detail-row .label {
  font-weight: 600;
  color: #ccc;
  min-width: 120px;
  flex-shrink: 0;
}

.detail-row span:last-child {
  color: white;
  text-align: right;
  flex: 1;
}

.detail-row.full-width span:last-child {
  text-align: left;
  line-height: 1.5;
}

@media (max-width: 1024px) {
  .media-header {
    padding: 20px;
    gap: 20px;
  }

  .poster-image {
    width: 250px;
    height: 375px;
  }

  .media-info h1 {
    font-size: 2rem;
  }

  /* Medium screen episode layout */
  .episodes-grid,
  #episodes.grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)) !important;
  }
}

@media (max-width: 768px) {
  .media-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 20px;
  }

  .poster-image {
    width: 200px;
    height: 300px;
  }

  .media-info h1 {
    font-size: 1.8rem;
  }

  .media-actions {
    justify-content: center;
  }

  /* Mobile Episode Layout */
  .episodes-grid,
  #episodes.grid {
    grid-template-columns: 1fr !important;
  }

  .episodes-grid .grid-item,
  .simple-episode-card,
  .tmdb-episode-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
  }

  .episode-number {
    align-self: flex-start;
  }

  .episode-title {
    white-space: normal;
  }

  .episode-actions {
    align-self: flex-start;
  }

  .providers-container {
    grid-template-columns: 1fr;
  }

  .details-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .episode-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .media-header {
    padding: 15px;
  }

  .poster-image {
    width: 150px;
    height: 225px;
  }

  .media-info h1 {
    font-size: 1.5rem;
  }

  .media-meta {
    justify-content: center;
  }

  /* Small Screen Adjustments */
  .episode-title {
    font-size: 15px;
  }

  .episode-overview {
    font-size: 12px;
  }
}