/* Jikan Seasons Section Styles */
.jikan-seasons-section {
  margin-bottom: 20px;
  display: none; /* Hidden by default, shown when data is available */
}

.jikan-seasons-container {
  padding: 10px;
}

.jikan-seasons-select-container {
  margin-bottom: 15px;
}

.jikan-seasons-select-container label {
  margin-right: 10px;
  font-weight: 500;
}

#jikan-season-select {
  padding: 5px 10px;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #333;
  color: #fff;
}

.jikan-season-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.jikan-season-info {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 10px;
}

.jikan-season-info > div {
  margin-bottom: 10px;
}

.jikan-data {
  margin: 0;
  padding: 0;
}

dt.jikan-data {
  font-weight: 500;
  margin-bottom: 5px;
  color: #00bcd4;
}

dd.jikan-data {
  margin-left: 0;
  margin-bottom: 10px;
}

.jikan-season-episodes h4 {
  margin-bottom: 15px;
  border-bottom: 1px solid #444;
  padding-bottom: 5px;
}

.jikan-episodes-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.jikan-episode-card {
  background-color: #2a2a2a;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
}

.jikan-episode-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.jikan-episode-card h5 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1.1em;
  color: #00bcd4;
}

.jikan-episode-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 0.9em;
  color: #aaa;
}

.jikan-episode-overview {
  margin-top: 10px;
}

.jikan-episode-overview p {
  margin-bottom: 10px;
  font-size: 0.9em;
  line-height: 1.4;
}

.play-episode-btn {
  background-color: #00bcd4;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 0.9em;
  transition: background-color 0.2s;
}

.play-episode-btn:hover {
  background-color: #00a0b7;
}

.no-streams {
  color: #888;
  font-style: italic;
  font-size: 0.9em;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .jikan-episodes-list {
    grid-template-columns: 1fr;
  }

  .jikan-season-info {
    grid-template-columns: 1fr;
  }
}
