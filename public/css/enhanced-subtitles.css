/* Enhanced Subtitle Menu Styles */
:root {
  --subtitle-bg: rgba(20, 20, 20, 0.95);
  --subtitle-border: rgba(255, 255, 255, 0.1);
  --subtitle-text: rgba(255, 255, 255, 0.9);
  --subtitle-text-secondary: rgba(255, 255, 255, 0.6);
  --subtitle-hover: rgba(255, 255, 255, 0.15);
  --subtitle-active: var(--player-primary);
  --subtitle-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  --subtitle-radius: 8px;
  --subtitle-transition: 0.2s ease;
}

/* Main subtitle menu container */
#player-subtitles-menu {
  position: absolute;
  right: 15px;
  bottom: 70px;
  background-color: var(--subtitle-bg);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border-radius: var(--subtitle-radius);
  padding: 15px;
  z-index: 5;
  display: none;
  width: 260px;
  max-height: 400px;
  box-shadow: var(--subtitle-shadow);
  border: 1px solid var(--subtitle-border);
  transition: all var(--subtitle-transition);
  overflow-x: hidden;
  overflow-y: auto;
  color: var(--subtitle-text);
  font-family: 'Roboto', 'Arial', sans-serif;
  scrollbar-width: thin;
  scrollbar-color: var(--player-primary) rgba(0, 0, 0, 0.2);
}

#player-subtitles-menu.active {
  display: block;
  animation: subtitleMenuFadeIn 0.25s ease-out;
}

@keyframes subtitleMenuFadeIn {
  from { opacity: 0; transform: translateY(10px) scale(0.98); }
  to { opacity: 1; transform: translateY(0) scale(1); }
}

/* Custom scrollbar for the menu */
#player-subtitles-menu::-webkit-scrollbar {
  width: 5px;
}

#player-subtitles-menu::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

#player-subtitles-menu::-webkit-scrollbar-thumb {
  background: var(--player-primary);
  border-radius: 10px;
}

/* Sections within the menu */
.player-settings-section {
  margin-bottom: 15px;
}

.player-settings-header {
  font-size: 15px;
  font-weight: 600;
  color: var(--subtitle-text);
  margin-bottom: 10px;
  padding-bottom: 6px;
  border-bottom: 1px solid var(--subtitle-border);
  letter-spacing: 0.5px;
}

.player-settings-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* Toggle button */
#subtitle-toggle-btn {
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 4px;
  color: var(--subtitle-text);
  padding: 8px 12px;
  text-align: center;
  cursor: pointer;
  transition: all var(--subtitle-transition);
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

#subtitle-toggle-btn:hover {
  background-color: var(--subtitle-hover);
}

#subtitle-toggle-btn.active {
  background-color: var(--subtitle-active);
  color: #000;
  font-weight: 600;
}

/* File upload section */
.player-subtitle-upload {
  margin-top: 5px;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  transition: all var(--subtitle-transition);
}

.player-subtitle-upload:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

.player-subtitle-upload label {
  display: block;
  margin-bottom: 6px;
  cursor: pointer;
  color: var(--subtitle-active);
  font-weight: 500;
  text-align: center;
  transition: color var(--subtitle-transition);
  font-size: 13px;
  letter-spacing: 0.3px;
}

.player-subtitle-upload label:hover {
  color: white;
}

.player-subtitle-upload input[type="file"] {
  width: 100%;
  margin-top: 5px;
  color: var(--subtitle-text);
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  font-size: 12px;
}

.player-subtitle-upload input[type="file"]::-webkit-file-upload-button {
  background-color: var(--subtitle-active);
  color: black;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 8px;
  font-weight: 600;
  font-size: 12px;
  transition: all var(--subtitle-transition);
}

.player-subtitle-upload input[type="file"]::-webkit-file-upload-button:hover {
  background-color: var(--player-primary-light);
}

/* URL input section */
.player-subtitle-url {
  margin-top: 5px;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  transition: all var(--subtitle-transition);
}

.player-subtitle-url:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

.player-subtitle-url label {
  display: block;
  margin-bottom: 6px;
  font-size: 13px;
  color: var(--subtitle-text);
  font-weight: 500;
  text-align: center;
  letter-spacing: 0.3px;
}

.subtitle-url-input-group {
  display: flex;
  width: 100%;
}

.subtitle-url-input-group input {
  flex: 1;
  padding: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background-color: rgba(0, 0, 0, 0.2);
  color: white;
  border-radius: 4px 0 0 4px;
  font-size: 12px;
  transition: all var(--subtitle-transition);
}

.subtitle-url-input-group input:focus {
  outline: none;
  border-color: var(--subtitle-active);
}

.subtitle-url-input-group button {
  padding: 8px 12px;
  background-color: var(--subtitle-active);
  color: black;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all var(--subtitle-transition);
}

.subtitle-url-input-group button:hover {
  background-color: var(--player-primary-light);
}

.subtitle-url-input-group button:disabled {
  background-color: rgba(255, 255, 255, 0.2);
  cursor: not-allowed;
}

/* Addic7ed search button */
.player-subtitle-addic7ed {
  margin-top: 5px;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  display: flex;
  justify-content: center;
  transition: all var(--subtitle-transition);
}

.player-subtitle-addic7ed:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

.subtitle-action-btn {
  width: 100%;
  padding: 8px 12px;
  background-color: var(--subtitle-active);
  color: black;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 600;
  transition: all var(--subtitle-transition);
  letter-spacing: 0.3px;
}

.subtitle-action-btn:hover {
  background-color: var(--player-primary-light);
}

.subtitle-action-btn:disabled {
  background-color: rgba(255, 255, 255, 0.2);
  cursor: not-allowed;
}

/* Subtitle information section */
.online-subtitles-container {
  margin-top: 10px;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  max-height: none;
  overflow: visible;
}

.subtitle-section-title {
  font-size: 13px;
  font-weight: 600;
  color: var(--subtitle-text);
  margin-bottom: 8px;
  letter-spacing: 0.3px;
}

.subtitle-info p {
  font-size: 12px;
  color: var(--subtitle-text-secondary);
  margin: 5px 0;
  line-height: 1.4;
}

/* Subtitle search results */
.subtitle-language-groups {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.subtitle-language-group {
  margin-bottom: 8px;
}

.subtitle-language-header {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  font-weight: 600;
  color: var(--subtitle-text);
  margin-bottom: 5px;
  padding-bottom: 3px;
  border-bottom: 1px solid var(--subtitle-border);
}

.subtitle-language-code {
  background-color: var(--subtitle-active);
  color: black;
  padding: 2px 5px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: bold;
}

.subtitle-language-options {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.online-subtitle-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 4px;
  color: var(--subtitle-text);
  padding: 6px 8px;
  cursor: pointer;
  transition: background-color var(--subtitle-transition);
  font-size: 12px;
  text-align: left;
}

.online-subtitle-option:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.subtitle-option-name {
  display: flex;
  align-items: center;
  gap: 5px;
}

.subtitle-option-source {
  font-size: 10px;
  color: var(--subtitle-text-secondary);
  background-color: rgba(0, 0, 0, 0.3);
  padding: 2px 5px;
  border-radius: 3px;
}

.subtitle-season-episode {
  font-size: 11px;
  color: var(--subtitle-text-secondary);
}

.subtitle-hi-icon {
  color: var(--subtitle-active);
  font-size: 10px;
}

/* Loading and error states */
.subtitle-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  color: var(--subtitle-text);
  font-size: 13px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  margin: 5px 0;
}

.subtitle-loading::before {
  content: '';
  width: 14px;
  height: 14px;
  margin-right: 8px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid var(--subtitle-active);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.subtitle-error {
  padding: 10px;
  color: #ff6b6b;
  font-size: 12px;
  background-color: rgba(255, 107, 107, 0.1);
  border-left: 3px solid #ff6b6b;
  border-radius: 4px;
  margin: 5px 0;
}

/* Subtitle customization controls */
.subtitle-customization {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
}

.subtitle-customization-label {
  font-size: 13px;
  color: var(--subtitle-text);
  margin-bottom: 4px;
  font-weight: 500;
}

.subtitle-size-controls, .subtitle-opacity-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.subtitle-control-btn {
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 4px;
  color: white;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--subtitle-transition);
  font-size: 14px;
}

.subtitle-control-btn:hover {
  background-color: var(--subtitle-hover);
}

.subtitle-value {
  font-size: 13px;
  color: var(--subtitle-text);
  font-weight: 500;
}
