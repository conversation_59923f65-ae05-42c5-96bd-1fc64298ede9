/**
 * NetStream Admin Panel Styles
 * Comprehensive styling for the enhanced admin panel
 */

/* Admin Panel Modal */
.admin-panel-modal {
  z-index: 10000;
}

.admin-panel-content {
  width: 99vw;
  height: 97vh;
  max-width: none;
  max-height: none;
  margin: 1.5vh auto;
  padding: 0;
  border-radius: 16px;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.6);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(79, 195, 247, 0.2);
  backdrop-filter: blur(20px);
}

/* Admin Panel Header */
.admin-panel-header {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  padding: 16px 24px;
  border-bottom: 1px solid rgba(79, 195, 247, 0.3);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  min-height: 60px;
}

.admin-panel-header h2 {
  color: #ffffff;
  margin: 0;
  font-size: 22px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
  letter-spacing: -0.3px;
}

.admin-panel-header h2 i {
  color: #4fc3f7;
  font-size: 28px;
  background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.admin-panel-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.admin-panel-close {
  font-size: 28px;
  color: #ffffff;
  cursor: pointer;
  transition: color 0.3s ease;
}

.admin-panel-close:hover {
  color: #ff6b6b;
}

/* Admin Panel Body with Sidebar */
.admin-panel-body {
  flex: 1;
  display: flex;
  overflow: hidden;
  position: relative;
  width: 100%;
}

/* Admin Panel Sidebar */
.admin-panel-tabs {
  background: linear-gradient(135deg, #16213e 0%, #1a1a2e 100%);
  width: 280px;
  min-width: 280px;
  max-width: 280px;
  border-right: 1px solid rgba(79, 195, 247, 0.2);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #4fc3f7 #16213e;
  box-shadow: inset -1px 0 0 rgba(79, 195, 247, 0.1);
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.admin-panel-tabs::-webkit-scrollbar {
  width: 6px;
}

.admin-panel-tabs::-webkit-scrollbar-track {
  background: #1e1e3f;
}

.admin-panel-tabs::-webkit-scrollbar-thumb {
  background: #4fc3f7;
  border-radius: 3px;
}

.tab-button {
  background: transparent;
  border: none;
  color: #b0b0b0;
  padding: 18px 24px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 15px;
  font-weight: 600;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 12px;
  border-right: 3px solid transparent;
  position: relative;
  overflow: hidden;
  width: 100%;
  text-align: left;
  justify-content: flex-start;
}

.tab-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(79, 195, 247, 0.1) 0%, rgba(41, 182, 246, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tab-button:hover::before {
  opacity: 1;
}

.tab-button:hover {
  color: #ffffff;
  transform: translateY(-1px);
}

.tab-button.active {
  background: linear-gradient(135deg, rgba(79, 195, 247, 0.15) 0%, rgba(41, 182, 246, 0.15) 100%);
  color: #4fc3f7;
  border-right-color: #4fc3f7;
  box-shadow: 0 4px 12px rgba(79, 195, 247, 0.2);
}

.tab-button i {
  font-size: 18px;
  transition: transform 0.3s ease;
}

.tab-button:hover i {
  transform: scale(1.1);
}

/* Tab Content Container */
.tab-content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
}

.tab-content {
  display: none;
  height: 100%;
  overflow-y: auto;
  padding: 40px;
  scrollbar-width: thin;
  scrollbar-color: #4fc3f7 #1a1a2e;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
  flex: 1;
  width: 100%;
  box-sizing: border-box;
}

.tab-content::-webkit-scrollbar {
  width: 8px;
}

.tab-content::-webkit-scrollbar-track {
  background: #1a1a2e;
}

.tab-content::-webkit-scrollbar-thumb {
  background: #4fc3f7;
  border-radius: 4px;
}

.tab-content.active {
  display: flex;
  flex-direction: column;
}

/* Dashboard Grid - Enhanced Grid Layout */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  grid-auto-rows: minmax(280px, auto);
  gap: 24px;
  margin-bottom: 40px;
  padding: 0;
  align-items: start;
}

/* Dashboard Card - Grid Item Styling */
.dashboard-card {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 16px;
  padding: 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(79, 195, 247, 0.2);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  display: flex;
  flex-direction: column;
  height: fit-content;
  min-height: 280px;
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(79, 195, 247, 0.05) 0%, rgba(41, 182, 246, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dashboard-card:hover::before {
  opacity: 1;
}

.dashboard-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(79, 195, 247, 0.3);
  border-color: rgba(79, 195, 247, 0.5);
}

.card-header {
  background: linear-gradient(135deg, #16213e 0%, #1a1a2e 100%);
  padding: 28px 32px;
  border-bottom: 1px solid rgba(79, 195, 247, 0.3);
  position: relative;
}

.card-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #4fc3f7 0%, #29b6f6 100%);
}

.card-header h3 {
  color: #ffffff;
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
  letter-spacing: -0.3px;
}

.card-header h3 i {
  color: #4fc3f7;
  font-size: 22px;
  background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card-body {
  padding: 24px 32px 32px 32px;
  color: #e0e0e0;
  position: relative;
  z-index: 1;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

/* Stats Container */
.stats-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #3a3a5a;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  color: #b0b0b0;
  font-weight: 500;
}

.stat-value {
  color: #4fc3f7;
  font-weight: 600;
  font-size: 16px;
}

.stat-value.success {
  color: #4caf50;
}

.stat-value.warning {
  color: #ff9800;
}

.stat-value.error {
  color: #f44336;
}

/* Loading Animation */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #b0b0b0;
  font-style: italic;
}

.loading::before {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid #3a3a5a;
  border-top: 2px solid #4fc3f7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Buttons */
.button {
  background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
  color: #ffffff;
  border: none;
  padding: 14px 28px;
  border-radius: 10px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  gap: 10px;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  min-height: 48px;
  box-sizing: border-box;
}

.button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.button:hover::before {
  left: 100%;
}

.button:hover {
  background: linear-gradient(135deg, #29b6f6 0%, #0288d1 100%);
  transform: translateY(-2px);
  box-shadow: 0 12px 28px rgba(79, 195, 247, 0.4);
}

.button.secondary {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.button.secondary:hover {
  background: linear-gradient(135deg, #495057 0%, #343a40 100%);
  box-shadow: 0 8px 20px rgba(108, 117, 125, 0.3);
}

.button.warning {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.button.warning:hover {
  background: linear-gradient(135deg, #f57c00 0%, #e65100 100%);
  box-shadow: 0 8px 20px rgba(255, 152, 0, 0.3);
}

.button.danger {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
}

.button.danger:hover {
  background: linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%);
  box-shadow: 0 8px 20px rgba(244, 67, 54, 0.3);
}

.button.primary {
  background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
}

/* Form Elements */
input[type="text"],
input[type="password"],
input[type="number"],
select,
textarea {
  background: #2a2a4a;
  border: 2px solid #3a3a5a;
  color: #ffffff;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: #4fc3f7;
  box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.1);
}

/* Checkbox Styling */
input[type="checkbox"] {
  appearance: none;
  width: 20px;
  height: 20px;
  background: #2a2a4a;
  border: 2px solid #3a3a5a;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

input[type="checkbox"]:checked {
  background: #4fc3f7;
  border-color: #4fc3f7;
}

input[type="checkbox"]:checked::before {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ffffff;
  font-weight: bold;
  font-size: 12px;
}

/* Progress Bar */
.progress-bar {
  background: #2a2a4a;
  border-radius: 10px;
  height: 8px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  background: linear-gradient(90deg, #4fc3f7 0%, #29b6f6 100%);
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Progress Modal Styles */
.progress-modal {
  z-index: 10000;
}

.progress-modal-content {
  max-width: 500px;
  width: 90%;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 16px;
  border: 1px solid rgba(79, 195, 247, 0.3);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  overflow: hidden;
}

.progress-modal-header {
  background: linear-gradient(135deg, #16213e 0%, #0f0f23 100%);
  padding: 20px 24px;
  border-bottom: 1px solid rgba(79, 195, 247, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-modal-header h3 {
  margin: 0;
  color: #4fc3f7;
  font-size: 18px;
  font-weight: 600;
}

.progress-modal-header h3 i {
  margin-right: 8px;
  color: #4fc3f7;
}

.progress-modal-close {
  color: #888;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  transition: color 0.3s ease;
}

.progress-modal-close:hover {
  color: #4fc3f7;
}

.progress-modal-body {
  padding: 32px 24px;
}

.progress-status {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24px;
}

.progress-icon {
  margin-right: 16px;
  font-size: 24px;
  color: #4fc3f7;
  min-width: 32px;
  text-align: center;
}

.progress-text {
  flex: 1;
}

.progress-message {
  margin: 0 0 8px 0;
  color: #e0e0e0;
  font-size: 16px;
  font-weight: 600;
}

.progress-description {
  margin: 0;
  color: #b0b0b0;
  font-size: 14px;
  line-height: 1.5;
}

.progress-bar-container {
  margin-top: 20px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(79, 195, 247, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #4fc3f7 0%, #29b6f6 100%);
  border-radius: 4px;
  width: 0%;
  transition: width 0.3s ease;
  animation: progressPulse 2s ease-in-out infinite;
}

@keyframes progressPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.progress-modal-footer {
  background: rgba(79, 195, 247, 0.05);
  padding: 16px 24px;
  border-top: 1px solid rgba(79, 195, 247, 0.1);
  text-align: right;
}

.close-progress-btn {
  background: rgba(79, 195, 247, 0.1);
  border: 1px solid rgba(79, 195, 247, 0.3);
  color: #4fc3f7;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-progress-btn:hover {
  background: rgba(79, 195, 247, 0.2);
  border-color: rgba(79, 195, 247, 0.5);
}

/* Compact Analytics Styles */
.compact-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  margin: 0;
}

.compact-stat {
  background: rgba(79, 195, 247, 0.05);
  border: 1px solid rgba(79, 195, 247, 0.2);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  transition: all 0.3s ease;
}

.compact-stat:hover {
  background: rgba(79, 195, 247, 0.1);
  border-color: rgba(79, 195, 247, 0.3);
  transform: translateY(-2px);
}

.compact-stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #4fc3f7;
  margin-bottom: 4px;
  line-height: 1;
}

.compact-stat-label {
  font-size: 12px;
  color: #b0b0b0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

/* Configuration Grid Styles */
.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin: 0;
}

.config-card {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border: 1px solid rgba(79, 195, 247, 0.2);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.config-card:hover {
  border-color: rgba(79, 195, 247, 0.4);
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(79, 195, 247, 0.1);
}

.config-card-header {
  background: linear-gradient(135deg, #16213e 0%, #0f0f23 100%);
  padding: 20px 24px;
  border-bottom: 1px solid rgba(79, 195, 247, 0.2);
}

.config-card-header h3 {
  margin: 0;
  color: #4fc3f7;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.config-card-header h3 i {
  margin-right: 8px;
  color: #4fc3f7;
}

.config-card-body {
  padding: 24px;
}

.config-item {
  margin-bottom: 20px;
}

.config-item:last-child {
  margin-bottom: 0;
}

.config-item label {
  display: block;
  color: #e0e0e0;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.config-item input[type="text"],
.config-item input[type="password"],
.config-item input[type="number"],
.config-item select {
  width: 100%;
  padding: 12px 16px;
  background: rgba(79, 195, 247, 0.05);
  border: 1px solid rgba(79, 195, 247, 0.2);
  border-radius: 8px;
  color: #e0e0e0;
  font-size: 14px;
  transition: all 0.3s ease;
}

.config-item input[type="text"]:focus,
.config-item input[type="password"]:focus,
.config-item input[type="number"]:focus,
.config-item select:focus {
  outline: none;
  border-color: #4fc3f7;
  background: rgba(79, 195, 247, 0.1);
  box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.1);
}

.input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.input-group input {
  flex: 1;
}

.button.compact {
  padding: 8px 16px;
  font-size: 12px;
  min-width: 80px;
}

.button.full-width {
  width: 100%;
  justify-content: center;
}

.config-item input[type="checkbox"] {
  margin-right: 8px;
  transform: scale(1.2);
}

.config-item label:has(input[type="checkbox"]) {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 0;
}

.status-indicator {
  margin-top: 8px;
  padding: 8px;
  border-radius: 6px;
  font-size: 12px;
  text-align: center;
}

.status-indicator.success {
  background: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.status-indicator.error {
  background: rgba(244, 67, 54, 0.1);
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

/* System Tab Styles */
.system-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin: 24px 0;
}

.system-card {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border: 1px solid rgba(79, 195, 247, 0.2);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.system-card:hover {
  border-color: rgba(79, 195, 247, 0.4);
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(79, 195, 247, 0.1);
}

.system-card-header {
  background: linear-gradient(135deg, #16213e 0%, #0f0f23 100%);
  padding: 20px 24px;
  border-bottom: 1px solid rgba(79, 195, 247, 0.2);
}

.system-card-header h3 {
  margin: 0;
  color: #4fc3f7;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.system-card-header h3 i {
  margin-right: 8px;
  color: #4fc3f7;
}

.system-card-body {
  padding: 24px;
}

.system-controls {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.system-controls .button {
  flex: 1;
  min-width: 150px;
}

/* Process List Styles */
.process-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.process-item {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 16px;
  padding: 12px 16px;
  background: rgba(79, 195, 247, 0.05);
  border: 1px solid rgba(79, 195, 247, 0.1);
  border-radius: 8px;
  align-items: center;
  transition: all 0.3s ease;
}

.process-item:hover {
  background: rgba(79, 195, 247, 0.1);
  border-color: rgba(79, 195, 247, 0.2);
}

.process-name {
  font-weight: 600;
  color: #e0e0e0;
}

.process-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
  font-weight: 500;
}

.process-status.success {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.process-cpu,
.process-memory {
  color: #b0b0b0;
  font-size: 14px;
  text-align: center;
}

/* Error List Styles */
.error-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.error-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.error-item.warning {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.error-item.error {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.error-icon {
  font-size: 18px;
  margin-top: 2px;
}

.error-item.warning .error-icon {
  color: #ffc107;
}

.error-item.error .error-icon {
  color: #f44336;
}

.error-content {
  flex: 1;
}

.error-message {
  color: #e0e0e0;
  font-weight: 500;
  margin-bottom: 4px;
}

.error-time {
  color: #b0b0b0;
  font-size: 12px;
}

.no-errors {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 24px;
  color: #4caf50;
  font-weight: 500;
}

.no-errors i {
  font-size: 20px;
}

/* Confirmation Modal Styles */
.confirmation-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.confirmation-modal-content {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 16px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.confirmation-modal-header {
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid rgba(79, 195, 247, 0.2);
}

.confirmation-modal-header h3 {
  margin: 0 0 16px 0;
  color: #4fc3f7;
  font-size: 20px;
  font-weight: 600;
}

.confirmation-modal-body {
  padding: 24px;
}

.confirmation-message {
  color: #e0e0e0;
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 16px 0;
}

.confirmation-details {
  color: #b0b0b0;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.confirmation-modal-footer {
  padding: 0 24px 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* Progress Modal Styles */
.progress-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.progress-modal-content {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 16px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.progress-modal-header {
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid rgba(79, 195, 247, 0.2);
}

.progress-modal-header h3 {
  margin: 0 0 16px 0;
  color: #4fc3f7;
  font-size: 20px;
  font-weight: 600;
}

.progress-modal-body {
  padding: 24px;
  text-align: center;
}

.progress-spinner {
  margin-bottom: 20px;
}

.progress-spinner i {
  font-size: 32px;
  color: #4fc3f7;
}

.progress-spinner i.success {
  color: #4caf50;
}

.progress-spinner i.error {
  color: #f44336;
}

.progress-message {
  color: #e0e0e0;
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 12px 0;
}

.progress-details {
  color: #b0b0b0;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

/* Responsive Design - Enhanced Grid Breakpoints */
@media (max-width: 1400px) {
  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 1200px) {
  .admin-panel-content {
    width: 99vw;
    height: 96vh;
    margin: 2vh auto;
  }

  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 18px;
    grid-auto-rows: minmax(260px, auto);
  }

  .tab-content {
    padding: 32px;
  }

  .card-body {
    padding: 20px 24px 24px 24px;
  }

  .card-header {
    padding: 20px 24px;
  }

  .tab-button {
    padding: 18px 28px;
  }
}

@media (max-width: 768px) {
  .admin-panel-content {
    width: 100vw;
    height: 100vh;
    margin: 0;
    border-radius: 0;
  }

  .admin-panel-header {
    padding: 20px 24px;
  }

  .admin-panel-header h2 {
    font-size: 24px;
  }

  .tab-button {
    padding: 16px 20px;
    font-size: 14px;
  }

  .tab-content {
    padding: 24px;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    grid-auto-rows: minmax(240px, auto);
  }

  .dashboard-card {
    min-height: 240px;
  }

  .button {
    padding: 12px 20px;
    font-size: 14px;
    min-height: 44px;
  }
}

/* Content Management Styles */
.content-management {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.content-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.action-group {
  background: linear-gradient(135deg, #2a2a4a 0%, #1e1e3f 100%);
  border-radius: 12px;
  padding: 25px;
  border: 1px solid #3a3a5a;
}

.action-group h3 {
  color: #4fc3f7;
  margin: 0 0 20px 0;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.search-controls input,
.search-controls select {
  flex: 1;
  min-width: 150px;
}

/* Performance Monitoring Styles */
.performance-monitoring {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.performance-controls {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.performance-card {
  background: linear-gradient(135deg, #2a2a4a 0%, #1e1e3f 100%);
  border-radius: 12px;
  padding: 25px;
  border: 1px solid #3a3a5a;
}

.performance-card h3 {
  color: #4fc3f7;
  margin: 0 0 20px 0;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.performance-charts {
  background: linear-gradient(135deg, #2a2a4a 0%, #1e1e3f 100%);
  border-radius: 12px;
  padding: 25px;
  border: 1px solid #3a3a5a;
}

.chart-container {
  text-align: center;
}

.chart-container h3 {
  color: #4fc3f7;
  margin: 0 0 20px 0;
  font-size: 18px;
}

/* Scraping Management Styles */
.scraping-management {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.scraping-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 25px;
}

.control-group {
  background: linear-gradient(135deg, #2a2a4a 0%, #1e1e3f 100%);
  border-radius: 12px;
  padding: 25px;
  border: 1px solid #3a3a5a;
}

.control-group h3 {
  color: #4fc3f7;
  margin: 0 0 20px 0;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.scraping-options,
.config-options {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  align-items: center;
}

.scraping-options select,
.config-options input,
.config-options label {
  flex: 1;
  min-width: 120px;
}

.scraping-status {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.status-card {
  background: linear-gradient(135deg, #2a2a4a 0%, #1e1e3f 100%);
  border-radius: 12px;
  padding: 25px;
  border: 1px solid #3a3a5a;
}

.status-card h3 {
  color: #4fc3f7;
  margin: 0 0 20px 0;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.status-label {
  color: #b0b0b0;
  font-weight: 500;
}

.status-value {
  color: #4fc3f7;
  font-weight: 600;
}

/* Log Container Styles */
.log-container {
  background: linear-gradient(135deg, #2a2a4a 0%, #1e1e3f 100%);
  border-radius: 12px;
  border: 1px solid #3a3a5a;
  overflow: hidden;
}

.log-controls {
  background: #1e1e3f;
  padding: 15px 20px;
  border-bottom: 1px solid #3a3a5a;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.logs {
  height: 400px;
  overflow-y: auto;
  padding: 20px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  background: #1a1a2e;
  scrollbar-width: thin;
  scrollbar-color: #4fc3f7 #1a1a2e;
}

.logs::-webkit-scrollbar {
  width: 8px;
}

.logs::-webkit-scrollbar-track {
  background: #1a1a2e;
}

.logs::-webkit-scrollbar-thumb {
  background: #4fc3f7;
  border-radius: 4px;
}

.log-entry {
  margin-bottom: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid #3a3a5a;
}

.log-entry.error {
  background: rgba(244, 67, 54, 0.1);
  border-left-color: #f44336;
  color: #ffcdd2;
}

.log-entry.warning {
  background: rgba(255, 152, 0, 0.1);
  border-left-color: #ff9800;
  color: #ffe0b2;
}

.log-entry.info {
  background: rgba(79, 195, 247, 0.1);
  border-left-color: #4fc3f7;
  color: #b3e5fc;
}

.log-entry.success {
  background: rgba(76, 175, 80, 0.1);
  border-left-color: #4caf50;
  color: #c8e6c9;
}

/* Configuration Styles */
.configuration-management {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.config-sections {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.config-section {
  background: linear-gradient(135deg, #2a2a4a 0%, #1e1e3f 100%);
  border-radius: 12px;
  padding: 25px;
  border: 1px solid #3a3a5a;
}

.config-section h3 {
  color: #4fc3f7;
  margin: 0 0 20px 0;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.config-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  color: #e0e0e0;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-group input,
.form-group select {
  max-width: 400px;
}

/* Status Indicator */
.status-indicator {
  padding: 10px 15px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  margin-top: 10px;
}

.status-indicator.active {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border: 1px solid #4caf50;
}

.status-indicator.inactive {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
  border: 1px solid #f44336;
}

/* Message Styles */
.message {
  padding: 12px 16px;
  border-radius: 8px;
  margin-top: 15px;
  font-weight: 500;
}

.message.success {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border: 1px solid #4caf50;
}

.message.error {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
  border: 1px solid #f44336;
}

.message.info {
  background: rgba(79, 195, 247, 0.2);
  color: #4fc3f7;
  border: 1px solid #4fc3f7;
}

.message.warning {
  background: rgba(255, 152, 0, 0.2);
  color: #ff9800;
  border: 1px solid #ff9800;
}

@media (max-width: 768px) {
  .admin-panel-header {
    padding: 15px 20px;
  }

  .admin-panel-header h2 {
    font-size: 20px;
  }

  .tab-button {
    padding: 12px 20px;
    font-size: 13px;
  }

  .tab-content {
    padding: 20px;
  }

  .dashboard-grid,
  .content-actions,
  .performance-grid,
  .scraping-controls,
  .scraping-status {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .search-controls,
  .scraping-options,
  .config-options {
    flex-direction: column;
  }

  .search-controls input,
  .search-controls select,
  .scraping-options select,
  .config-options input {
    min-width: auto;
  }
}

/* Search Results Header */
.search-results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 0;
  border-bottom: 1px solid rgba(79, 195, 247, 0.2);
}

.search-results-header h3 {
  color: #4fc3f7;
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
}

.results-count {
  background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
}

/* Content Grid Container */
.content-grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  margin-top: 20px;
}

/* Grid Item Styles */
.grid-item {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(79, 195, 247, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.grid-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  border-color: rgba(79, 195, 247, 0.4);
}

.grid-item-image {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.grid-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.grid-item:hover .grid-item-image img {
  transform: scale(1.05);
}

/* Grid Item Overlay */
.grid-item-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.4) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.grid-item:hover .grid-item-overlay {
  opacity: 1;
}

.grid-item-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  background: rgba(79, 195, 247, 0.9);
  color: #ffffff;
  border: none;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  backdrop-filter: blur(10px);
}

.action-btn:hover {
  background: #4fc3f7;
  transform: scale(1.1);
  box-shadow: 0 8px 20px rgba(79, 195, 247, 0.4);
}

.action-btn.edit-btn:hover {
  background: #29b6f6;
}

.action-btn.delete-btn:hover {
  background: #f44336;
}

.action-btn.view-btn:hover {
  background: #4caf50;
}

/* Type Badge */
.grid-item-type {
  position: absolute;
  top: 12px;
  right: 12px;
}

.type-badge {
  background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
  color: #ffffff;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.type-badge.movie {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.type-badge.series {
  background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
}

.type-badge.anime {
  background: linear-gradient(135deg, #e91e63 0%, #c2185b 100%);
}

.type-badge.livetv {
  background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
}

/* Grid Item Info */
.grid-item-info {
  padding: 20px;
}

.grid-item-title {
  color: #ffffff;
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.grid-item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-id {
  color: #b0b0b0;
  font-size: 12px;
  font-family: 'Courier New', monospace;
  background: rgba(79, 195, 247, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
}

.content-message {
  text-align: center;
  padding: 40px 20px;
  color: #b0b0b0;
  font-style: italic;
}

.content-message.error {
  color: #f44336;
}

/* Process List Styles */
.processes-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.process-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(79, 195, 247, 0.1);
  border-radius: 8px;
  border-left: 3px solid #4fc3f7;
}

.process-name {
  color: #ffffff;
  font-weight: 500;
  flex: 1;
}

.process-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin: 0 10px;
}

.process-status.success {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.process-status.warning {
  background: rgba(255, 152, 0, 0.2);
  color: #ff9800;
}

.process-status.error {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
}

.process-memory {
  color: #b0b0b0;
  font-size: 12px;
  min-width: 60px;
  text-align: right;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 10000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
}

.modal-content {
  background: linear-gradient(135deg, #1e1e3f 0%, #2a2a4a 100%);
  margin: 2% auto;
  padding: 0;
  border: 1px solid #3a3a5a;
  border-radius: 12px;
  width: 95%;
  max-width: 1200px;
  max-height: 96vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.modal-header {
  background: linear-gradient(135deg, #2a2a4a 0%, #1e1e3f 100%);
  padding: 20px 25px;
  border-bottom: 1px solid #3a3a5a;
  border-radius: 12px 12px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  color: #4fc3f7;
  margin: 0;
  font-size: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.modal-header .close {
  color: #b0b0b0;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  transition: color 0.3s ease;
}

.modal-header .close:hover {
  color: #ffffff;
}

.modal-body {
  padding: 25px;
}

.modal-footer {
  background: #1a1a2e;
  padding: 20px 25px;
  border-top: 1px solid #3a3a5a;
  border-radius: 0 0 12px 12px;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  color: #e0e0e0;
  font-weight: 500;
  margin-bottom: 8px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  background: #1a1a2e;
  border: 1px solid #3a3a5a;
  border-radius: 8px;
  color: #ffffff;
  font-size: 14px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #4fc3f7;
  box-shadow: 0 0 0 2px rgba(79, 195, 247, 0.2);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

/* Error States */
.error {
  color: #f44336 !important;
}

.stat-value.error {
  color: #f44336;
}

.stat-label.error {
  color: #f44336;
}

/* Loading States */
.loading {
  text-align: center;
  padding: 40px 20px;
  color: #4fc3f7;
  font-style: italic;
}

.loading::after {
  content: '';
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-left: 10px;
  border: 2px solid #4fc3f7;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
