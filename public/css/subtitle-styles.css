/* Subtitle Styles for OpenSubtitles Integration */

/* Online subtitles container */
.online-subtitles-container {
  max-height: 300px;
  overflow-y: auto;
  margin-top: 10px;
  padding: 5px;
}

/* Subtitle language groups */
.subtitle-language-groups {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.subtitle-language-group {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 10px;
}

.subtitle-language-group:last-child {
  border-bottom: none;
}

.subtitle-language-header {
  font-weight: bold;
  color: var(--player-primary);
  margin-bottom: 8px;
  font-size: 14px;
}

.subtitle-language-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Subtitle option button */
.online-subtitle-option {
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 4px;
  padding: 8px 10px;
  color: white;
  cursor: pointer;
  text-align: left;
  transition: background-color 0.2s ease;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.online-subtitle-option:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.subtitle-option-name {
  font-weight: 500;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.subtitle-option-info {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
}

.subtitle-downloads {
  color: var(--player-primary-light);
}

.subtitle-format {
  text-transform: uppercase;
  font-weight: bold;
  font-size: 10px;
}

/* Loading and error states */
.subtitle-loading {
  padding: 10px;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
}

.subtitle-error {
  padding: 10px;
  text-align: center;
  color: #ff6b6b;
  font-style: italic;
}

.subtitle-info {
  padding: 10px;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  line-height: 1.5;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  margin: 5px 0;
}

.subtitle-info p {
  margin: 5px 0;
}

.subtitle-section-title {
  font-weight: bold;
  color: var(--player-primary);
  margin-bottom: 8px;
  font-size: 14px;
  text-align: center;
}

/* Subtitle URL input styles */
.player-subtitle-url {
  margin-top: 10px;
  width: 100%;
}

.player-subtitle-url label {
  display: block;
  margin-bottom: 5px;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
}

.subtitle-url-input-group {
  display: flex;
  width: 100%;
}

.subtitle-url-input-group input {
  flex: 1;
  padding: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background-color: rgba(0, 0, 0, 0.3);
  color: white;
  border-radius: 4px 0 0 4px;
  font-size: 12px;
}

.subtitle-url-input-group input:focus {
  outline: none;
  border-color: var(--player-primary);
}

.subtitle-url-input-group button {
  padding: 8px 12px;
  background-color: var(--player-primary);
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  transition: background-color 0.2s;
}

.subtitle-url-input-group button:hover {
  background-color: var(--player-primary-light, #2980b9);
}

.subtitle-url-input-group button:disabled {
  background-color: rgba(255, 255, 255, 0.2);
  cursor: not-allowed;
}

/* Addic7ed styles */
.player-subtitle-addic7ed {
  margin-top: 10px;
  width: 100%;
  display: flex;
  justify-content: center;
}

.subtitle-action-btn {
  padding: 8px 12px;
  background-color: var(--player-primary);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  transition: background-color 0.2s;
}

.subtitle-action-btn:hover {
  background-color: var(--player-primary-light, #2980b9);
}

.subtitle-action-btn:disabled {
  background-color: rgba(255, 255, 255, 0.2);
  cursor: not-allowed;
}

.subtitle-loading {
  padding: 10px;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-style: italic;
}

.subtitle-language-groups {
  max-height: 300px;
  overflow-y: auto;
  margin: 10px 0;
}

.subtitle-language-group {
  margin-bottom: 15px;
}

.subtitle-language-header {
  font-weight: bold;
  color: var(--player-primary);
  padding: 5px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.subtitle-language-header .subtitle-language-code {
  background-color: var(--player-primary);
  color: white;
  padding: 2px 5px;
  border-radius: 3px;
  font-size: 12px;
  min-width: 30px;
  text-align: center;
}

.subtitle-language-name {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
}

.subtitle-language-options {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.online-subtitle-option {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  width: 100%;
  text-align: left;
}

.online-subtitle-option:hover {
  background-color: rgba(0, 0, 0, 0.5);
  border-color: var(--player-primary);
}

.subtitle-option-name {
  font-weight: bold;
  color: white;
  margin-bottom: 3px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.subtitle-language-code {
  background-color: var(--player-primary);
  color: white;
  padding: 2px 5px;
  border-radius: 3px;
  font-size: 12px;
  min-width: 30px;
  text-align: center;
}

.subtitle-season-episode {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  background-color: rgba(0, 0, 0, 0.3);
  padding: 2px 5px;
  border-radius: 3px;
}

.subtitle-option-source {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 2px;
}

.subtitle-option-info {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
}

.subtitle-hi-icon {
  width: 16px;
  height: 16px;
  vertical-align: middle;
}

.subtitle-success-message {
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 10px 20px;
  border-radius: 4px;
  position: absolute;
  bottom: 120px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
}

/* Scrollbar styles for the subtitles container */
.online-subtitles-container::-webkit-scrollbar {
  width: 6px;
}

.online-subtitles-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.online-subtitles-container::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.online-subtitles-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.5);
}
