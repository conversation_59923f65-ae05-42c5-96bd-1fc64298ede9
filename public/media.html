<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NetStream - Media</title>
  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg">
  <link rel="icon" type="image/png" href="/favicon.png">
  <link rel="apple-touch-icon" sizes="180x180" href="/favicon/apple-touch-icon.png">
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  <link rel="manifest" href="/site.webmanifest">
  <meta name="theme-color" content="#00bcd4">
  <!-- Stylesheets -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="/css/styles.css">
  <link rel="stylesheet" href="/css/livetv.css">
  <link rel="stylesheet" href="/css/remote-control.css">
  <link rel="stylesheet" href="/css/player.css">
  <link rel="stylesheet" href="/css/tmdb-seasons.css">
  <link rel="stylesheet" href="/css/jikan-seasons.css">
  <link rel="stylesheet" href="/css/subtitle-styles.css">
  <link rel="stylesheet" href="/css/admin-panel.css">
  <link rel="stylesheet" href="/css/media-modern.css">
  <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
</head>
<body>
  <div class="container">
    <div class="sidebar" role="navigation" aria-label="Main Navigation">
      <h2>NetStream</h2>
      <ul>
        <li><a href="/#search" data-section="search" aria-label="Search"><i class="fas fa-search"></i> Search</a></li>
        <li><a href="/#movies" data-section="movies" aria-label="Movies"><i class="fas fa-film"></i> Movies</a></li>
        <li><a href="/#series" data-section="series" aria-label="Series"><i class="fas fa-tv"></i> Series</a></li>
        <li><a href="/#anime" data-section="anime" aria-label="Anime"><i class="fas fa-frog"></i> Anime</a></li>
        <li><a href="/#livetv" data-section="livetv" aria-label="Live TV"><i class="fas fa-broadcast-tower"></i> Live TV</a></li>
      </ul>
    </div>
    <main class="content">
      <a href="#" id="return-arrow" class="return-arrow" aria-label="Return to list"><i class="fas fa-arrow-left"></i></a>

      <!-- Media Header Section -->
      <div class="media-header">
        <div class="media-poster">
          <div id="banner" class="poster-image"></div>
        </div>
        <div class="media-info">
          <!-- All content will be populated by JavaScript -->
          <div id="media-info" class="media-tmdb-info" style="display: none;">
            <!-- Will be populated by JavaScript -->
          </div>
          <div class="media-actions">
            <button class="btn-play" id="hero-play-btn">
              <i class="fas fa-play"></i>
              Play
            </button>
            <button class="btn-wishlist" id="media-wishlist-button">
              <i class="far fa-heart"></i>
              My List
            </button>
          </div>
        </div>
      </div>
      <!-- Netflix-Style Content -->
      <div class="content-wrapper">
        <!-- Episodes Section -->
        <div class="episodes-section">
          <div class="section-header">
            <h2>Episodes</h2>
            <div class="episode-controls">
              <div class="season-dropdown" id="season-dropdown" style="display: none;">
                <label for="season-select">Season:</label>
                <select id="season-select">
                  <option value="1">Season 1</option>
                </select>
              </div>
              <div class="language-filter" id="language-filter">
                <label for="language-select">Language:</label>
                <select id="language-select">
                  <option value="all">All Languages</option>
                  <option value="VOSTFR">VOSTFR</option>
                  <option value="VF">VF</option>
                </select>
              </div>
            </div>
          </div>
          <div class="episodes-container">
            <div id="episodes" class="episodes-grid"></div>
          </div>
        </div>

        <!-- Providers Section -->
        <div class="providers-section">
          <h2>Watch Now</h2>
          <div id="providers" class="providers-container"></div>
        </div>





        <!-- Hidden compatibility elements for media.js -->
        <div style="display: none;">
          <!-- Required elements that media.js expects -->
          <div id="seasons"></div>
          <div id="media-synopsis">Loading synopsis...</div>
          <h1 id="media-title">Loading...</h1>
        </div>







            <!-- Jikan Data Section (for Anime) -->
            <div class="metadata-section jikan-section">
              <div class="metadata-header">
                <h3><i class="fas fa-dragon"></i> MyAnimeList Data</h3>
                <button class="expand-toggle" id="jikan-expand-toggle"><i class="fas fa-plus"></i></button>
              </div>

              <!-- Core Jikan Data (always visible) -->
              <div class="core-metadata jikan-core">
                <div>
                  <dt class="jikan-data"><i class="fas fa-star"></i> Rating</dt>
                  <dd id="jikan-rating" class="jikan-data"></dd>
                </div>
                <div>
                  <dt class="jikan-data"><i class="fas fa-tv"></i> Type</dt>
                  <dd id="jikan-type" class="jikan-data"></dd>
                </div>
                <div>
                  <dt class="jikan-data"><i class="fas fa-hashtag"></i> Episodes</dt>
                  <dd id="jikan-episodes" class="jikan-data"></dd>
                </div>
                <div>
                  <dt class="jikan-data"><i class="fas fa-tags"></i> Genres</dt>
                  <dd id="jikan-genres" class="jikan-data"></dd>
                </div>
                <div>
                  <dt class="jikan-data"><i class="fas fa-book"></i> Synopsis</dt>
                  <dd id="jikan-synopsis" class="jikan-data"></dd>
                </div>
              </div>

              <!-- Expandable Jikan Data -->
              <div class="expandable-metadata jikan-expandable">
                <div>
                  <dt class="jikan-data"><i class="fas fa-id-card"></i> MAL ID</dt>
                  <dd id="jikan-id" class="jikan-data"></dd>
                </div>
                <div>
                  <dt class="jikan-data"><i class="fas fa-signal"></i> Status</dt>
                  <dd id="jikan-status" class="jikan-data"></dd>
                </div>
                <div>
                  <dt class="jikan-data"><i class="fas fa-building"></i> Studios</dt>
                  <dd id="jikan-studios" class="jikan-data"></dd>
                </div>
                <div>
                  <dt class="jikan-data"><i class="fas fa-calendar-alt"></i> Season</dt>
                  <dd id="jikan-season" class="jikan-data"></dd>
                </div>
                <div>
                  <dt class="jikan-data"><i class="fas fa-calendar-day"></i> Aired</dt>
                  <dd id="jikan-aired" class="jikan-data"></dd>
                </div>
                <div>
                  <dt class="jikan-data"><i class="fas fa-image"></i> Image URL</dt>
                  <dd id="jikan-image" class="jikan-data"></dd>
                </div>
              </div>
            </div>

            <!-- Jikan Seasons Data Section (for Anime) -->
            <div class="metadata-section jikan-seasons-section">
              <div class="metadata-header">
                <h3><i class="fas fa-dragon"></i> Anime Seasons</h3>
              </div>

              <div class="jikan-seasons-container">
                <div class="jikan-seasons-select-container">
                  <label for="jikan-season-select">Select Season:</label>
                  <select id="jikan-season-select"></select>
                </div>

                <div class="jikan-season-details">
                  <div class="jikan-season-info">
                    <div>
                      <dt class="jikan-data"><i class="fas fa-hashtag"></i> Season Number</dt>
                      <dd id="jikan-season-number" class="jikan-data"></dd>
                    </div>
                    <div style="display: none;">
                      <dt class="jikan-data"><i class="fas fa-calendar-alt"></i> Air Date</dt>
                      <dd id="jikan-season-air-date" class="jikan-data"></dd>
                    </div>
                    <div style="display: none;">
                      <dt class="jikan-data"><i class="fas fa-star"></i> Rating</dt>
                      <dd id="jikan-season-rating" class="jikan-data"></dd>
                    </div>
                    <div style="display: none;">
                      <dt class="jikan-data"><i class="fas fa-id-card"></i> MAL ID</dt>
                      <dd id="jikan-season-id" class="jikan-data"></dd>
                    </div>
                    <div style="display: none;">
                      <dt class="jikan-data"><i class="fas fa-book"></i> Synopsis</dt>
                      <dd id="jikan-season-synopsis" class="jikan-data"></dd>
                    </div>
                  </div>

                  <div class="jikan-season-episodes">
                    <h4>Episodes</h4>
                    <div id="jikan-season-episodes-list" class="jikan-episodes-list"></div>
                  </div>
                </div>
              </div>
            </div>
          </dl>
        </div>
        <section id="seasons" style="display: none;">
          <h3>Seasons</h3>
          <div id="seasons" class="grid"></div>
        </section>

        <!-- Legacy sections hidden for compatibility -->
        <div style="display: none;">
          <h3>Episodes</h3>
          <div id="episodes-legacy" class="grid"></div>
          <h3>Providers</h3>
          <div id="providers-legacy" class="grid"></div>
        </div>

          </div>
        </div>
      </div>
    </main>
    <div id="player-container" class="hidden">
      <div id="player-wrapper">
        <div id="player-logo">NetStream</div>
        <video id="player" playsinline></video>
        <iframe id="player-iframe" allowfullscreen></iframe>
        <!-- Player controls will be created by player.js -->
      </div>
      <button id="close-player" aria-label="Close player">&times;</button>
    </div>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
  <script src="/js/performance.js"></script>
  <script src="/js/debug-helper.js"></script>
  <script src="/js/opensubtitles-service.js"></script>
  <script src="/js/subtitle-proxy.js"></script>
  <script src="/js/player.js"></script>
  <script src="/js/player-integration.js"></script>
  <script src="/js/script.js"></script>
  <script src="/js/jikanClient.js"></script>
  <script src="/js/recentlyWatched.js"></script>
  <script src="/js/wishlist.js"></script>
  <script src="/js/media.js"></script>
  <script src="/js/media-modern.js"></script>
  <script src="/js/livetv.js"></script>
  <script src="/js/remoteControl.js"></script>
  <script src="/js/admin.js"></script>

  <script>
    // Initialize performance optimizations for media page
    document.addEventListener('DOMContentLoaded', function() {
      // Setup lazy loading for episode grids
      const grids = document.querySelectorAll('#episodes, #providers, #seasons');
      grids.forEach(grid => {
        const items = grid.querySelectorAll('.grid-item');
        items.forEach(item => {
          const img = item.querySelector('img');
          if (img && img.dataset.src) {
            performanceOptimizer.imageObserver.observe(img);
          }
        });
      });

      // Optimize GraphQL queries for media details
      if (typeof fetchMediaDetails === 'function') {
        const originalFetch = fetchMediaDetails;
        window.fetchMediaDetails = function(...args) {
          return performanceOptimizer.graphqlQuery(
            originalFetch.toString(),
            args,
            { ttl: 600000 } // 10 minutes cache
          );
        };
      }
    });
  </script>
</body>
</html>