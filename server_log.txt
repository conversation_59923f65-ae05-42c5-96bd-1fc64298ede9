2025-05-21T00:17:05.799Z [info]: [BrowserUtils] Initialized with MAX_PAGES=0, MAX_RETRY_ATTEMPTS=3, RETRY_DELAY_BASE=3000ms
Starting trending update cron job...
Trending update cron job started.
2025-05-21T00:17:05.855Z [info]: Initialized enrichment configuration: {"USE_GEMINI":true,"FETCH_SEASONS":true,"USE_ADVANCED_MATCHING":true,"TITLE_SIMILARITY_THRESHOLD":0.5,"GEMINI_RATE_LIMIT_MS":2000,"TMDB_RATE_LIMIT_MS":1500,"JIKAN_RATE_LIMIT_MS":1000,"API_RATE_LIMIT_MS":2000,"MAX_CONCURRENT_ENRICHMENTS":40}
2025-05-21T00:17:06.028Z [info]: WebSocket server initialized
(node:65547) [MONGOOSE] Warning: Duplicate schema index on {"fetchedAt":1} found. This is often due to declaring an index using both "index: true" and "schema.index()". Please remove the duplicate index definition.
(Use `node --trace-warnings ...` to show where the warning was created)
2025-05-21T00:17:06.038Z [info]: Server is running on http://localhost:3001
2025-05-21T00:17:06.038Z [info]: GraphQL endpoint ready at http://localhost:3001/graphql
2025-05-21T00:17:06.038Z [info]: WebSocket server is running on the same port
2025-05-21T00:17:06.534Z [info]: Connected to MongoDB
2025-05-21T00:17:06.540Z [info]: Initial scrape scheduled (mode: latest)
2025-05-21T00:17:06.541Z [info]: Starting scrapeAll function in latest mode
2025-05-21T00:17:06.542Z [info]: Using existing MongoDB connection in scrapeService
2025-05-21T00:17:06.544Z [info]: Page limits parsed for latest mode: {"movies":0,"series":6,"anime":0,"witv":0}
2025-05-21T00:17:06.545Z [info]: Using page limits for latest mode: {"movies":0,"series":6,"anime":0,"witv":0}
2025-05-21T00:17:06.545Z [info]: SCRAPING_ORDER: wiflix_series, anime, wiflix_movies, witv
2025-05-21T00:17:06.546Z [info]: Scrape concurrency settings: MAX_CONCURRENT_PUPPETEER_TASKS=1, MAX_CONCURRENT_AXIOS_TASKS=2
2025-05-21T00:17:06.546Z [info]: Starting Axios tasks with concurrency 2: anime, witv
2025-05-21T00:17:06.547Z [info]: Processing Axios batch 1/1: anime, witv
2025-05-21T00:17:06.547Z [info]: Skipping anime (limit 0)
2025-05-21T00:17:06.547Z [info]: Skipping WiTV (limit 0)
2025-05-21T00:17:06.548Z [info]: Axios batch completed. Batch results: 0
2025-05-21T00:17:06.548Z [info]: All Axios tasks completed. Total results: 0
2025-05-21T00:17:06.549Z [info]: Starting Puppeteer tasks with concurrency 1: wiflix_series, wiflix_movies
2025-05-21T00:17:06.549Z [info]: Processing Puppeteer batch 1/2: wiflix_series
2025-05-21T00:17:06.549Z [info]: Starting Puppeteer task: wiflix_series
2025-05-21T00:17:06.550Z [info]: Starting series scraping with limit 6
2025-05-21T00:17:06.551Z [info]: [Wiflix List] Detecting total pages using Puppeteer for https://flemmix.ws/serie-en-streaming
2025-05-21T00:17:06.552Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming
2025-05-21T00:17:06.553Z [info]: [BrowserUtils] Launching new Puppeteer browser instance...
2025-05-21T00:17:06.666Z [info]: Regular scrape worker processor set up.
2025-05-21T00:17:06.695Z [info]: Trending update worker processor set up.
2025-05-21T00:17:07.312Z [info]: [BrowserUtils] New browser instance launched.
2025-05-21T00:17:10.029Z [info]: [Wiflix List] Detected 168 total pages via link (Puppeteer)
2025-05-21T00:17:10.029Z [info]: [Wiflix Series] Scraping endpoint "serie-en-streaming" - Limit: 6, Detected: 168, Scraping: 6 pages
2025-05-21T00:17:10.029Z [info]: [Wiflix Series] Processing page 1/6 for serie-en-streaming
2025-05-21T00:17:10.031Z [info]: [Wiflix List] Starting Wiflix series list scrape for "serie-en-streaming", planning to scrape UP TO 1 pages using Puppeteer... Base URL: https://flemmix.ws
2025-05-21T00:17:10.033Z [info]: [Wiflix List] Fetching page 1 for serie-en-streaming using Puppeteer...
2025-05-21T00:17:10.034Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/
2025-05-21T00:17:12.077Z [info]: [Wiflix List] Scraped Wiflix series endpoint "serie-en-streaming" page 1 - Found & Validated: 20 items
2025-05-21T00:17:12.078Z [info]: [Wiflix List] Finished Wiflix series list scrape for endpoint "serie-en-streaming". Total unique items collected: 20
2025-05-21T00:17:12.078Z [info]: Collected 20 series from serie-en-streaming, page 1 before processing
2025-05-21T00:17:12.119Z [info]: Processing series: The Handmaid’s Tale : la servante écarlate (https://flemmix.ws/serie-en-streaming/33255-the-handmaids-tale-la-servante-ecarlate-saison-6.html)
2025-05-21T00:17:12.120Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33255-the-handmaids-tale-la-servante-ecarlate-saison-6.html...
2025-05-21T00:17:12.121Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:17:12.121Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33255-the-handmaids-tale-la-servante-ecarlate-saison-6.html
2025-05-21T00:17:15.443Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:17:15.444Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:17:15.445Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:17:15.447Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:17:15.449Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:17:15.450Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:17:15.451Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:17:15.453Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:17:15.455Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vs"
2025-05-21T00:17:15.457Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vs"
2025-05-21T00:17:15.459Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vs"
2025-05-21T00:17:15.460Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:17:15.461Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:17:15.462Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:17:15.464Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:17:15.466Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:17:15.468Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:17:15.469Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:17:15.472Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vf"
2025-05-21T00:17:15.474Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vf"
2025-05-21T00:17:15.476Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vf"
2025-05-21T00:17:15.477Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33255-the-handmaids-tale-la-servante-ecarlate-saison-6.html... | Episodes: 9, Movie Streams: 0
2025-05-21T00:17:15.480Z [info]: Gemini AI Initialized: gemini-2.0-flash-lite.
2025-05-21T00:17:15.480Z [info]: Using advanced enrichment for The Handmaid’s Tale : la servante écarlate
2025-05-21T00:17:15.984Z [info]: Using Gemini-optimized query for 'The Handmaid’s Tale : la servante écarlate': 'The Handmaid's Tale'
2025-05-21T00:17:15.986Z [info]: Searching TMDB for 'The Handmaid's Tale' (tv)
2025-05-21T00:17:16.074Z [info]: Fetching TMDB details for 'The Handmaid's Tale' (ID: 69478)
2025-05-21T00:17:16.154Z [info]: Cleaned title: The Handmaid's Tale -> The Handmaid's Tale
2025-05-21T00:17:16.154Z [info]: Enriched The Handmaid's Tale with TMDb ID: 69478
2025-05-21T00:17:16.154Z [info]: Fetching TMDB seasons data for The Handmaid's Tale (TMDB ID: 69478)
2025-05-21T00:17:16.155Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=69478
2025-05-21T00:17:16.234Z [info]: Found 7 seasons for TMDB TV series 69478.
2025-05-21T00:17:16.235Z [warn]: Missing required parameters for fetchTmdbSeason: tmdbId=69478, seasonNumber=0
2025-05-21T00:17:17.737Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/69478/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:17:19.295Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/69478/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:17:20.852Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/69478/season/3?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:17:21.700Z [info]: Starting TMDb trending data update job...
2025-05-21T00:17:21.724Z [info]: Cleared 40 previous TMDB trending data entries.
2025-05-21T00:17:21.841Z [info]: Inserted 20 trending movie items.
2025-05-21T00:17:22.403Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/69478/season/4?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:17:22.654Z [info]: Inserted 20 trending tv items.
2025-05-21T00:17:23.405Z [info]: Finished TMDb trending data update job. Total items added: 40
2025-05-21T00:17:23.960Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/69478/season/5?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:17:25.514Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/69478/season/6?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:17:27.079Z [info]: Successfully fetched details for 6 out of 7 seasons for series 69478.
2025-05-21T00:17:27.080Z [info]: Formatted 6 out of 6 seasons for DB.
2025-05-21T00:17:27.080Z [info]: Found 6 TMDB seasons for The Handmaid's Tale
2025-05-21T00:17:27.080Z [info]: Set tmdbSeason field for season 6
2025-05-21T00:17:27.081Z [info]: TMDB title similarity check: "The Handmaid’s Tale : la servante écarlate" vs "The Handmaid's Tale : La Servante écarlate" = 0.943
2025-05-21T00:17:27.081Z [info]: TMDB match verified by similarity: 0.943 >= 0.5
2025-05-21T00:17:28.082Z [info]: Seasons count changed for The Handmaid’s Tale : la servante écarlate: TMDB (0 -> 6)
2025-05-21T00:17:28.082Z [info]: Updating series (latest mode): The Handmaid’s Tale : la servante écarlate - metadata changes detected, updating database
2025-05-21T00:17:28.107Z [info]: Processing series: The Last Of Us (https://flemmix.ws/serie-en-streaming/33307-the-last-of-us-saison-2.html)
2025-05-21T00:17:28.107Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33307-the-last-of-us-saison-2.html...
2025-05-21T00:17:28.107Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:17:28.107Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33307-the-last-of-us-saison-2.html
2025-05-21T00:17:31.980Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:17:31.980Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:17:31.981Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:17:31.984Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:17:31.986Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:17:31.989Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:17:31.991Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:17:31.993Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:17:31.996Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:17:31.996Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:17:31.998Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:17:32.001Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:17:32.003Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:17:32.005Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:17:32.007Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:17:32.009Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33307-the-last-of-us-saison-2.html... | Episodes: 6, Movie Streams: 0
2025-05-21T00:17:32.009Z [info]: Using advanced enrichment for The Last Of Us
2025-05-21T00:17:32.503Z [info]: Using Gemini-optimized query for 'The Last Of Us': 'The Last of Us'
2025-05-21T00:17:32.503Z [info]: Searching TMDB for 'The Last of Us' (tv)
2025-05-21T00:17:32.559Z [info]: Fetching TMDB details for 'The Last of Us' (ID: 100088)
2025-05-21T00:17:32.604Z [info]: Cleaned title: The Last of Us -> The Last of Us
2025-05-21T00:17:32.604Z [info]: Enriched The Last of Us with TMDb ID: 100088
2025-05-21T00:17:32.604Z [info]: Fetching TMDB seasons data for The Last of Us (TMDB ID: 100088)
2025-05-21T00:17:32.605Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=100088
2025-05-21T00:17:32.651Z [info]: Found 2 seasons for TMDB TV series 100088.
2025-05-21T00:17:32.652Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/100088/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:17:34.206Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/100088/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:17:35.750Z [info]: Successfully fetched details for 2 out of 2 seasons for series 100088.
2025-05-21T00:17:35.751Z [info]: Formatted 2 out of 2 seasons for DB.
2025-05-21T00:17:35.751Z [info]: Found 2 TMDB seasons for The Last of Us
2025-05-21T00:17:35.751Z [info]: Set tmdbSeason field for season 2
2025-05-21T00:17:35.752Z [info]: TMDB title similarity check: "The Last Of Us" vs "The Last of Us" = 1.000
2025-05-21T00:17:35.752Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:17:36.752Z [info]: Seasons count changed for The Last Of Us: TMDB (0 -> 2)
2025-05-21T00:17:36.753Z [info]: Updating series (latest mode): The Last Of Us - metadata changes detected, updating database
2025-05-21T00:17:36.780Z [info]: Processing series: The Walking Dead : Dead City (https://flemmix.ws/serie-en-streaming/33497-the-walking-dead-dead-city-saison-2.html)
2025-05-21T00:17:36.780Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33497-the-walking-dead-dead-city-saison-2.html...
2025-05-21T00:17:36.781Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:17:36.781Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33497-the-walking-dead-dead-city-saison-2.html
2025-05-21T00:17:41.299Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:17:41.299Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:17:41.299Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:17:41.301Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:17:41.304Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:17:41.306Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:17:41.306Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:17:41.308Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:17:41.309Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:17:41.311Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33497-the-walking-dead-dead-city-saison-2.html... | Episodes: 3, Movie Streams: 0
2025-05-21T00:17:41.311Z [info]: Using advanced enrichment for The Walking Dead : Dead City
2025-05-21T00:17:41.718Z [info]: Using Gemini-optimized query for 'The Walking Dead : Dead City': 'The Walking Dead: Dead City'
2025-05-21T00:17:41.719Z [info]: Searching TMDB for 'The Walking Dead: Dead City' (tv)
2025-05-21T00:17:41.778Z [info]: Fetching TMDB details for 'The Walking Dead: Dead City' (ID: 194583)
2025-05-21T00:17:41.825Z [info]: Cleaned title: The Walking Dead: Dead City -> The Walking Dead: Dead City
2025-05-21T00:17:41.825Z [info]: Enriched The Walking Dead: Dead City with TMDb ID: 194583
2025-05-21T00:17:41.825Z [info]: Fetching TMDB seasons data for The Walking Dead: Dead City (TMDB ID: 194583)
2025-05-21T00:17:41.825Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=194583
2025-05-21T00:17:41.869Z [info]: Found 3 seasons for TMDB TV series 194583.
2025-05-21T00:17:41.869Z [warn]: Missing required parameters for fetchTmdbSeason: tmdbId=194583, seasonNumber=0
2025-05-21T00:17:43.369Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/194583/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:17:44.936Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/194583/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:17:46.480Z [info]: Successfully fetched details for 2 out of 3 seasons for series 194583.
2025-05-21T00:17:46.480Z [info]: Formatted 2 out of 2 seasons for DB.
2025-05-21T00:17:46.480Z [info]: Found 2 TMDB seasons for The Walking Dead: Dead City
2025-05-21T00:17:46.480Z [info]: Set tmdbSeason field for season 2
2025-05-21T00:17:46.481Z [info]: TMDB title similarity check: "The Walking Dead : Dead City" vs "The Walking Dead : Dead City" = 1.000
2025-05-21T00:17:46.481Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:17:47.482Z [info]: Seasons count changed for The Walking Dead : Dead City: TMDB (0 -> 2)
2025-05-21T00:17:47.482Z [info]: Updating series (latest mode): The Walking Dead : Dead City - metadata changes detected, updating database
2025-05-21T00:17:47.500Z [info]: Processing series: Power Book III: Raising Kanan (https://flemmix.ws/serie-en-streaming/33004-power-book-iii-raising-kanan-saison-4.html)
2025-05-21T00:17:47.500Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33004-power-book-iii-raising-kanan-saison-4.html...
2025-05-21T00:17:47.500Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:17:47.500Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33004-power-book-iii-raising-kanan-saison-4.html
2025-05-21T00:17:50.743Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:17:50.744Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:17:50.745Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:17:50.747Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:17:50.750Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:17:50.753Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:17:50.755Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:17:50.757Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:17:50.759Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vs"
2025-05-21T00:17:50.760Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vs"
2025-05-21T00:17:50.762Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vs"
2025-05-21T00:17:50.764Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vs"
2025-05-21T00:17:50.768Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:17:50.768Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:17:50.771Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:17:50.773Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:17:50.775Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:17:50.776Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:17:50.778Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:17:50.779Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vf"
2025-05-21T00:17:50.781Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vf"
2025-05-21T00:17:50.785Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vf"
2025-05-21T00:17:50.787Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vf"
2025-05-21T00:17:50.796Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33004-power-book-iii-raising-kanan-saison-4.html... | Episodes: 10, Movie Streams: 0
2025-05-21T00:17:50.796Z [info]: Using advanced enrichment for Power Book III: Raising Kanan
2025-05-21T00:17:51.254Z [info]: Using Gemini-optimized query for 'Power Book III: Raising Kanan': 'Power Book III: Raising Kanan'
2025-05-21T00:17:51.254Z [info]: Searching TMDB for 'Power Book III: Raising Kanan' (tv)
2025-05-21T00:17:51.323Z [info]: Fetching TMDB details for 'Power Book III: Raising Kanan' (ID: 124394)
2025-05-21T00:17:51.394Z [info]: Cleaned title: Power Book III: Raising Kanan -> Power Book III: Raising Kanan
2025-05-21T00:17:51.394Z [info]: Enriched Power Book III: Raising Kanan with TMDb ID: 124394
2025-05-21T00:17:51.395Z [info]: Fetching TMDB seasons data for Power Book III: Raising Kanan (TMDB ID: 124394)
2025-05-21T00:17:51.395Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=124394
2025-05-21T00:17:51.483Z [info]: Found 4 seasons for TMDB TV series 124394.
2025-05-21T00:17:51.483Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/124394/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:17:53.044Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/124394/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:17:54.586Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/124394/season/3?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:17:56.130Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/124394/season/4?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:17:57.681Z [info]: Successfully fetched details for 4 out of 4 seasons for series 124394.
2025-05-21T00:17:57.682Z [info]: Formatted 4 out of 4 seasons for DB.
2025-05-21T00:17:57.682Z [info]: Found 4 TMDB seasons for Power Book III: Raising Kanan
2025-05-21T00:17:57.682Z [info]: Set tmdbSeason field for season 4
2025-05-21T00:17:57.682Z [info]: TMDB title similarity check: "Power Book III: Raising Kanan" vs "Power Book III: Raising Kanan" = 1.000
2025-05-21T00:17:57.682Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:17:58.683Z [info]: Seasons count changed for Power Book III: Raising Kanan: TMDB (0 -> 4)
2025-05-21T00:17:58.683Z [info]: Updating series (latest mode): Power Book III: Raising Kanan - metadata changes detected, updating database
2025-05-21T00:17:58.708Z [info]: Processing series: Andor (https://flemmix.ws/serie-en-streaming/33380-andor-saison-2.html)
2025-05-21T00:17:58.708Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33380-andor-saison-2.html...
2025-05-21T00:17:58.708Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:17:58.708Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33380-andor-saison-2.html
2025-05-21T00:18:01.690Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:18:01.690Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:18:01.692Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:18:01.695Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:18:01.697Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:18:01.699Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:18:01.703Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:18:01.706Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:18:01.709Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vs"
2025-05-21T00:18:01.711Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vs"
2025-05-21T00:18:01.713Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vs"
2025-05-21T00:18:01.715Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vs"
2025-05-21T00:18:01.717Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 11" with rel="ep11vs"
2025-05-21T00:18:01.719Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 12" with rel="ep12vs"
2025-05-21T00:18:01.721Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:18:01.722Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:18:01.725Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:18:01.727Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:18:01.729Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:18:01.730Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:18:01.732Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:18:01.734Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vf"
2025-05-21T00:18:01.736Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vf"
2025-05-21T00:18:01.738Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vf"
2025-05-21T00:18:01.741Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vf"
2025-05-21T00:18:01.743Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 11" with rel="ep11vf"
2025-05-21T00:18:01.745Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 12" with rel="ep12vf"
2025-05-21T00:18:01.747Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33380-andor-saison-2.html... | Episodes: 12, Movie Streams: 0
2025-05-21T00:18:01.748Z [info]: Using advanced enrichment for Andor
2025-05-21T00:18:02.206Z [info]: Using Gemini-optimized query for 'Andor': 'Andor'
2025-05-21T00:18:02.207Z [info]: Searching TMDB for 'Andor' (tv)
2025-05-21T00:18:02.266Z [info]: Fetching TMDB details for 'Andor' (ID: 83867)
2025-05-21T00:18:02.311Z [info]: Cleaned title: Andor -> Andor
2025-05-21T00:18:02.311Z [info]: Enriched Andor with TMDb ID: 83867
2025-05-21T00:18:02.311Z [info]: Fetching TMDB seasons data for Andor (TMDB ID: 83867)
2025-05-21T00:18:02.311Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=83867
2025-05-21T00:18:02.360Z [info]: Found 3 seasons for TMDB TV series 83867.
2025-05-21T00:18:02.360Z [warn]: Missing required parameters for fetchTmdbSeason: tmdbId=83867, seasonNumber=0
2025-05-21T00:18:03.860Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/83867/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:18:05.407Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/83867/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:18:06.962Z [info]: Successfully fetched details for 2 out of 3 seasons for series 83867.
2025-05-21T00:18:06.963Z [info]: Formatted 2 out of 2 seasons for DB.
2025-05-21T00:18:06.963Z [info]: Found 2 TMDB seasons for Andor
2025-05-21T00:18:06.963Z [info]: Set tmdbSeason field for season 2
2025-05-21T00:18:06.963Z [info]: TMDB title similarity check: "Andor" vs "Star Wars : Andor" = 0.471
2025-05-21T00:18:06.964Z [info]: Asking Gemini for final verdict: Does the title "Andor" likely refer to the same series as the TMDB entry with ID 83867 which has a title of "Star Wars : Andor"? Consider potential translations or variations. Respond with ONLY 'YES' or 'NO'.
2025-05-21T00:18:07.340Z [info]: Gemini Verdict Response: "YES"
2025-05-21T00:18:07.341Z [info]: TMDB match verified by Gemini: true
2025-05-21T00:18:08.342Z [info]: Seasons count changed for Andor: TMDB (0 -> 2)
2025-05-21T00:18:08.342Z [info]: Updating series (latest mode): Andor - metadata changes detected, updating database
2025-05-21T00:18:08.360Z [info]: Processing series: HPI (https://flemmix.ws/serie-en-streaming/33545-hpi-saison-5.html)
2025-05-21T00:18:08.361Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33545-hpi-saison-5.html...
2025-05-21T00:18:08.361Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:18:08.361Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33545-hpi-saison-5.html
2025-05-21T00:18:12.918Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:18:12.919Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:18:12.919Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:18:12.920Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:18:12.921Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:18:12.924Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33545-hpi-saison-5.html... | Episodes: 2, Movie Streams: 0
2025-05-21T00:18:12.924Z [info]: Using advanced enrichment for HPI
2025-05-21T00:18:13.305Z [info]: Using Gemini-optimized query for 'HPI': 'HPI'
2025-05-21T00:18:13.305Z [info]: Searching TMDB for 'HPI' (tv)
2025-05-21T00:18:13.350Z [info]: Fetching TMDB details for 'HPI' (ID: 112738)
2025-05-21T00:18:13.398Z [info]: Cleaned title: HPI -> HPI
2025-05-21T00:18:13.398Z [info]: Enriched HPI with TMDb ID: 112738
2025-05-21T00:18:13.398Z [info]: Fetching TMDB seasons data for HPI (TMDB ID: 112738)
2025-05-21T00:18:13.398Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=112738
2025-05-21T00:18:13.439Z [info]: Found 5 seasons for TMDB TV series 112738.
2025-05-21T00:18:13.439Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/112738/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:18:14.982Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/112738/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:18:16.546Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/112738/season/3?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:18:18.092Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/112738/season/4?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:18:19.646Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/112738/season/5?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:18:21.213Z [info]: Successfully fetched details for 5 out of 5 seasons for series 112738.
2025-05-21T00:18:21.214Z [info]: Formatted 5 out of 5 seasons for DB.
2025-05-21T00:18:21.214Z [info]: Found 5 TMDB seasons for HPI
2025-05-21T00:18:21.214Z [info]: Set tmdbSeason field for season 5
2025-05-21T00:18:21.215Z [info]: TMDB title similarity check: "HPI" vs "HPI : Haut Potentiel Intellectuel" = 0.133
2025-05-21T00:18:21.215Z [info]: Asking Gemini for final verdict: Does the title "HPI" likely refer to the same series as the TMDB entry with ID 112738 which has a title of "HPI : Haut Potentiel Intellectuel"? Consider potential translations or variations. Respond with ONLY 'YES' or 'NO'.
2025-05-21T00:18:21.564Z [info]: Gemini Verdict Response: "YES"
2025-05-21T00:18:21.564Z [info]: TMDB match verified by Gemini: true
2025-05-21T00:18:22.566Z [info]: Seasons count changed for HPI: TMDB (0 -> 5)
2025-05-21T00:18:22.566Z [info]: Updating series (latest mode): HPI - metadata changes detected, updating database
2025-05-21T00:18:22.584Z [info]: Processing series: Astérix et Obélix : le Combat des Chefs (https://flemmix.ws/serie-en-streaming/33451-asterix-et-obelix-le-combat-des-chefs-saison-1.html)
2025-05-21T00:18:22.584Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33451-asterix-et-obelix-le-combat-des-chefs-saison-1.html...
2025-05-21T00:18:22.584Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:18:22.585Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33451-asterix-et-obelix-le-combat-des-chefs-saison-1.html
2025-05-21T00:18:25.854Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:18:25.855Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:18:25.855Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:18:25.855Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:18:25.857Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:18:25.859Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:18:25.862Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:18:25.864Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:18:25.866Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33451-asterix-et-obelix-le-combat-des-chefs-saison-1.html... | Episodes: 5, Movie Streams: 0
2025-05-21T00:18:25.867Z [info]: Using advanced enrichment for Astérix et Obélix : le Combat des Chefs
2025-05-21T00:18:26.278Z [info]: Using Gemini-optimized query for 'Astérix et Obélix : le Combat des Chefs': 'Astérix et Obélix'
2025-05-21T00:18:26.278Z [info]: Searching TMDB for 'Asterix et Obelix' (tv)
2025-05-21T00:18:26.322Z [info]: Fetching TMDB details for 'Asterix et Obelix' (ID: 122781)
2025-05-21T00:18:26.361Z [info]: Cleaned title: Astérix et Obélix -> Asterix et Obelix
2025-05-21T00:18:26.361Z [info]: Enriched Asterix et Obelix with TMDb ID: 122781
2025-05-21T00:18:26.362Z [info]: Fetching TMDB seasons data for Asterix et Obelix (TMDB ID: 122781)
2025-05-21T00:18:26.362Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=122781
2025-05-21T00:18:26.403Z [info]: Found 1 seasons for TMDB TV series 122781.
2025-05-21T00:18:26.404Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/122781/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:18:27.948Z [info]: Successfully fetched details for 1 out of 1 seasons for series 122781.
2025-05-21T00:18:27.948Z [info]: Formatted 1 out of 1 seasons for DB.
2025-05-21T00:18:27.949Z [info]: Found 1 TMDB seasons for Asterix et Obelix
2025-05-21T00:18:27.949Z [info]: Set tmdbSeason field for season 1
2025-05-21T00:18:27.949Z [info]: TMDB title similarity check: "Astérix et Obélix : le Combat des Chefs" vs "Astérix & Obélix : Le Combat des chefs" = 0.918
2025-05-21T00:18:27.949Z [info]: TMDB match verified by similarity: 0.918 >= 0.5
2025-05-21T00:18:28.951Z [info]: Seasons count changed for Astérix et Obélix : le Combat des Chefs: TMDB (0 -> 1)
2025-05-21T00:18:28.951Z [info]: Updating series (latest mode): Astérix et Obélix : le Combat des Chefs - metadata changes detected, updating database
2025-05-21T00:18:28.974Z [info]: Processing series: You (2018) (https://flemmix.ws/serie-en-streaming/33398-you-saison-5.html)
2025-05-21T00:18:28.974Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33398-you-saison-5.html...
2025-05-21T00:18:28.974Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:18:28.975Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33398-you-saison-5.html
2025-05-21T00:18:32.417Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:18:32.417Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:18:32.418Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:18:32.420Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:18:32.422Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:18:32.423Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:18:32.425Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:18:32.426Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:18:32.427Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vs"
2025-05-21T00:18:32.428Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vs"
2025-05-21T00:18:32.430Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vs"
2025-05-21T00:18:32.432Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vs"
2025-05-21T00:18:32.434Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:18:32.435Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:18:32.437Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:18:32.439Z [warn]: [Wiflix Detail Final] Invalid streaming URL: "fttps://tipfly.xyz/em-476911-fb80snpm402s"
2025-05-21T00:18:32.439Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:18:32.440Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:18:32.442Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:18:32.444Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:18:32.445Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vf"
2025-05-21T00:18:32.446Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vf"
2025-05-21T00:18:32.447Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vf"
2025-05-21T00:18:32.450Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vf"
2025-05-21T00:18:32.452Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33398-you-saison-5.html... | Episodes: 10, Movie Streams: 0
2025-05-21T00:18:32.453Z [info]: Using advanced enrichment for You (2018)
2025-05-21T00:18:32.859Z [info]: Using Gemini-optimized query for 'You (2018)': 'You'
2025-05-21T00:18:32.859Z [info]: Searching TMDB for 'You' (tv)
2025-05-21T00:18:32.900Z [info]: Fetching TMDB details for 'You' (ID: 78191)
2025-05-21T00:18:32.940Z [info]: Cleaned title: You -> You
2025-05-21T00:18:32.941Z [info]: Enriched You with TMDb ID: 78191
2025-05-21T00:18:32.941Z [info]: Fetching TMDB seasons data for You (TMDB ID: 78191)
2025-05-21T00:18:32.941Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=78191
2025-05-21T00:18:32.983Z [info]: Found 5 seasons for TMDB TV series 78191.
2025-05-21T00:18:32.983Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/78191/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:18:34.525Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/78191/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:18:36.068Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/78191/season/3?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:18:37.616Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/78191/season/4?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:18:39.160Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/78191/season/5?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:18:40.711Z [info]: Successfully fetched details for 5 out of 5 seasons for series 78191.
2025-05-21T00:18:40.712Z [info]: Formatted 5 out of 5 seasons for DB.
2025-05-21T00:18:40.712Z [info]: Found 5 TMDB seasons for You
2025-05-21T00:18:40.712Z [info]: Set tmdbSeason field for season 5
2025-05-21T00:18:40.712Z [info]: TMDB title similarity check: "You (2018)" vs "You" = 0.400
2025-05-21T00:18:40.712Z [info]: Asking Gemini for final verdict: Does the title "You (2018)" likely refer to the same series as the TMDB entry with ID 78191 which has a title of "You"? Consider potential translations or variations. Respond with ONLY 'YES' or 'NO'.
2025-05-21T00:18:41.099Z [info]: Gemini Verdict Response: "YES"
2025-05-21T00:18:41.099Z [info]: TMDB match verified by Gemini: true
2025-05-21T00:18:42.100Z [info]: Seasons count changed for You (2018): TMDB (0 -> 5)
2025-05-21T00:18:42.100Z [info]: Updating series (latest mode): You (2018) - metadata changes detected, updating database
2025-05-21T00:18:42.117Z [info]: Processing series: Daredevil: Born Again (https://flemmix.ws/serie-en-streaming/32957-daredevil-born-again.html)
2025-05-21T00:18:42.118Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/32957-daredevil-born-again.html...
2025-05-21T00:18:42.118Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:18:42.118Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/32957-daredevil-born-again.html
2025-05-21T00:18:45.710Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:18:45.710Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:18:45.711Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:18:45.713Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:18:45.717Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:18:45.721Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:18:45.723Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:18:45.725Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:18:45.727Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vs"
2025-05-21T00:18:45.729Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vs"
2025-05-21T00:18:45.731Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vs"
2025-05-21T00:18:45.735Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:18:45.736Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:18:45.738Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:18:45.740Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:18:45.742Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:18:45.744Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:18:45.746Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:18:45.748Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vf"
2025-05-21T00:18:45.751Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vf"
2025-05-21T00:18:45.753Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vf"
2025-05-21T00:18:45.756Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/32957-daredevil-born-again.html... | Episodes: 9, Movie Streams: 0
2025-05-21T00:18:45.756Z [info]: Using advanced enrichment for Daredevil: Born Again
2025-05-21T00:18:46.216Z [info]: Using Gemini-optimized query for 'Daredevil: Born Again': 'Daredevil Born Again'
2025-05-21T00:18:46.217Z [info]: Searching TMDB for 'Daredevil Born Again' (tv)
2025-05-21T00:18:46.272Z [info]: Fetching TMDB details for 'Daredevil Born Again' (ID: 202555)
2025-05-21T00:18:46.315Z [info]: Cleaned title: Daredevil Born Again -> Daredevil Born Again
2025-05-21T00:18:46.315Z [info]: Enriched Daredevil Born Again with TMDb ID: 202555
2025-05-21T00:18:46.315Z [info]: Fetching TMDB seasons data for Daredevil Born Again (TMDB ID: 202555)
2025-05-21T00:18:46.315Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=202555
2025-05-21T00:18:46.357Z [info]: Found 1 seasons for TMDB TV series 202555.
2025-05-21T00:18:46.357Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/202555/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:18:47.908Z [info]: Successfully fetched details for 1 out of 1 seasons for series 202555.
2025-05-21T00:18:47.909Z [info]: Formatted 1 out of 1 seasons for DB.
2025-05-21T00:18:47.909Z [info]: Found 1 TMDB seasons for Daredevil Born Again
2025-05-21T00:18:47.909Z [info]: Set tmdbSeason field for season 1
2025-05-21T00:18:47.909Z [info]: TMDB title similarity check: "Daredevil: Born Again" vs "Daredevil : Born Again" = 1.000
2025-05-21T00:18:47.909Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:18:48.910Z [info]: Seasons count changed for Daredevil: Born Again: TMDB (0 -> 1)
2025-05-21T00:18:48.910Z [info]: Updating series (latest mode): Daredevil: Born Again - metadata changes detected, updating database
2025-05-21T00:18:48.925Z [info]: Processing series: Black Mirror (https://flemmix.ws/serie-en-streaming/33278-black-mirror-saison-7.html)
2025-05-21T00:18:48.925Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33278-black-mirror-saison-7.html...
2025-05-21T00:18:48.925Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:18:48.925Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33278-black-mirror-saison-7.html
2025-05-21T00:18:52.104Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:18:52.105Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:18:52.108Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:18:52.112Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:18:52.114Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:18:52.117Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:18:52.118Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:18:52.126Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:18:52.129Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:18:52.129Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:18:52.132Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:18:52.134Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:18:52.137Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:18:52.142Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:18:52.144Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:18:52.146Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33278-black-mirror-saison-7.html... | Episodes: 6, Movie Streams: 0
2025-05-21T00:18:52.146Z [info]: Using advanced enrichment for Black Mirror
2025-05-21T00:18:52.537Z [info]: Using Gemini-optimized query for 'Black Mirror': 'Black Mirror'
2025-05-21T00:18:52.538Z [info]: Searching TMDB for 'Black Mirror' (tv)
2025-05-21T00:18:52.582Z [info]: Fetching TMDB details for 'Black Mirror' (ID: 42009)
2025-05-21T00:18:52.634Z [info]: Cleaned title: Black Mirror -> Black Mirror
2025-05-21T00:18:52.635Z [info]: Enriched Black Mirror with TMDb ID: 42009
2025-05-21T00:18:52.635Z [info]: Fetching TMDB seasons data for Black Mirror (TMDB ID: 42009)
2025-05-21T00:18:52.636Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=42009
2025-05-21T00:18:52.697Z [info]: Found 8 seasons for TMDB TV series 42009.
2025-05-21T00:18:52.697Z [warn]: Missing required parameters for fetchTmdbSeason: tmdbId=42009, seasonNumber=0
2025-05-21T00:18:54.197Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/42009/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:18:55.741Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/42009/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:18:57.300Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/42009/season/3?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:18:58.849Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/42009/season/4?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:00.400Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/42009/season/5?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:01.951Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/42009/season/6?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:03.502Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/42009/season/7?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:05.065Z [info]: Successfully fetched details for 7 out of 8 seasons for series 42009.
2025-05-21T00:19:05.066Z [info]: Formatted 7 out of 7 seasons for DB.
2025-05-21T00:19:05.066Z [info]: Found 7 TMDB seasons for Black Mirror
2025-05-21T00:19:05.066Z [info]: Set tmdbSeason field for season 7
2025-05-21T00:19:05.066Z [info]: TMDB title similarity check: "Black Mirror" vs "Black Mirror" = 1.000
2025-05-21T00:19:05.066Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:19:06.067Z [info]: Seasons count changed for Black Mirror: TMDB (0 -> 7)
2025-05-21T00:19:06.068Z [info]: Updating series (latest mode): Black Mirror - metadata changes detected, updating database
2025-05-21T00:19:06.103Z [info]: Processing series: Ici Tout Commence (https://flemmix.ws/serie-en-streaming/33504-ici-tout-commence-saison-9-1170.html)
2025-05-21T00:19:06.103Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33504-ici-tout-commence-saison-9-1170.html...
2025-05-21T00:19:06.104Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:19:06.104Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33504-ici-tout-commence-saison-9-1170.html
2025-05-21T00:19:09.308Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:19:09.308Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:19:09.309Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:19:09.315Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:19:09.344Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:19:09.347Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:19:09.350Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:19:09.353Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:19:09.356Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:19:09.359Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vf"
2025-05-21T00:19:09.363Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vf"
2025-05-21T00:19:09.366Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vf"
2025-05-21T00:19:09.371Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vf"
2025-05-21T00:19:09.374Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 11" with rel="ep11vf"
2025-05-21T00:19:09.377Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 12" with rel="ep12vf"
2025-05-21T00:19:09.380Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33504-ici-tout-commence-saison-9-1170.html... | Episodes: 12, Movie Streams: 0
2025-05-21T00:19:09.381Z [info]: Using advanced enrichment for Ici Tout Commence
2025-05-21T00:19:09.820Z [info]: Using Gemini-optimized query for 'Ici Tout Commence': 'Ici Tout Commence'
2025-05-21T00:19:09.821Z [info]: Searching TMDB for 'Ici Tout Commence' (tv)
2025-05-21T00:19:09.908Z [info]: Fetching TMDB details for 'Ici Tout Commence' (ID: 112470)
2025-05-21T00:19:09.973Z [info]: Cleaned title: Ici Tout Commence -> Ici Tout Commence
2025-05-21T00:19:09.974Z [info]: Enriched Ici Tout Commence with TMDb ID: 112470
2025-05-21T00:19:09.974Z [info]: Fetching TMDB seasons data for Ici Tout Commence (TMDB ID: 112470)
2025-05-21T00:19:09.974Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=112470
2025-05-21T00:19:10.038Z [info]: Found 5 seasons for TMDB TV series 112470.
2025-05-21T00:19:10.038Z [warn]: Missing required parameters for fetchTmdbSeason: tmdbId=112470, seasonNumber=0
2025-05-21T00:19:11.538Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/112470/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:13.264Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/112470/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:14.817Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/112470/season/3?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:16.370Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/112470/season/4?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:17.923Z [info]: Successfully fetched details for 4 out of 5 seasons for series 112470.
2025-05-21T00:19:17.923Z [info]: Formatted 4 out of 4 seasons for DB.
2025-05-21T00:19:17.923Z [info]: Found 4 TMDB seasons for Ici Tout Commence
2025-05-21T00:19:17.924Z [info]: TMDB title similarity check: "Ici Tout Commence" vs "Ici tout commence" = 1.000
2025-05-21T00:19:17.924Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:19:18.925Z [info]: Seasons count changed for Ici Tout Commence: TMDB (0 -> 4)
2025-05-21T00:19:18.925Z [info]: Updating series (latest mode): Ici Tout Commence - metadata changes detected, updating database
2025-05-21T00:19:18.956Z [info]: Processing series: À l'épreuve du diable (https://flemmix.ws/serie-en-streaming/33515-a-lepreuve-du-diable-saison-2.html)
2025-05-21T00:19:18.956Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33515-a-lepreuve-du-diable-saison-2.html...
2025-05-21T00:19:18.956Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:19:18.956Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33515-a-lepreuve-du-diable-saison-2.html
2025-05-21T00:19:21.996Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:19:21.997Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:19:21.998Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:19:22.002Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:19:22.004Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:19:22.006Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:19:22.008Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:19:22.010Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:19:22.013Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vs"
2025-05-21T00:19:22.015Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vs"
2025-05-21T00:19:22.017Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vs"
2025-05-21T00:19:22.021Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vs"
2025-05-21T00:19:22.023Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 11" with rel="ep11vs"
2025-05-21T00:19:22.025Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 12" with rel="ep12vs"
2025-05-21T00:19:22.026Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:19:22.027Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:19:22.028Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:19:22.030Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:19:22.032Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:19:22.033Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:19:22.035Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:19:22.037Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vf"
2025-05-21T00:19:22.039Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vf"
2025-05-21T00:19:22.041Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vf"
2025-05-21T00:19:22.042Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vf"
2025-05-21T00:19:22.043Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 11" with rel="ep11vf"
2025-05-21T00:19:22.045Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 12" with rel="ep12vf"
2025-05-21T00:19:22.047Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33515-a-lepreuve-du-diable-saison-2.html... | Episodes: 12, Movie Streams: 0
2025-05-21T00:19:22.047Z [info]: Using advanced enrichment for À l'épreuve du diable
2025-05-21T00:19:22.512Z [info]: Using Gemini-optimized query for 'À l'épreuve du diable': 'À l'épreuve du diable'
2025-05-21T00:19:22.512Z [info]: Searching TMDB for 'A l'epreuve du diable' (tv)
2025-05-21T00:19:22.661Z [info]: Fetching TMDB details for 'A l'epreuve du diable' (ID: 214582)
2025-05-21T00:19:22.704Z [info]: Cleaned title: À l'épreuve du diable -> A l'epreuve du diable
2025-05-21T00:19:22.704Z [info]: Enriched A l'epreuve du diable with TMDb ID: 214582
2025-05-21T00:19:22.705Z [info]: Fetching TMDB seasons data for A l'epreuve du diable (TMDB ID: 214582)
2025-05-21T00:19:22.705Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=214582
2025-05-21T00:19:22.752Z [info]: Found 2 seasons for TMDB TV series 214582.
2025-05-21T00:19:22.752Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/214582/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:24.305Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/214582/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:25.948Z [info]: Successfully fetched details for 2 out of 2 seasons for series 214582.
2025-05-21T00:19:25.948Z [info]: Formatted 2 out of 2 seasons for DB.
2025-05-21T00:19:25.948Z [info]: Found 2 TMDB seasons for A l'epreuve du diable
2025-05-21T00:19:25.948Z [info]: Set tmdbSeason field for season 2
2025-05-21T00:19:25.948Z [info]: TMDB title similarity check: "À l'épreuve du diable" vs "À l'épreuve du diable" = 1.000
2025-05-21T00:19:25.949Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:19:26.950Z [info]: Seasons count changed for À l'épreuve du diable: TMDB (0 -> 2)
2025-05-21T00:19:26.950Z [info]: Updating series (latest mode): À l'épreuve du diable - metadata changes detected, updating database
2025-05-21T00:19:26.968Z [info]: Processing series: Alaska : la ruée vers l'or (https://flemmix.ws/serie-en-streaming/32507-alaska-la-ruee-vers-lor-saison-15.html)
2025-05-21T00:19:26.968Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/32507-alaska-la-ruee-vers-lor-saison-15.html...
2025-05-21T00:19:26.968Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:19:26.968Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/32507-alaska-la-ruee-vers-lor-saison-15.html
2025-05-21T00:19:30.756Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:19:30.756Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:19:30.757Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:19:30.758Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:19:30.760Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:19:30.762Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:19:30.763Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:19:30.765Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:19:30.766Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:19:30.767Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vf"
2025-05-21T00:19:30.769Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vf"
2025-05-21T00:19:30.770Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vf"
2025-05-21T00:19:30.772Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vf"
2025-05-21T00:19:30.773Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 11" with rel="ep11vf"
2025-05-21T00:19:30.775Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 12" with rel="ep12vf"
2025-05-21T00:19:30.777Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 13" with rel="ep13vf"
2025-05-21T00:19:30.778Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 14" with rel="ep14vf"
2025-05-21T00:19:30.780Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 15" with rel="ep15vf"
2025-05-21T00:19:30.781Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 16" with rel="ep16vf"
2025-05-21T00:19:30.783Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 17" with rel="ep17vf"
2025-05-21T00:19:30.784Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 18" with rel="ep18vf"
2025-05-21T00:19:30.786Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 19" with rel="ep19vf"
2025-05-21T00:19:30.787Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/32507-alaska-la-ruee-vers-lor-saison-15.html... | Episodes: 19, Movie Streams: 0
2025-05-21T00:19:30.788Z [info]: Using advanced enrichment for Alaska : la ruée vers l'or
2025-05-21T00:19:31.222Z [info]: Using Gemini-optimized query for 'Alaska : la ruée vers l'or': 'Alaska: Gold Rush'
2025-05-21T00:19:31.223Z [info]: Searching TMDB for 'Alaska: Gold Rush' (tv)
2025-05-21T00:19:31.289Z [info]: Fetching TMDB details for 'Alaska: Gold Rush' (ID: 34634)
2025-05-21T00:19:31.340Z [info]: Cleaned title: Alaska: Gold Rush -> Alaska: Gold Rush
2025-05-21T00:19:31.340Z [info]: Enriched Alaska: Gold Rush with TMDb ID: 34634
2025-05-21T00:19:31.340Z [info]: Fetching TMDB seasons data for Alaska: Gold Rush (TMDB ID: 34634)
2025-05-21T00:19:31.340Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=34634
2025-05-21T00:19:31.381Z [info]: Found 16 seasons for TMDB TV series 34634.
2025-05-21T00:19:31.382Z [warn]: Missing required parameters for fetchTmdbSeason: tmdbId=34634, seasonNumber=0
2025-05-21T00:19:32.882Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/34634/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:34.423Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/34634/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:35.972Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/34634/season/3?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:37.512Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/34634/season/4?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:39.068Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/34634/season/5?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:40.613Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/34634/season/6?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:42.158Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/34634/season/7?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:43.798Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/34634/season/8?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:45.447Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/34634/season/9?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:47.084Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/34634/season/10?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:48.724Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/34634/season/11?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:50.277Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/34634/season/12?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:51.868Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/34634/season/13?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:53.467Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/34634/season/14?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:55.107Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/34634/season/15?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:19:56.664Z [info]: Successfully fetched details for 15 out of 16 seasons for series 34634.
2025-05-21T00:19:56.665Z [info]: Formatted 15 out of 15 seasons for DB.
2025-05-21T00:19:56.665Z [info]: Found 15 TMDB seasons for Alaska: Gold Rush
2025-05-21T00:19:56.665Z [info]: Set tmdbSeason field for season 15
2025-05-21T00:19:56.666Z [info]: TMDB title similarity check: "Alaska : la ruée vers l'or" vs "Alaska : La ruée vers l'or" = 1.000
2025-05-21T00:19:56.666Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:19:57.669Z [info]: Seasons count changed for Alaska : la ruée vers l'or: TMDB (0 -> 15)
2025-05-21T00:19:57.670Z [info]: Updating series (latest mode): Alaska : la ruée vers l'or - metadata changes detected, updating database
2025-05-21T00:19:57.699Z [info]: Processing series: Motorheads (https://flemmix.ws/serie-en-streaming/33603-motorheads-saison-1.html)
2025-05-21T00:19:57.700Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33603-motorheads-saison-1.html...
2025-05-21T00:19:57.700Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:19:57.700Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33603-motorheads-saison-1.html
2025-05-21T00:20:01.097Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:20:01.097Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:20:01.106Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:20:01.108Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:20:01.109Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:20:01.110Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:20:01.112Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:20:01.113Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:20:01.114Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vs"
2025-05-21T00:20:01.117Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vs"
2025-05-21T00:20:01.119Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vs"
2025-05-21T00:20:01.121Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vs"
2025-05-21T00:20:01.122Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:20:01.123Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:20:01.124Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:20:01.126Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:20:01.127Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:20:01.129Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:20:01.130Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:20:01.131Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vf"
2025-05-21T00:20:01.134Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vf"
2025-05-21T00:20:01.136Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vf"
2025-05-21T00:20:01.139Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vf"
2025-05-21T00:20:01.140Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33603-motorheads-saison-1.html... | Episodes: 10, Movie Streams: 0
2025-05-21T00:20:01.141Z [info]: Using advanced enrichment for Motorheads
2025-05-21T00:20:01.571Z [info]: Using Gemini-optimized query for 'Motorheads': 'Motorheads'
2025-05-21T00:20:01.572Z [info]: Searching TMDB for 'Motorheads' (tv)
2025-05-21T00:20:01.615Z [info]: Fetching TMDB details for 'Motorheads' (ID: 243954)
2025-05-21T00:20:01.657Z [info]: Cleaned title: Motorheads -> Motorheads
2025-05-21T00:20:01.657Z [info]: Enriched Motorheads with TMDb ID: 243954
2025-05-21T00:20:01.657Z [info]: Fetching TMDB seasons data for Motorheads (TMDB ID: 243954)
2025-05-21T00:20:01.657Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=243954
2025-05-21T00:20:01.697Z [info]: Found 1 seasons for TMDB TV series 243954.
2025-05-21T00:20:01.697Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/243954/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:03.239Z [info]: Successfully fetched details for 1 out of 1 seasons for series 243954.
2025-05-21T00:20:03.240Z [info]: Formatted 1 out of 1 seasons for DB.
2025-05-21T00:20:03.240Z [info]: Found 1 TMDB seasons for Motorheads
2025-05-21T00:20:03.240Z [info]: Set tmdbSeason field for season 1
2025-05-21T00:20:03.241Z [info]: TMDB title similarity check: "Motorheads" vs "Motorheads" = 1.000
2025-05-21T00:20:03.241Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:20:04.242Z [info]: Seasons count changed for Motorheads: TMDB (0 -> 1)
2025-05-21T00:20:04.242Z [info]: Updating series (latest mode): Motorheads - metadata changes detected, updating database
2025-05-21T00:20:04.258Z [info]: Processing series: Le Négociateur (https://flemmix.ws/serie-en-streaming/33602-le-negociateur-saison-2.html)
2025-05-21T00:20:04.259Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33602-le-negociateur-saison-2.html...
2025-05-21T00:20:04.259Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:20:04.259Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33602-le-negociateur-saison-2.html
2025-05-21T00:20:07.102Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:20:07.102Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:20:07.102Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:20:07.103Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:20:07.105Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:20:07.107Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:20:07.109Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:20:07.110Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33602-le-negociateur-saison-2.html... | Episodes: 4, Movie Streams: 0
2025-05-21T00:20:07.110Z [info]: Using advanced enrichment for Le Négociateur
2025-05-21T00:20:07.516Z [info]: Using Gemini-optimized query for 'Le Négociateur': 'Le Négociateur'
2025-05-21T00:20:07.516Z [info]: Searching TMDB for 'Le Negociateur' (tv)
2025-05-21T00:20:07.558Z [info]: Fetching TMDB details for 'Le Negociateur' (ID: 219940)
2025-05-21T00:20:07.689Z [info]: Cleaned title: Le Négociateur -> Le Negociateur
2025-05-21T00:20:07.689Z [info]: Enriched Le Negociateur with TMDb ID: 219940
2025-05-21T00:20:07.689Z [info]: Fetching TMDB seasons data for Le Negociateur (TMDB ID: 219940)
2025-05-21T00:20:07.689Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=219940
2025-05-21T00:20:07.819Z [info]: Found 2 seasons for TMDB TV series 219940.
2025-05-21T00:20:07.819Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/219940/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:09.458Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/219940/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:11.097Z [info]: Successfully fetched details for 2 out of 2 seasons for series 219940.
2025-05-21T00:20:11.098Z [info]: Formatted 2 out of 2 seasons for DB.
2025-05-21T00:20:11.098Z [info]: Found 2 TMDB seasons for Le Negociateur
2025-05-21T00:20:11.099Z [info]: Set tmdbSeason field for season 2
2025-05-21T00:20:11.099Z [info]: TMDB title similarity check: "Le Négociateur" vs "Le Négociateur" = 1.000
2025-05-21T00:20:11.099Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:20:12.100Z [info]: Seasons count changed for Le Négociateur: TMDB (0 -> 2)
2025-05-21T00:20:12.100Z [info]: Updating series (latest mode): Le Négociateur - metadata changes detected, updating database
2025-05-21T00:20:12.116Z [info]: Processing series: Allegiance (https://flemmix.ws/serie-en-streaming/33553-allegiance-saison-2.html)
2025-05-21T00:20:12.116Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33553-allegiance-saison-2.html...
2025-05-21T00:20:12.116Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:20:12.116Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33553-allegiance-saison-2.html
2025-05-21T00:20:14.956Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:20:14.957Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:20:14.958Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:20:14.960Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:20:14.961Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:20:14.962Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:20:14.964Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:20:14.965Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:20:14.967Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:20:14.969Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:20:14.970Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:20:14.972Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33553-allegiance-saison-2.html... | Episodes: 4, Movie Streams: 0
2025-05-21T00:20:14.972Z [info]: Using advanced enrichment for Allegiance
2025-05-21T00:20:15.340Z [info]: Using Gemini-optimized query for 'Allegiance': 'Allegiance'
2025-05-21T00:20:15.340Z [info]: Searching TMDB for 'Allegiance' (tv)
2025-05-21T00:20:15.473Z [info]: Fetching TMDB details for 'Allegiance' (ID: 61703)
2025-05-21T00:20:15.613Z [info]: Cleaned title: Allegiance -> Allegiance
2025-05-21T00:20:15.614Z [info]: Enriched Allegiance with TMDb ID: 61703
2025-05-21T00:20:15.614Z [info]: Fetching TMDB seasons data for Allegiance (TMDB ID: 61703)
2025-05-21T00:20:15.614Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=61703
2025-05-21T00:20:15.761Z [info]: Found 1 seasons for TMDB TV series 61703.
2025-05-21T00:20:15.761Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/61703/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:17.418Z [info]: Successfully fetched details for 1 out of 1 seasons for series 61703.
2025-05-21T00:20:17.419Z [info]: Formatted 1 out of 1 seasons for DB.
2025-05-21T00:20:17.419Z [info]: Found 1 TMDB seasons for Allegiance
2025-05-21T00:20:17.419Z [info]: TMDB title similarity check: "Allegiance" vs "Allegiance" = 1.000
2025-05-21T00:20:17.419Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:20:18.421Z [info]: Seasons count changed for Allegiance: TMDB (0 -> 1)
2025-05-21T00:20:18.421Z [info]: Updating series (latest mode): Allegiance - metadata changes detected, updating database
2025-05-21T00:20:18.449Z [info]: Processing series: Les Simpson (https://flemmix.ws/serie-en-streaming/31907-les-simpson-saison-36.html)
2025-05-21T00:20:18.450Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/31907-les-simpson-saison-36.html...
2025-05-21T00:20:18.450Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:20:18.450Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/31907-les-simpson-saison-36.html
2025-05-21T00:20:21.298Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:20:21.299Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:20:21.300Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:20:21.302Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:20:21.304Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:20:21.306Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:20:21.307Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:20:21.309Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:20:21.311Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vs"
2025-05-21T00:20:21.312Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vs"
2025-05-21T00:20:21.314Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vs"
2025-05-21T00:20:21.315Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vs"
2025-05-21T00:20:21.316Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 11" with rel="ep11vs"
2025-05-21T00:20:21.318Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 12" with rel="ep12vs"
2025-05-21T00:20:21.320Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 13" with rel="ep13vs"
2025-05-21T00:20:21.321Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 14" with rel="ep14vs"
2025-05-21T00:20:21.322Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 15" with rel="ep15vs"
2025-05-21T00:20:21.323Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:20:21.323Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:20:21.324Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:20:21.326Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:20:21.328Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:20:21.329Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:20:21.330Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:20:21.332Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vf"
2025-05-21T00:20:21.334Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vf"
2025-05-21T00:20:21.336Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vf"
2025-05-21T00:20:21.337Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vf"
2025-05-21T00:20:21.339Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 11" with rel="ep11vf"
2025-05-21T00:20:21.341Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 12" with rel="ep12vf"
2025-05-21T00:20:21.344Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/31907-les-simpson-saison-36.html... | Episodes: 15, Movie Streams: 0
2025-05-21T00:20:21.344Z [info]: Using advanced enrichment for Les Simpson
2025-05-21T00:20:21.718Z [info]: Using Gemini-optimized query for 'Les Simpson': 'The Simpsons'
2025-05-21T00:20:21.718Z [info]: Searching TMDB for 'The Simpsons' (tv)
2025-05-21T00:20:21.760Z [info]: Fetching TMDB details for 'The Simpsons' (ID: 456)
2025-05-21T00:20:21.805Z [info]: Cleaned title: The Simpsons -> The Simpsons
2025-05-21T00:20:21.805Z [info]: Enriched The Simpsons with TMDb ID: 456
2025-05-21T00:20:21.805Z [info]: Fetching TMDB seasons data for The Simpsons (TMDB ID: 456)
2025-05-21T00:20:21.806Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=456
2025-05-21T00:20:21.846Z [info]: Found 37 seasons for TMDB TV series 456.
2025-05-21T00:20:21.846Z [warn]: Missing required parameters for fetchTmdbSeason: tmdbId=456, seasonNumber=0
2025-05-21T00:20:23.346Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:24.890Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:26.543Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/3?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:28.088Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/4?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:29.733Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/5?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:31.376Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/6?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:32.924Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/7?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:34.565Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/8?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:36.128Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/9?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:37.671Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/10?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:39.219Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/11?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:40.762Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/12?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:42.307Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/13?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:43.854Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/14?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:45.400Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/15?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:46.966Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/16?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:48.517Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/17?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:50.073Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/18?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:51.619Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/19?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:53.185Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/20?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:54.752Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/21?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:56.315Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/22?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:57.958Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/23?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:20:59.510Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/24?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:21:01.198Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/25?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:21:02.745Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/26?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:21:04.298Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/27?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:21:05.942Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/28?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:21:07.496Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/29?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:21:09.054Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/30?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:21:10.599Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/31?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:21:12.235Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/32?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:21:13.887Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/33?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:21:15.437Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/34?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:21:16.980Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/35?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:21:18.532Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/456/season/36?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:21:20.085Z [info]: Successfully fetched details for 36 out of 37 seasons for series 456.
2025-05-21T00:21:20.085Z [info]: Formatted 36 out of 36 seasons for DB.
2025-05-21T00:21:20.085Z [info]: Found 36 TMDB seasons for The Simpsons
2025-05-21T00:21:20.085Z [info]: Set tmdbSeason field for season 36
2025-05-21T00:21:20.086Z [info]: TMDB title similarity check: "Les Simpson" vs "Les Simpson" = 1.000
2025-05-21T00:21:20.086Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:21:21.088Z [info]: Seasons count changed for Les Simpson: TMDB (0 -> 36)
2025-05-21T00:21:21.088Z [info]: Updating series (latest mode): Les Simpson - metadata changes detected, updating database
2025-05-21T00:21:21.106Z [info]: Processing series: Law & Order Toronto: Criminal Intent (https://flemmix.ws/serie-en-streaming/33169-toronto-section-criminelle-saison-2.html)
2025-05-21T00:21:21.106Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33169-toronto-section-criminelle-saison-2.html...
2025-05-21T00:21:21.106Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:21:21.107Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33169-toronto-section-criminelle-saison-2.html
2025-05-21T00:21:24.082Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:21:24.082Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:21:24.083Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:21:24.085Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:21:24.087Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:21:24.089Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:21:24.090Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:21:24.091Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:21:24.093Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vs"
2025-05-21T00:21:24.094Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vs"
2025-05-21T00:21:24.095Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vs"
2025-05-21T00:21:24.097Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:21:24.097Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33169-toronto-section-criminelle-saison-2.html... | Episodes: 9, Movie Streams: 0
2025-05-21T00:21:24.097Z [info]: Using advanced enrichment for Law & Order Toronto: Criminal Intent
2025-05-21T00:21:24.564Z [info]: Using Gemini-optimized query for 'Law & Order Toronto: Criminal Intent': 'Law & Order Toronto: Criminal Intent'
2025-05-21T00:21:24.564Z [info]: Searching TMDB for 'Law & Order Toronto: Criminal Intent' (tv)
2025-05-21T00:21:24.711Z [info]: Fetching TMDB details for 'Law & Order Toronto: Criminal Intent' (ID: 228026)
2025-05-21T00:21:24.757Z [info]: Cleaned title: Law & Order Toronto: Criminal Intent -> Law & Order Toronto: Criminal Intent
2025-05-21T00:21:24.757Z [info]: Enriched Law & Order Toronto: Criminal Intent with TMDb ID: 228026
2025-05-21T00:21:24.757Z [info]: Fetching TMDB seasons data for Law & Order Toronto: Criminal Intent (TMDB ID: 228026)
2025-05-21T00:21:24.757Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=228026
2025-05-21T00:21:24.798Z [info]: Found 2 seasons for TMDB TV series 228026.
2025-05-21T00:21:24.799Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/228026/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:21:26.342Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/228026/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:21:27.890Z [info]: Successfully fetched details for 2 out of 2 seasons for series 228026.
2025-05-21T00:21:27.890Z [info]: Formatted 2 out of 2 seasons for DB.
2025-05-21T00:21:27.890Z [info]: Found 2 TMDB seasons for Law & Order Toronto: Criminal Intent
2025-05-21T00:21:27.891Z [info]: Set tmdbSeason field for season 2
2025-05-21T00:21:27.891Z [info]: TMDB title similarity check: "Law & Order Toronto: Criminal Intent" vs "Law & Order Toronto: Criminal Intent" = 1.000
2025-05-21T00:21:27.891Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:21:28.892Z [info]: Seasons count changed for Law & Order Toronto: Criminal Intent: TMDB (0 -> 2)
2025-05-21T00:21:28.892Z [info]: Updating series (latest mode): Law & Order Toronto: Criminal Intent - metadata changes detected, updating database
2025-05-21T00:21:28.912Z [info]: Processing series: Cimetière indien (https://flemmix.ws/serie-en-streaming/33253-cimetiere-indien-saison-1.html)
2025-05-21T00:21:28.912Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33253-cimetiere-indien-saison-1.html...
2025-05-21T00:21:28.912Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:21:28.913Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33253-cimetiere-indien-saison-1.html
2025-05-21T00:21:30.712Z [info]: GraphQL Config query accessed
2025-05-21T00:21:31.029Z [info]: GraphQL Movies: sort=TRENDING, page=1, limit=15
2025-05-21T00:21:31.030Z [info]: Fetching trending items: type=movie, page=1, limit=15
2025-05-21T00:21:31.063Z [info]: GraphQL Series: sort=TRENDING, page=1, limit=15
2025-05-21T00:21:31.064Z [info]: Fetching trending items: type=tv, page=1, limit=15
2025-05-21T00:21:31.084Z [info]: GraphQL Anime: sort=TRENDING, page=1, limit=15
2025-05-21T00:21:31.084Z [info]: Fetching trending items: type=tv, page=1, limit=15
2025-05-21T00:21:31.336Z [info]: Returning 12 trending items sorted by rank for movie page 1.
2025-05-21T00:21:31.514Z [info]: GraphQL Series: sort=TRENDING, page=1, limit=15
2025-05-21T00:21:31.514Z [info]: Fetching trending items: type=tv, page=1, limit=15
2025-05-21T00:21:31.539Z [info]: GraphQL Series: sort=LATEST, page=1, limit=40
2025-05-21T00:21:31.587Z [info]: GraphQL Series: sort=TRENDING, page=1, limit=15
2025-05-21T00:21:31.587Z [info]: Fetching trending items: type=tv, page=1, limit=15
2025-05-21T00:21:32.223Z [info]: Returning 27 trending items sorted by rank for tv page 1.
2025-05-21T00:21:33.054Z [info]: Returning 2 trending items sorted by rank for tv page 1.
2025-05-21T00:21:33.090Z [info]: GraphQL Movies: sort=TRENDING, page=1, limit=15
2025-05-21T00:21:33.091Z [info]: Fetching trending items: type=movie, page=1, limit=15
2025-05-21T00:21:33.129Z [info]: GraphQL Series: sort=TRENDING, page=1, limit=15
2025-05-21T00:21:33.129Z [info]: Fetching trending items: type=tv, page=1, limit=15
2025-05-21T00:21:33.168Z [info]: GraphQL Anime: sort=TRENDING, page=1, limit=15
2025-05-21T00:21:33.169Z [info]: Fetching trending items: type=tv, page=1, limit=15
2025-05-21T00:21:33.439Z [info]: Returning 27 trending items sorted by rank for tv page 1.
2025-05-21T00:21:33.528Z [info]: Returning 12 trending items sorted by rank for movie page 1.
2025-05-21T00:21:33.708Z [info]: Returning 27 trending items sorted by rank for tv page 1.
2025-05-21T00:21:34.385Z [info]: Returning 27 trending items sorted by rank for tv page 1.
2025-05-21T00:21:34.831Z [info]: Returning 2 trending items sorted by rank for tv page 1.
2025-05-21T00:21:37.582Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:21:37.583Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:21:37.584Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:21:37.585Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:21:37.589Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:21:37.592Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:21:37.594Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:21:37.598Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:21:37.602Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:21:37.607Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vf"
2025-05-21T00:21:37.620Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vf"
2025-05-21T00:21:37.624Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33253-cimetiere-indien-saison-1.html... | Episodes: 8, Movie Streams: 0
2025-05-21T00:21:37.624Z [info]: Using advanced enrichment for Cimetière indien
2025-05-21T00:21:38.058Z [info]: Using Gemini-optimized query for 'Cimetière indien': 'Indian Cemetery'
2025-05-21T00:21:38.059Z [info]: Searching TMDB for 'Indian Cemetery' (tv)
2025-05-21T00:21:38.196Z [warn]: No TMDb match for Indian Cemetery (tv)
2025-05-21T00:21:38.197Z [info]: Cleaned title: Indian Cemetery -> Indian Cemetery
2025-05-21T00:21:38.197Z [warn]: No TMDb match for Indian Cemetery (type: tv, year: none)
2025-05-21T00:21:39.198Z [info]: Updating series (latest mode): Cimetière indien - No new episodes but updating metadata/seasons
2025-05-21T00:21:39.212Z [info]: Processing series: Le Remplaçant (https://flemmix.ws/serie-en-streaming/33593-le-remplacant-saison-1.html)
2025-05-21T00:21:39.213Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33593-le-remplacant-saison-1.html...
2025-05-21T00:21:39.213Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:21:39.213Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33593-le-remplacant-saison-1.html
2025-05-21T00:21:41.956Z [info]: GraphQL Series: sort=LATEST, page=2, limit=40
2025-05-21T00:21:43.108Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:21:43.109Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:21:43.109Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:21:43.110Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:21:43.111Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:21:43.115Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33593-le-remplacant-saison-1.html... | Episodes: 2, Movie Streams: 0
2025-05-21T00:21:43.115Z [info]: Using advanced enrichment for Le Remplaçant
2025-05-21T00:21:43.499Z [info]: Using Gemini-optimized query for 'Le Remplaçant': 'Le Remplaçant'
2025-05-21T00:21:43.500Z [info]: Searching TMDB for 'Le Remplacant' (tv)
2025-05-21T00:21:43.562Z [info]: Fetching TMDB details for 'Le Remplacant' (ID: 121622)
2025-05-21T00:21:43.602Z [info]: Cleaned title: Le Remplaçant -> Le Remplacant
2025-05-21T00:21:43.602Z [info]: Enriched Le Remplacant with TMDb ID: 121622
2025-05-21T00:21:43.602Z [info]: Fetching TMDB seasons data for Le Remplacant (TMDB ID: 121622)
2025-05-21T00:21:43.602Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=121622
2025-05-21T00:21:43.646Z [info]: Found 3 seasons for TMDB TV series 121622.
2025-05-21T00:21:43.647Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121622/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:21:45.200Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121622/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:21:46.746Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121622/season/3?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:21:48.307Z [info]: Successfully fetched details for 3 out of 3 seasons for series 121622.
2025-05-21T00:21:48.307Z [info]: Formatted 3 out of 3 seasons for DB.
2025-05-21T00:21:48.307Z [info]: Found 3 TMDB seasons for Le Remplacant
2025-05-21T00:21:48.307Z [info]: Set tmdbSeason field for season 3
2025-05-21T00:21:48.308Z [info]: TMDB title similarity check: "Le Remplaçant" vs "Le Remplaçant" = 1.000
2025-05-21T00:21:48.308Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:21:49.309Z [info]: Seasons count changed for Le Remplaçant: TMDB (0 -> 3)
2025-05-21T00:21:49.310Z [info]: Updating series (latest mode): Le Remplaçant - metadata changes detected, updating database
2025-05-21T00:21:49.310Z [info]: Saving/Updating 20 series from page 1 to database
2025-05-21T00:21:49.312Z [info]: Attempting to save 20 series items to DB
2025-05-21T00:21:49.382Z [info]: Updating existing series: The Handmaid’s Tale : la servante écarlate (ID: 67f4ec1169b0845f14d7a392)
2025-05-21T00:21:49.383Z [info]: Updating 9 episodes for The Handmaid’s Tale : la servante écarlate
2025-05-21T00:21:49.383Z [info]: Updating 6 TMDB seasons for The Handmaid’s Tale : la servante écarlate
2025-05-21T00:21:49.383Z [info]: Updating TMDB season data for The Handmaid’s Tale : la servante écarlate season 6
2025-05-21T00:21:49.469Z [info]: Successfully updated series: The Handmaid’s Tale : la servante écarlate with 10 update operations
2025-05-21T00:21:49.500Z [info]: Updating existing series: The Last Of Us (ID: 67fe40c3608d7c6e175ab877)
2025-05-21T00:21:49.500Z [info]: Updating 6 episodes for The Last Of Us
2025-05-21T00:21:49.500Z [info]: Updating 2 TMDB seasons for The Last Of Us
2025-05-21T00:21:49.500Z [info]: Updating TMDB season data for The Last Of Us season 2
2025-05-21T00:21:49.537Z [info]: Successfully updated series: The Last Of Us with 10 update operations
2025-05-21T00:21:49.552Z [info]: Updating existing series: The Walking Dead : Dead City (ID: 6817adcb882c61756bece555)
2025-05-21T00:21:49.552Z [info]: Updating 3 episodes for The Walking Dead : Dead City
2025-05-21T00:21:49.552Z [info]: Updating 2 TMDB seasons for The Walking Dead : Dead City
2025-05-21T00:21:49.552Z [info]: Updating TMDB season data for The Walking Dead : Dead City season 2
2025-05-21T00:21:49.586Z [info]: Successfully updated series: The Walking Dead : Dead City with 10 update operations
2025-05-21T00:21:49.604Z [info]: Updating existing series: Power Book III: Raising Kanan (ID: 67de16676825d721f0040b19)
2025-05-21T00:21:49.604Z [info]: Updating 10 episodes for Power Book III: Raising Kanan
2025-05-21T00:21:49.604Z [info]: Updating 4 TMDB seasons for Power Book III: Raising Kanan
2025-05-21T00:21:49.605Z [info]: Updating TMDB season data for Power Book III: Raising Kanan season 4
2025-05-21T00:21:49.661Z [info]: Successfully updated series: Power Book III: Raising Kanan with 10 update operations
2025-05-21T00:21:49.680Z [info]: Updating existing series: Andor (ID: 680916f9aad2bf7833441fa2)
2025-05-21T00:21:49.680Z [info]: Updating 12 episodes for Andor
2025-05-21T00:21:49.680Z [info]: Updating 2 TMDB seasons for Andor
2025-05-21T00:21:49.681Z [info]: Updating TMDB season data for Andor season 2
2025-05-21T00:21:49.726Z [info]: Successfully updated series: Andor with 10 update operations
2025-05-21T00:21:49.745Z [info]: Updating existing series: HPI (ID: 6821374a365b464d9cb17a61)
2025-05-21T00:21:49.745Z [info]: Updating 2 episodes for HPI
2025-05-21T00:21:49.745Z [info]: Updating 5 TMDB seasons for HPI
2025-05-21T00:21:49.745Z [info]: Updating TMDB season data for HPI season 5
2025-05-21T00:21:49.818Z [info]: GraphQL Anime: sort=LATEST, page=1, limit=40
2025-05-21T00:21:49.823Z [info]: Successfully updated series: HPI with 10 update operations
2025-05-21T00:21:49.856Z [info]: Updating existing series: Astérix et Obélix : le Combat des Chefs (ID: 6812758f7cec33533bc22a0d)
2025-05-21T00:21:49.856Z [info]: Updating 5 episodes for Astérix et Obélix : le Combat des Chefs
2025-05-21T00:21:49.857Z [info]: Updating 1 TMDB seasons for Astérix et Obélix : le Combat des Chefs
2025-05-21T00:21:49.857Z [info]: Updating TMDB season data for Astérix et Obélix : le Combat des Chefs season 1
2025-05-21T00:21:49.897Z [info]: Successfully updated series: Astérix et Obélix : le Combat des Chefs with 10 update operations
2025-05-21T00:21:49.925Z [info]: Updating existing series: You (2018) (ID: 680a6ba61e094d47bd0101f0)
2025-05-21T00:21:49.926Z [info]: Updating 10 episodes for You (2018)
2025-05-21T00:21:49.926Z [info]: Updating 5 TMDB seasons for You (2018)
2025-05-21T00:21:49.926Z [info]: Updating TMDB season data for You (2018) season 5
2025-05-21T00:21:49.986Z [info]: Successfully updated series: You (2018) with 10 update operations
2025-05-21T00:21:50.315Z [info]: Updating existing series: Daredevil: Born Again (ID: 67de16676825d721f0040a8d)
2025-05-21T00:21:50.315Z [info]: Updating 9 episodes for Daredevil: Born Again
2025-05-21T00:21:50.316Z [info]: Updating 1 TMDB seasons for Daredevil: Born Again
2025-05-21T00:21:50.316Z [info]: Updating TMDB season data for Daredevil: Born Again season 1
2025-05-21T00:21:50.365Z [info]: Successfully updated series: Daredevil: Born Again with 10 update operations
2025-05-21T00:21:50.421Z [info]: Updating existing series: Black Mirror (ID: 67f7f3d50304cc4b56478967)
2025-05-21T00:21:50.421Z [info]: Updating 6 episodes for Black Mirror
2025-05-21T00:21:50.421Z [info]: Updating 7 TMDB seasons for Black Mirror
2025-05-21T00:21:50.421Z [info]: Updating TMDB season data for Black Mirror season 7
2025-05-21T00:21:50.527Z [info]: Successfully updated series: Black Mirror with 10 update operations
2025-05-21T00:21:50.738Z [info]: Updating existing series: Ici Tout Commence (ID: 681a71dac1ca514e793d2e71)
2025-05-21T00:21:50.738Z [info]: Updating 12 episodes for Ici Tout Commence
2025-05-21T00:21:50.738Z [info]: Updating 4 TMDB seasons for Ici Tout Commence
2025-05-21T00:21:51.252Z [info]: Successfully updated series: Ici Tout Commence with 9 update operations
2025-05-21T00:21:51.291Z [info]: Updating existing series: À l'épreuve du diable (ID: 681a71d7c1ca514e793d2daf)
2025-05-21T00:21:51.291Z [info]: Updating 12 episodes for À l'épreuve du diable
2025-05-21T00:21:51.291Z [info]: Updating 2 TMDB seasons for À l'épreuve du diable
2025-05-21T00:21:51.292Z [info]: Updating TMDB season data for À l'épreuve du diable season 2
2025-05-21T00:21:51.395Z [info]: Successfully updated series: À l'épreuve du diable with 10 update operations
2025-05-21T00:21:51.430Z [info]: Updating existing series: Alaska : la ruée vers l'or (ID: 67de16696825d721f00424e7)
2025-05-21T00:21:51.430Z [info]: Updating 19 episodes for Alaska : la ruée vers l'or
2025-05-21T00:21:51.430Z [info]: Updating 15 TMDB seasons for Alaska : la ruée vers l'or
2025-05-21T00:21:51.430Z [info]: Updating TMDB season data for Alaska : la ruée vers l'or season 15
2025-05-21T00:21:51.569Z [info]: Successfully updated series: Alaska : la ruée vers l'or with 10 update operations
2025-05-21T00:21:51.618Z [info]: Updating existing series: Motorheads (ID: 682cff3f5d080bbe86898fee)
2025-05-21T00:21:51.619Z [info]: Updating 10 episodes for Motorheads
2025-05-21T00:21:51.619Z [info]: Updating 1 TMDB seasons for Motorheads
2025-05-21T00:21:51.619Z [info]: Updating TMDB season data for Motorheads season 1
2025-05-21T00:21:51.668Z [info]: Successfully updated series: Motorheads with 10 update operations
2025-05-21T00:21:51.685Z [info]: Updating existing series: Le Négociateur (ID: 682ca9b68aae2314978fd624)
2025-05-21T00:21:51.686Z [info]: Updating 4 episodes for Le Négociateur
2025-05-21T00:21:51.686Z [info]: Updating 2 TMDB seasons for Le Négociateur
2025-05-21T00:21:51.686Z [info]: Updating TMDB season data for Le Négociateur season 2
2025-05-21T00:21:51.710Z [info]: Successfully updated series: Le Négociateur with 10 update operations
2025-05-21T00:21:51.728Z [info]: Updating existing series: Allegiance (ID: 682323b01c9051a19c2093f8)
2025-05-21T00:21:51.729Z [info]: Updating 4 episodes for Allegiance
2025-05-21T00:21:51.729Z [info]: Updating 1 TMDB seasons for Allegiance
2025-05-21T00:21:51.765Z [info]: Successfully updated series: Allegiance with 9 update operations
2025-05-21T00:21:51.828Z [info]: Updating existing series: Les Simpson (ID: 67de16a16825d721f00489e2)
2025-05-21T00:21:51.828Z [info]: Updating 15 episodes for Les Simpson
2025-05-21T00:21:51.828Z [info]: Updating 36 TMDB seasons for Les Simpson
2025-05-21T00:21:51.828Z [info]: Updating TMDB season data for Les Simpson season 36
2025-05-21T00:21:52.056Z [info]: Successfully updated series: Les Simpson with 10 update operations
2025-05-21T00:21:52.077Z [info]: Updating existing series: Law & Order Toronto: Criminal Intent (ID: 67efb2cdc5dcb0fc0905c6c2)
2025-05-21T00:21:52.077Z [info]: Updating 9 episodes for Law & Order Toronto: Criminal Intent
2025-05-21T00:21:52.077Z [info]: Updating 2 TMDB seasons for Law & Order Toronto: Criminal Intent
2025-05-21T00:21:52.077Z [info]: Updating TMDB season data for Law & Order Toronto: Criminal Intent season 2
2025-05-21T00:21:52.116Z [info]: Successfully updated series: Law & Order Toronto: Criminal Intent with 10 update operations
2025-05-21T00:21:52.137Z [info]: Updating existing series: Cimetière indien (ID: 67f456755e602f960ec148d9)
2025-05-21T00:21:52.137Z [info]: Updating 8 episodes for Cimetière indien
2025-05-21T00:21:52.174Z [info]: Successfully updated series: Cimetière indien with 8 update operations
2025-05-21T00:21:52.191Z [info]: Updating existing series: Le Remplaçant (ID: 682b951fe21156b728a76bde)
2025-05-21T00:21:52.191Z [info]: Updating 2 episodes for Le Remplaçant
2025-05-21T00:21:52.191Z [info]: Updating 3 TMDB seasons for Le Remplaçant
2025-05-21T00:21:52.191Z [info]: Updating TMDB season data for Le Remplaçant season 3
2025-05-21T00:21:52.222Z [info]: Successfully updated series: Le Remplaçant with 10 update operations
2025-05-21T00:21:52.222Z [info]: 20 series items processed. Created: 0, Updated: 20, Errors: 0
2025-05-21T00:21:52.222Z [info]: Database operation results for page 1: Created: 0, Updated: 20, Errors: 0
2025-05-21T00:21:52.222Z [info]: Successfully processed/saved 20 series from page 1
2025-05-21T00:21:52.222Z [info]: [Wiflix Series] Pausing 3s between pages for endpoint serie-en-streaming...
2025-05-21T00:21:55.224Z [info]: [Wiflix Series] Processing page 2/6 for serie-en-streaming
2025-05-21T00:21:55.226Z [info]: [Wiflix List] Starting Wiflix series list scrape for "serie-en-streaming", planning to scrape UP TO 2 pages using Puppeteer... Base URL: https://flemmix.ws
2025-05-21T00:21:55.228Z [info]: [Wiflix List] Fetching page 1 for serie-en-streaming using Puppeteer...
2025-05-21T00:21:55.229Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/
2025-05-21T00:21:55.229Z [info]: [Wiflix List] Fetching page 2 for serie-en-streaming using Puppeteer...
2025-05-21T00:21:56.230Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/page/2/
2025-05-21T00:21:57.182Z [info]: [Wiflix List] Scraped Wiflix series endpoint "serie-en-streaming" page 1 - Found & Validated: 20 items
2025-05-21T00:21:57.950Z [info]: [Wiflix List] Scraped Wiflix series endpoint "serie-en-streaming" page 2 - Found & Validated: 20 items
2025-05-21T00:21:57.950Z [info]: [Wiflix List] Finished Wiflix series list scrape for endpoint "serie-en-streaming". Total unique items collected: 40
2025-05-21T00:21:57.950Z [info]: Collected 40 series from serie-en-streaming, page 2 before processing
2025-05-21T00:21:57.950Z [info]: Skipping already processed URL in this session: The Handmaid’s Tale : la servante écarlate (https://flemmix.ws/serie-en-streaming/33255-the-handmaids-tale-la-servante-ecarlate-saison-6.html)
2025-05-21T00:21:57.950Z [info]: Skipping already processed URL in this session: The Last Of Us (https://flemmix.ws/serie-en-streaming/33307-the-last-of-us-saison-2.html)
2025-05-21T00:21:57.950Z [info]: Skipping already processed URL in this session: The Walking Dead : Dead City (https://flemmix.ws/serie-en-streaming/33497-the-walking-dead-dead-city-saison-2.html)
2025-05-21T00:21:57.950Z [info]: Skipping already processed URL in this session: Power Book III: Raising Kanan (https://flemmix.ws/serie-en-streaming/33004-power-book-iii-raising-kanan-saison-4.html)
2025-05-21T00:21:57.950Z [info]: Skipping already processed URL in this session: Andor (https://flemmix.ws/serie-en-streaming/33380-andor-saison-2.html)
2025-05-21T00:21:57.951Z [info]: Skipping already processed URL in this session: HPI (https://flemmix.ws/serie-en-streaming/33545-hpi-saison-5.html)
2025-05-21T00:21:57.951Z [info]: Skipping already processed URL in this session: Astérix et Obélix : le Combat des Chefs (https://flemmix.ws/serie-en-streaming/33451-asterix-et-obelix-le-combat-des-chefs-saison-1.html)
2025-05-21T00:21:57.951Z [info]: Skipping already processed URL in this session: You (2018) (https://flemmix.ws/serie-en-streaming/33398-you-saison-5.html)
2025-05-21T00:21:57.951Z [info]: Skipping already processed URL in this session: Daredevil: Born Again (https://flemmix.ws/serie-en-streaming/32957-daredevil-born-again.html)
2025-05-21T00:21:57.951Z [info]: Skipping already processed URL in this session: Black Mirror (https://flemmix.ws/serie-en-streaming/33278-black-mirror-saison-7.html)
2025-05-21T00:21:57.951Z [info]: Skipping already processed URL in this session: Ici Tout Commence (https://flemmix.ws/serie-en-streaming/33504-ici-tout-commence-saison-9-1170.html)
2025-05-21T00:21:57.951Z [info]: Skipping already processed URL in this session: À l'épreuve du diable (https://flemmix.ws/serie-en-streaming/33515-a-lepreuve-du-diable-saison-2.html)
2025-05-21T00:21:57.951Z [info]: Skipping already processed URL in this session: Alaska : la ruée vers l'or (https://flemmix.ws/serie-en-streaming/32507-alaska-la-ruee-vers-lor-saison-15.html)
2025-05-21T00:21:57.951Z [info]: Skipping already processed URL in this session: Motorheads (https://flemmix.ws/serie-en-streaming/33603-motorheads-saison-1.html)
2025-05-21T00:21:57.951Z [info]: Skipping already processed URL in this session: Le Négociateur (https://flemmix.ws/serie-en-streaming/33602-le-negociateur-saison-2.html)
2025-05-21T00:21:57.951Z [info]: Skipping already processed URL in this session: Allegiance (https://flemmix.ws/serie-en-streaming/33553-allegiance-saison-2.html)
2025-05-21T00:21:57.951Z [info]: Skipping already processed URL in this session: Les Simpson (https://flemmix.ws/serie-en-streaming/31907-les-simpson-saison-36.html)
2025-05-21T00:21:57.951Z [info]: Skipping already processed URL in this session: Law & Order Toronto: Criminal Intent (https://flemmix.ws/serie-en-streaming/33169-toronto-section-criminelle-saison-2.html)
2025-05-21T00:21:57.951Z [info]: Skipping already processed URL in this session: Cimetière indien (https://flemmix.ws/serie-en-streaming/33253-cimetiere-indien-saison-1.html)
2025-05-21T00:21:57.951Z [info]: Skipping already processed URL in this session: Le Remplaçant (https://flemmix.ws/serie-en-streaming/33593-le-remplacant-saison-1.html)
2025-05-21T00:21:57.964Z [info]: Processing series: F1 Grand Prix d'Emilie-Romagne (2025) (https://flemmix.ws/serie-en-streaming/33570-f1-grand-prix-demilie-romagne-2025-saison-1.html)
2025-05-21T00:21:57.964Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33570-f1-grand-prix-demilie-romagne-2025-saison-1.html...
2025-05-21T00:21:57.964Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:21:57.964Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33570-f1-grand-prix-demilie-romagne-2025-saison-1.html
2025-05-21T00:23:13.052Z [error]: [BrowserUtils] Navigation timeout for https://flemmix.ws/serie-en-streaming/33570-f1-grand-prix-demilie-romagne-2025-saison-1.html: Navigation timeout of 75000ms exceeded for https://flemmix.ws/serie-en-streaming/33570-f1-grand-prix-demilie-romagne-2025-saison-1.html
2025-05-21T00:25:13.055Z [error]: [BrowserUtils] Failed to get partial content: Runtime.callFunctionOn timed out. Increase the 'protocolTimeout' setting in launch/connect calls for a higher timeout if needed.
2025-05-21T00:25:13.055Z [error]: [BrowserUtils] Puppeteer fetch failed for https://flemmix.ws/serie-en-streaming/33570-f1-grand-prix-demilie-romagne-2025-saison-1.html: Navigation timeout of 75000ms exceeded for https://flemmix.ws/serie-en-streaming/33570-f1-grand-prix-demilie-romagne-2025-saison-1.html
2025-05-21T00:25:13.606Z [error]: [Wiflix Detail Final] Attempt 1 failed: Navigation timeout of 75000ms exceeded for https://flemmix.ws/serie-en-streaming/33570-f1-grand-prix-demilie-romagne-2025-saison-1.html
2025-05-21T00:25:13.606Z [info]: [Wiflix Detail Final] Attempt 2 with timeout 105000ms
2025-05-21T00:25:13.606Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33570-f1-grand-prix-demilie-romagne-2025-saison-1.html (retry 1/3)
2025-05-21T00:25:20.427Z [warn]: [BrowserUtils] Puppeteer browser instance disconnected.
2025-05-21T00:25:20.428Z [error]: [BrowserUtils] Puppeteer fetch failed for https://flemmix.ws/serie-en-streaming/33570-f1-grand-prix-demilie-romagne-2025-saison-1.html: Navigating frame was detached
2025-05-21T00:25:20.428Z [warn]: [BrowserUtils] Error closing page: Protocol error: Connection closed.
2025-05-21T00:25:20.428Z [error]: [Wiflix Detail Final] Attempt 2 failed: Navigating frame was detached
2025-05-21T00:25:20.428Z [info]: [Wiflix Detail Final] Attempt 3 with timeout 135000ms
2025-05-21T00:25:20.428Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33570-f1-grand-prix-demilie-romagne-2025-saison-1.html (retry 2/3)
2025-05-21T00:25:20.429Z [info]: [BrowserUtils] Launching new Puppeteer browser instance...
2025-05-21T00:25:21.005Z [info]: [BrowserUtils] New browser instance launched.
2025-05-21T00:25:24.763Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:25:24.764Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:25:24.772Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:25:24.773Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:25:24.775Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:25:24.776Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:25:24.779Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:25:24.781Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:25:24.783Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:25:24.784Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33570-f1-grand-prix-demilie-romagne-2025-saison-1.html... | Episodes: 6, Movie Streams: 0
2025-05-21T00:25:24.784Z [info]: Using advanced enrichment for F1 Grand Prix d'Emilie-Romagne (2025)
2025-05-21T00:25:25.812Z [info]: Using Gemini-optimized query for 'F1 Grand Prix d'Emilie-Romagne (2025)': 'F1 Grand Prix d'Emilie-Romagne'
2025-05-21T00:25:25.813Z [info]: Searching TMDB for 'F1 Grand Prix d'Emilie-Romagne' (tv)
2025-05-21T00:25:25.868Z [warn]: No TMDb match for F1 Grand Prix d'Emilie-Romagne (tv)
2025-05-21T00:25:25.868Z [info]: Cleaned title: F1 Grand Prix d'Emilie-Romagne -> F1 Grand Prix d'Emilie-Romagne
2025-05-21T00:25:25.868Z [warn]: No TMDb match for F1 Grand Prix d'Emilie-Romagne (type: tv, year: none)
2025-05-21T00:25:26.869Z [info]: Updating series (latest mode): F1 Grand Prix d'Emilie-Romagne (2025) - No new episodes but updating metadata/seasons
2025-05-21T00:25:26.892Z [info]: Processing series: The Rehearsal (https://flemmix.ws/serie-en-streaming/25258-the-rehearsal-saison-1.html)
2025-05-21T00:25:26.892Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/25258-the-rehearsal-saison-1.html...
2025-05-21T00:25:26.892Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:25:26.893Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/25258-the-rehearsal-saison-1.html
2025-05-21T00:25:30.094Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:25:30.095Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:25:30.095Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:25:30.097Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:25:30.099Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:25:30.101Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:25:30.103Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:25:30.106Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:25:30.106Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/25258-the-rehearsal-saison-1.html... | Episodes: 5, Movie Streams: 0
2025-05-21T00:25:30.106Z [info]: Using advanced enrichment for The Rehearsal
2025-05-21T00:25:30.553Z [info]: Using Gemini-optimized query for 'The Rehearsal': 'The Rehearsal'
2025-05-21T00:25:30.553Z [info]: Searching TMDB for 'The Rehearsal' (tv)
2025-05-21T00:25:30.599Z [info]: Fetching TMDB details for 'The Rehearsal' (ID: 204284)
2025-05-21T00:25:30.640Z [info]: Cleaned title: The Rehearsal -> The Rehearsal
2025-05-21T00:25:30.640Z [info]: Enriched The Rehearsal with TMDb ID: 204284
2025-05-21T00:25:30.640Z [info]: Fetching TMDB seasons data for The Rehearsal (TMDB ID: 204284)
2025-05-21T00:25:30.640Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=204284
2025-05-21T00:25:30.680Z [info]: Found 2 seasons for TMDB TV series 204284.
2025-05-21T00:25:30.680Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/204284/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:25:32.223Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/204284/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:25:33.874Z [info]: Successfully fetched details for 2 out of 2 seasons for series 204284.
2025-05-21T00:25:33.874Z [info]: Formatted 2 out of 2 seasons for DB.
2025-05-21T00:25:33.874Z [info]: Found 2 TMDB seasons for The Rehearsal
2025-05-21T00:25:33.875Z [info]: Set tmdbSeason field for season 2
2025-05-21T00:25:33.875Z [info]: TMDB title similarity check: "The Rehearsal" vs "The Rehearsal" = 1.000
2025-05-21T00:25:33.875Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:25:34.875Z [info]: Seasons count changed for The Rehearsal: TMDB (0 -> 2)
2025-05-21T00:25:34.875Z [info]: Updating series (latest mode): The Rehearsal - metadata changes detected, updating database
2025-05-21T00:25:34.902Z [info]: Processing series: Chicago Med (https://flemmix.ws/serie-en-streaming/31608-chicago-med-saison-10.html)
2025-05-21T00:25:34.902Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/31608-chicago-med-saison-10.html...
2025-05-21T00:25:34.902Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:25:34.902Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/31608-chicago-med-saison-10.html
2025-05-21T00:25:41.905Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:25:41.905Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:25:41.906Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:25:41.908Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:25:41.910Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:25:41.912Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:25:41.914Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:25:41.916Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:25:41.918Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vs"
2025-05-21T00:25:41.919Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vs"
2025-05-21T00:25:41.921Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vs"
2025-05-21T00:25:41.922Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vs"
2025-05-21T00:25:41.924Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 11" with rel="ep11vs"
2025-05-21T00:25:41.925Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 12" with rel="ep12vs"
2025-05-21T00:25:41.926Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 13" with rel="ep13vs"
2025-05-21T00:25:41.928Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 14" with rel="ep14vs"
2025-05-21T00:25:41.929Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 15" with rel="ep15vs"
2025-05-21T00:25:41.930Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 16" with rel="ep16vs"
2025-05-21T00:25:41.931Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 17" with rel="ep17vs"
2025-05-21T00:25:41.932Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 18" with rel="ep18vs"
2025-05-21T00:25:41.933Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 19" with rel="ep19vs"
2025-05-21T00:25:41.935Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 20" with rel="ep20vs"
2025-05-21T00:25:41.936Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 21" with rel="ep21vs"
2025-05-21T00:25:41.937Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:25:41.937Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:25:41.939Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:25:41.940Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/31608-chicago-med-saison-10.html... | Episodes: 21, Movie Streams: 0
2025-05-21T00:25:41.941Z [info]: Using advanced enrichment for Chicago Med
2025-05-21T00:25:42.331Z [info]: Using Gemini-optimized query for 'Chicago Med': 'Chicago Med'
2025-05-21T00:25:42.332Z [info]: Searching TMDB for 'Chicago Med' (tv)
2025-05-21T00:25:42.385Z [info]: Fetching TMDB details for 'Chicago Med' (ID: 62650)
2025-05-21T00:25:42.522Z [info]: Cleaned title: Chicago Med -> Chicago Med
2025-05-21T00:25:42.522Z [info]: Enriched Chicago Med with TMDb ID: 62650
2025-05-21T00:25:42.522Z [info]: Fetching TMDB seasons data for Chicago Med (TMDB ID: 62650)
2025-05-21T00:25:42.522Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=62650
2025-05-21T00:25:42.658Z [info]: Found 10 seasons for TMDB TV series 62650.
2025-05-21T00:25:42.658Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/62650/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:25:44.206Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/62650/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:25:45.868Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/62650/season/3?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:25:47.425Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/62650/season/4?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:25:48.973Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/62650/season/5?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:25:50.627Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/62650/season/6?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:25:52.222Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/62650/season/7?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:25:53.770Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/62650/season/8?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:25:55.318Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/62650/season/9?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:25:56.971Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/62650/season/10?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:25:58.517Z [info]: Successfully fetched details for 10 out of 10 seasons for series 62650.
2025-05-21T00:25:58.518Z [info]: Formatted 10 out of 10 seasons for DB.
2025-05-21T00:25:58.518Z [info]: Found 10 TMDB seasons for Chicago Med
2025-05-21T00:25:58.518Z [info]: Set tmdbSeason field for season 10
2025-05-21T00:25:58.518Z [info]: TMDB title similarity check: "Chicago Med" vs "Chicago Med" = 1.000
2025-05-21T00:25:58.518Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:25:59.519Z [info]: Seasons count changed for Chicago Med: TMDB (0 -> 10)
2025-05-21T00:25:59.519Z [info]: Updating series (latest mode): Chicago Med - metadata changes detected, updating database
2025-05-21T00:25:59.549Z [info]: Processing series: S.W.A.T. (https://flemmix.ws/serie-en-streaming/31808-swat-saison-8.html)
2025-05-21T00:25:59.549Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/31808-swat-saison-8.html...
2025-05-21T00:25:59.549Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:25:59.549Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/31808-swat-saison-8.html
2025-05-21T00:26:02.902Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:26:02.902Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:26:02.903Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:26:02.906Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:26:02.908Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:26:02.910Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:26:02.911Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:26:02.913Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:26:02.915Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vs"
2025-05-21T00:26:02.916Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vs"
2025-05-21T00:26:02.917Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vs"
2025-05-21T00:26:02.919Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vs"
2025-05-21T00:26:02.921Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 11" with rel="ep11vs"
2025-05-21T00:26:02.923Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 12" with rel="ep12vs"
2025-05-21T00:26:02.925Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 13" with rel="ep13vs"
2025-05-21T00:26:02.927Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 14" with rel="ep14vs"
2025-05-21T00:26:02.928Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 15" with rel="ep15vs"
2025-05-21T00:26:02.929Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 16" with rel="ep16vs"
2025-05-21T00:26:02.931Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 17" with rel="ep17vs"
2025-05-21T00:26:02.932Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 18" with rel="ep18vs"
2025-05-21T00:26:02.933Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 19" with rel="ep19vs"
2025-05-21T00:26:02.935Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 20" with rel="ep20vs"
2025-05-21T00:26:02.936Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 21" with rel="ep21vs"
2025-05-21T00:26:02.938Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 22" with rel="ep22vs"
2025-05-21T00:26:02.940Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:26:02.940Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/31808-swat-saison-8.html... | Episodes: 22, Movie Streams: 0
2025-05-21T00:26:02.941Z [info]: Using advanced enrichment for S.W.A.T.
2025-05-21T00:26:03.430Z [info]: Using Gemini-optimized query for 'S.W.A.T.': 'S.W.A.T.'
2025-05-21T00:26:03.430Z [info]: Searching TMDB for 'S.W.A.T.' (tv)
2025-05-21T00:26:03.475Z [info]: Fetching TMDB details for 'S.W.A.T.' (ID: 71790)
2025-05-21T00:26:03.610Z [info]: Cleaned title: S.W.A.T. -> S.W.A.T.
2025-05-21T00:26:03.611Z [info]: Enriched S.W.A.T. with TMDb ID: 71790
2025-05-21T00:26:03.611Z [info]: Fetching TMDB seasons data for S.W.A.T. (TMDB ID: 71790)
2025-05-21T00:26:03.611Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=71790
2025-05-21T00:26:03.750Z [info]: Found 8 seasons for TMDB TV series 71790.
2025-05-21T00:26:03.750Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/71790/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:26:05.303Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/71790/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:26:06.861Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/71790/season/3?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:26:08.409Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/71790/season/4?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:26:09.960Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/71790/season/5?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:26:11.609Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/71790/season/6?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:26:13.185Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/71790/season/7?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:26:14.735Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/71790/season/8?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:26:16.288Z [info]: Successfully fetched details for 8 out of 8 seasons for series 71790.
2025-05-21T00:26:16.288Z [info]: Formatted 8 out of 8 seasons for DB.
2025-05-21T00:26:16.288Z [info]: Found 8 TMDB seasons for S.W.A.T.
2025-05-21T00:26:16.288Z [info]: Set tmdbSeason field for season 8
2025-05-21T00:26:16.288Z [info]: TMDB title similarity check: "S.W.A.T." vs "S.W.A.T." = 1.000
2025-05-21T00:26:16.288Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:26:17.289Z [info]: Seasons count changed for S.W.A.T.: TMDB (0 -> 8)
2025-05-21T00:26:17.290Z [info]: Updating series (latest mode): S.W.A.T. - metadata changes detected, updating database
2025-05-21T00:26:17.307Z [info]: Processing series: New York Crime Organisé (https://flemmix.ws/serie-en-streaming/33368-new-york-crime-organise-saison-5.html)
2025-05-21T00:26:17.307Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33368-new-york-crime-organise-saison-5.html...
2025-05-21T00:26:17.307Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:26:17.308Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33368-new-york-crime-organise-saison-5.html
2025-05-21T00:26:20.215Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:26:20.215Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:26:20.216Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:26:20.218Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:26:20.221Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:26:20.224Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:26:20.226Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:26:20.227Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:26:20.228Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:26:20.228Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33368-new-york-crime-organise-saison-5.html... | Episodes: 6, Movie Streams: 0
2025-05-21T00:26:20.228Z [info]: Using advanced enrichment for New York Crime Organisé
2025-05-21T00:26:20.692Z [info]: Using Gemini-optimized query for 'New York Crime Organisé': 'New York Crime Organisé'
2025-05-21T00:26:20.693Z [info]: Searching TMDB for 'New York Crime Organise' (tv)
2025-05-21T00:26:20.738Z [info]: Fetching TMDB details for 'New York Crime Organise' (ID: 106158)
2025-05-21T00:26:20.784Z [info]: Cleaned title: New York Crime Organisé -> New York Crime Organise
2025-05-21T00:26:20.784Z [info]: Enriched New York Crime Organise with TMDb ID: 106158
2025-05-21T00:26:20.784Z [info]: Fetching TMDB seasons data for New York Crime Organise (TMDB ID: 106158)
2025-05-21T00:26:20.784Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=106158
2025-05-21T00:26:20.827Z [info]: Found 5 seasons for TMDB TV series 106158.
2025-05-21T00:26:20.827Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/106158/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:26:22.380Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/106158/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:26:23.964Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/106158/season/3?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:26:25.652Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/106158/season/4?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:26:27.197Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/106158/season/5?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:26:28.837Z [info]: Successfully fetched details for 5 out of 5 seasons for series 106158.
2025-05-21T00:26:28.837Z [info]: Formatted 5 out of 5 seasons for DB.
2025-05-21T00:26:28.837Z [info]: Found 5 TMDB seasons for New York Crime Organise
2025-05-21T00:26:28.838Z [info]: Set tmdbSeason field for season 5
2025-05-21T00:26:28.838Z [info]: TMDB title similarity check: "New York Crime Organisé" vs "New York : Crime organisé" = 0.923
2025-05-21T00:26:28.838Z [info]: TMDB match verified by similarity: 0.923 >= 0.5
2025-05-21T00:26:29.839Z [info]: Seasons count changed for New York Crime Organisé: TMDB (0 -> 5)
2025-05-21T00:26:29.839Z [info]: Updating series (latest mode): New York Crime Organisé - metadata changes detected, updating database
2025-05-21T00:26:29.864Z [info]: Processing series: FBI: International (https://flemmix.ws/serie-en-streaming/31809-fbi-international-saison-4.html)
2025-05-21T00:26:29.865Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/31809-fbi-international-saison-4.html...
2025-05-21T00:26:29.865Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:26:29.865Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/31809-fbi-international-saison-4.html
2025-05-21T00:26:33.365Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:26:33.365Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:26:33.366Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:26:33.369Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:26:33.372Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:26:33.373Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:26:33.375Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:26:33.376Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:26:33.377Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vs"
2025-05-21T00:26:33.378Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vs"
2025-05-21T00:26:33.379Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vs"
2025-05-21T00:26:33.382Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vs"
2025-05-21T00:26:33.385Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 11" with rel="ep11vs"
2025-05-21T00:26:33.387Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 12" with rel="ep12vs"
2025-05-21T00:26:33.388Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 13" with rel="ep13vs"
2025-05-21T00:26:33.389Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 14" with rel="ep14vs"
2025-05-21T00:26:33.390Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 15" with rel="ep15vs"
2025-05-21T00:26:33.392Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 16" with rel="ep16vs"
2025-05-21T00:26:33.393Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 17" with rel="ep17vs"
2025-05-21T00:26:33.394Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 18" with rel="ep18vs"
2025-05-21T00:26:33.395Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 19" with rel="ep19vs"
2025-05-21T00:26:33.397Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 20" with rel="ep20vs"
2025-05-21T00:26:33.400Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 21" with rel="ep21vs"
2025-05-21T00:26:33.401Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:26:33.402Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:26:33.404Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:26:33.405Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:26:33.407Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:26:33.408Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:26:33.409Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:26:33.411Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vf"
2025-05-21T00:26:33.412Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vf"
2025-05-21T00:26:33.415Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vf"
2025-05-21T00:26:33.416Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vf"
2025-05-21T00:26:33.418Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 11" with rel="ep11vf"
2025-05-21T00:26:33.419Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 12" with rel="ep12vf"
2025-05-21T00:26:33.421Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 13" with rel="ep13vf"
2025-05-21T00:26:33.422Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 14" with rel="ep14vf"
2025-05-21T00:26:33.423Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/31809-fbi-international-saison-4.html... | Episodes: 21, Movie Streams: 0
2025-05-21T00:26:33.423Z [info]: Using advanced enrichment for FBI: International
2025-05-21T00:26:33.830Z [info]: Using Gemini-optimized query for 'FBI: International': 'FBI International'
2025-05-21T00:26:33.830Z [info]: Searching TMDB for 'FBI International' (tv)
2025-05-21T00:26:34.018Z [info]: Fetching TMDB details for 'FBI International' (ID: 121658)
2025-05-21T00:26:34.062Z [info]: Cleaned title: FBI International -> FBI International
2025-05-21T00:26:34.062Z [info]: Enriched FBI International with TMDb ID: 121658
2025-05-21T00:26:34.062Z [info]: Fetching TMDB seasons data for FBI International (TMDB ID: 121658)
2025-05-21T00:26:34.063Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=121658
2025-05-21T00:26:34.107Z [info]: Found 4 seasons for TMDB TV series 121658.
2025-05-21T00:26:34.107Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121658/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:26:35.653Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121658/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:26:37.198Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121658/season/3?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:26:38.753Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121658/season/4?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:26:40.401Z [info]: Successfully fetched details for 4 out of 4 seasons for series 121658.
2025-05-21T00:26:40.402Z [info]: Formatted 4 out of 4 seasons for DB.
2025-05-21T00:26:40.402Z [info]: Found 4 TMDB seasons for FBI International
2025-05-21T00:26:40.402Z [info]: Set tmdbSeason field for season 4
2025-05-21T00:26:40.402Z [info]: TMDB title similarity check: "FBI: International" vs "FBI: International" = 1.000
2025-05-21T00:26:40.402Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:26:41.403Z [info]: Seasons count changed for FBI: International: TMDB (0 -> 4)
2025-05-21T00:26:41.403Z [info]: Updating series (latest mode): FBI: International - metadata changes detected, updating database
2025-05-21T00:26:41.418Z [info]: Processing series: Legado (https://flemmix.ws/serie-en-streaming/33581-legado-saison-1.html)
2025-05-21T00:26:41.418Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33581-legado-saison-1.html...
2025-05-21T00:26:41.418Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:26:41.418Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33581-legado-saison-1.html
2025-05-21T00:26:44.307Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:26:44.308Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:26:44.308Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:26:44.309Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:26:44.312Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:26:44.314Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:26:44.316Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:26:44.317Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:26:44.319Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:26:44.321Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vf"
2025-05-21T00:26:44.322Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vf"
2025-05-21T00:26:44.324Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33581-legado-saison-1.html... | Episodes: 8, Movie Streams: 0
2025-05-21T00:26:44.324Z [info]: Using advanced enrichment for Legado
2025-05-21T00:26:44.745Z [info]: Using Gemini-optimized query for 'Legado': 'Legado'
2025-05-21T00:26:44.745Z [info]: Searching TMDB for 'Legado' (tv)
2025-05-21T00:26:44.795Z [info]: Fetching TMDB details for 'Legado' (ID: 248716)
2025-05-21T00:26:44.856Z [info]: Cleaned title: Legado -> Legado
2025-05-21T00:26:44.856Z [info]: Enriched Legado with TMDb ID: 248716
2025-05-21T00:26:44.856Z [info]: Fetching TMDB seasons data for Legado (TMDB ID: 248716)
2025-05-21T00:26:44.856Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=248716
2025-05-21T00:26:44.901Z [info]: Found 1 seasons for TMDB TV series 248716.
2025-05-21T00:26:44.901Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/248716/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:26:46.542Z [info]: Successfully fetched details for 1 out of 1 seasons for series 248716.
2025-05-21T00:26:46.543Z [info]: Formatted 1 out of 1 seasons for DB.
2025-05-21T00:26:46.543Z [info]: Found 1 TMDB seasons for Legado
2025-05-21T00:26:46.543Z [info]: Set tmdbSeason field for season 1
2025-05-21T00:26:46.544Z [info]: TMDB title similarity check: "Legado" vs "Legado" = 1.000
2025-05-21T00:26:46.544Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:26:47.544Z [info]: Seasons count changed for Legado: TMDB (0 -> 1)
2025-05-21T00:26:47.544Z [info]: Updating series (latest mode): Legado - metadata changes detected, updating database
2025-05-21T00:26:47.559Z [info]: Processing series: Lazarus (https://flemmix.ws/serie-en-streaming/33244-lazarus-saison-1.html)
2025-05-21T00:26:47.559Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33244-lazarus-saison-1.html...
2025-05-21T00:26:47.559Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:26:47.559Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33244-lazarus-saison-1.html
2025-05-21T00:26:50.868Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:26:50.868Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:26:50.869Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:26:50.871Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:26:50.872Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:26:50.874Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:26:50.876Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:26:50.877Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:26:50.879Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vs"
2025-05-21T00:26:50.880Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:26:50.880Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:26:50.881Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:26:50.884Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:26:50.886Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:26:50.888Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:26:50.889Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:26:50.891Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vf"
2025-05-21T00:26:50.892Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33244-lazarus-saison-1.html... | Episodes: 7, Movie Streams: 0
2025-05-21T00:26:50.893Z [info]: Using advanced enrichment for Lazarus
2025-05-21T00:26:51.330Z [info]: Using Gemini-optimized query for 'Lazarus': 'Lazarus'
2025-05-21T00:26:51.330Z [info]: Searching TMDB for 'Lazarus' (tv)
2025-05-21T00:26:51.373Z [info]: Fetching TMDB details for 'Lazarus' (ID: 231003)
2025-05-21T00:26:51.420Z [info]: Cleaned title: Lazarus -> Lazarus
2025-05-21T00:26:51.420Z [info]: Enriched Lazarus with TMDb ID: 231003
2025-05-21T00:26:51.420Z [info]: Fetching TMDB seasons data for Lazarus (TMDB ID: 231003)
2025-05-21T00:26:51.420Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=231003
2025-05-21T00:26:51.464Z [info]: Found 1 seasons for TMDB TV series 231003.
2025-05-21T00:26:51.464Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/231003/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:26:53.112Z [info]: Successfully fetched details for 1 out of 1 seasons for series 231003.
2025-05-21T00:26:53.113Z [info]: Formatted 1 out of 1 seasons for DB.
2025-05-21T00:26:53.113Z [info]: Found 1 TMDB seasons for Lazarus
2025-05-21T00:26:53.113Z [info]: Set tmdbSeason field for season 1
2025-05-21T00:26:53.113Z [info]: TMDB title similarity check: "Lazarus" vs "Lazarus" = 1.000
2025-05-21T00:26:53.114Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:26:54.115Z [info]: Seasons count changed for Lazarus: TMDB (0 -> 1)
2025-05-21T00:26:54.115Z [info]: Updating series (latest mode): Lazarus - metadata changes detected, updating database
2025-05-21T00:26:54.142Z [info]: Processing series: NCIS : Enquêtes spéciales (https://flemmix.ws/serie-en-streaming/31773-ncis-enquetes-speciales-saison-22.html)
2025-05-21T00:26:54.142Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/31773-ncis-enquetes-speciales-saison-22.html...
2025-05-21T00:26:54.142Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:26:54.142Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/31773-ncis-enquetes-speciales-saison-22.html
2025-05-21T00:26:58.212Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:26:58.213Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:26:58.214Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:26:58.216Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:26:58.218Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:26:58.221Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:26:58.222Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:26:58.224Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:26:58.225Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vs"
2025-05-21T00:26:58.227Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vs"
2025-05-21T00:26:58.230Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vs"
2025-05-21T00:26:58.232Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vs"
2025-05-21T00:26:58.234Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 11" with rel="ep11vs"
2025-05-21T00:26:58.236Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 12" with rel="ep12vs"
2025-05-21T00:26:58.239Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 13" with rel="ep13vs"
2025-05-21T00:26:58.241Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 14" with rel="ep14vs"
2025-05-21T00:26:58.243Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 15" with rel="ep15vs"
2025-05-21T00:26:58.245Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 16" with rel="ep16vs"
2025-05-21T00:26:58.247Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 17" with rel="ep17vs"
2025-05-21T00:26:58.249Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 18" with rel="ep18vs"
2025-05-21T00:26:58.251Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 19" with rel="ep19vs"
2025-05-21T00:26:58.253Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 20" with rel="ep20vs"
2025-05-21T00:26:58.255Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:26:58.255Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:26:58.257Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:26:58.258Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:26:58.260Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:26:58.262Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:26:58.265Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:26:58.267Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vf"
2025-05-21T00:26:58.268Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vf"
2025-05-21T00:26:58.271Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vf"
2025-05-21T00:26:58.272Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vf"
2025-05-21T00:26:58.274Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 11" with rel="ep11vf"
2025-05-21T00:26:58.275Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 12" with rel="ep12vf"
2025-05-21T00:26:58.277Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 13" with rel="ep13vf"
2025-05-21T00:26:58.278Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 14" with rel="ep14vf"
2025-05-21T00:26:58.280Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 15" with rel="ep15vf"
2025-05-21T00:26:58.282Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 16" with rel="ep16vf"
2025-05-21T00:26:58.284Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 17" with rel="ep17vf"
2025-05-21T00:26:58.286Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/31773-ncis-enquetes-speciales-saison-22.html... | Episodes: 20, Movie Streams: 0
2025-05-21T00:26:58.286Z [info]: Using advanced enrichment for NCIS : Enquêtes spéciales
2025-05-21T00:26:59.263Z [info]: Using Gemini-optimized query for 'NCIS : Enquêtes spéciales': 'NCIS: Enquêtes spéciales'
2025-05-21T00:26:59.263Z [info]: Searching TMDB for 'NCIS: Enquetes speciales' (tv)
2025-05-21T00:26:59.477Z [info]: Fetching TMDB details for 'NCIS: Enquetes speciales' (ID: 4614)
2025-05-21T00:26:59.533Z [info]: Cleaned title: NCIS: Enquêtes spéciales -> NCIS: Enquetes speciales
2025-05-21T00:26:59.533Z [info]: Enriched NCIS: Enquetes speciales with TMDb ID: 4614
2025-05-21T00:26:59.534Z [info]: Fetching TMDB seasons data for NCIS: Enquetes speciales (TMDB ID: 4614)
2025-05-21T00:26:59.534Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=4614
2025-05-21T00:26:59.579Z [info]: Found 23 seasons for TMDB TV series 4614.
2025-05-21T00:26:59.579Z [warn]: Missing required parameters for fetchTmdbSeason: tmdbId=4614, seasonNumber=0
2025-05-21T00:27:01.080Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:02.642Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:04.313Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/3?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:05.962Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/4?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:07.518Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/5?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:09.062Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/6?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:10.875Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/7?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:12.430Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/8?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:13.977Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/9?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:15.564Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/10?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:17.117Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/11?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:18.663Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/12?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:20.267Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/13?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:21.812Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/14?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:23.418Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/15?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:24.962Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/16?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:26.524Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/17?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:28.077Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/18?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:29.736Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/19?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:31.376Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/20?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:33.024Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/21?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:34.578Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/4614/season/22?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:36.266Z [info]: Successfully fetched details for 22 out of 23 seasons for series 4614.
2025-05-21T00:27:36.266Z [info]: Formatted 22 out of 22 seasons for DB.
2025-05-21T00:27:36.276Z [info]: Found 22 TMDB seasons for NCIS: Enquetes speciales
2025-05-21T00:27:36.276Z [info]: Set tmdbSeason field for season 22
2025-05-21T00:27:36.277Z [info]: TMDB title similarity check: "NCIS : Enquêtes spéciales" vs "NCIS : Enquêtes spéciales" = 1.000
2025-05-21T00:27:36.277Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:27:37.278Z [info]: Seasons count changed for NCIS : Enquêtes spéciales: TMDB (0 -> 22)
2025-05-21T00:27:37.278Z [info]: Updating series (latest mode): NCIS : Enquêtes spéciales - metadata changes detected, updating database
2025-05-21T00:27:37.294Z [info]: Processing series: The Chi (https://flemmix.ws/serie-en-streaming/33576-the-chi-saison-7.html)
2025-05-21T00:27:37.294Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33576-the-chi-saison-7.html...
2025-05-21T00:27:37.294Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:27:37.294Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33576-the-chi-saison-7.html
2025-05-21T00:27:40.314Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:27:40.315Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:27:40.315Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:27:40.315Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:27:40.317Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33576-the-chi-saison-7.html... | Episodes: 1, Movie Streams: 0
2025-05-21T00:27:40.317Z [info]: Using advanced enrichment for The Chi
2025-05-21T00:27:40.744Z [info]: Using Gemini-optimized query for 'The Chi': 'The Chi'
2025-05-21T00:27:40.745Z [info]: Searching TMDB for 'The Chi' (tv)
2025-05-21T00:27:40.796Z [info]: Fetching TMDB details for 'The Chi' (ID: 72071)
2025-05-21T00:27:40.849Z [info]: Cleaned title: The Chi -> The Chi
2025-05-21T00:27:40.850Z [info]: Enriched The Chi with TMDb ID: 72071
2025-05-21T00:27:40.850Z [info]: Fetching TMDB seasons data for The Chi (TMDB ID: 72071)
2025-05-21T00:27:40.851Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=72071
2025-05-21T00:27:40.898Z [info]: Found 8 seasons for TMDB TV series 72071.
2025-05-21T00:27:40.898Z [warn]: Missing required parameters for fetchTmdbSeason: tmdbId=72071, seasonNumber=0
2025-05-21T00:27:42.399Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/72071/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:44.033Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/72071/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:45.686Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/72071/season/3?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:47.247Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/72071/season/4?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:48.904Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/72071/season/5?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:50.448Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/72071/season/6?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:52.009Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/72071/season/7?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:27:53.553Z [info]: Successfully fetched details for 7 out of 8 seasons for series 72071.
2025-05-21T00:27:53.553Z [info]: Formatted 7 out of 7 seasons for DB.
2025-05-21T00:27:53.553Z [info]: Found 7 TMDB seasons for The Chi
2025-05-21T00:27:53.553Z [info]: Set tmdbSeason field for season 7
2025-05-21T00:27:53.554Z [info]: TMDB title similarity check: "The Chi" vs "The Chi" = 1.000
2025-05-21T00:27:53.554Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:27:54.555Z [info]: Seasons count changed for The Chi: TMDB (0 -> 7)
2025-05-21T00:27:54.555Z [info]: Updating series (latest mode): The Chi - metadata changes detected, updating database
2025-05-21T00:27:54.571Z [info]: Processing series: Le Renard rouge (https://flemmix.ws/serie-en-streaming/33595-le-renard-rouge-saison-1.html)
2025-05-21T00:27:54.571Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33595-le-renard-rouge-saison-1.html...
2025-05-21T00:27:54.571Z [info]: [Wiflix Detail Final] Special handling for problematic URL: https://flemmix.ws/serie-en-streaming/33595-le-renard-rouge-saison-1.html
2025-05-21T00:27:54.571Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 120000ms
2025-05-21T00:27:54.571Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33595-le-renard-rouge-saison-1.html
2025-05-21T00:27:57.911Z [info]: [Wiflix Detail Final] Detailed episode block analysis for problematic URL
2025-05-21T00:27:57.911Z [info]: [Wiflix Detail Final] Episode blocks count: 2
2025-05-21T00:27:57.916Z [info]: [Wiflix Detail Final] All episode blocks: 

		<!-- <div class="sadsb">
			
		<script type="text/javascript" data-cfasync="false" src="https://tagbucket.cc/_tags/jstags.js?s=fr/flemmix/72890"></script>
		</div> -->
		
		<div class="sadst" styl...
2025-05-21T00:27:57.918Z [info]: [Wiflix Detail Final] Rel attributes: ep1vs, ep2vs, ep3vs, ep4vs
2025-05-21T00:27:57.919Z [info]: [Wiflix Detail Final] Host block for ep1vs exists: true
2025-05-21T00:27:57.920Z [info]: [Wiflix Detail Final] Links in ep1vs: 3
2025-05-21T00:27:57.921Z [info]: [Wiflix Detail Final] Host block for ep2vs exists: true
2025-05-21T00:27:57.922Z [info]: [Wiflix Detail Final] Links in ep2vs: 3
2025-05-21T00:27:57.923Z [info]: [Wiflix Detail Final] Host block for ep3vs exists: true
2025-05-21T00:27:57.924Z [info]: [Wiflix Detail Final] Links in ep3vs: 3
2025-05-21T00:27:57.925Z [info]: [Wiflix Detail Final] Host block for ep4vs exists: true
2025-05-21T00:27:57.927Z [info]: [Wiflix Detail Final] Links in ep4vs: 3
2025-05-21T00:27:57.927Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:27:57.927Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:27:57.928Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:27:57.929Z [info]: [Wiflix Detail Final] Processing link: onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo('https://do7go.com/e/dnfjvrtbkpks')", hostText="Lecteur 1"
2025-05-21T00:27:57.929Z [info]: [Wiflix Detail Final] Added streaming URL: https://do7go.com/e/dnfjvrtbkpks (Do7go)
2025-05-21T00:27:57.929Z [info]: [Wiflix Detail Final] Processing link: onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo('https://magasavor.net/e/gkarbzunnwae')", hostText="Lecteur 2"
2025-05-21T00:27:57.930Z [info]: [Wiflix Detail Final] Added streaming URL: https://magasavor.net/e/gkarbzunnwae (Magasavor)
2025-05-21T00:27:57.930Z [info]: [Wiflix Detail Final] Processing link: onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo('https://uqload.net/embed-orti9zequs7o.html')", hostText="Lecteur 3"
2025-05-21T00:27:57.930Z [info]: [Wiflix Detail Final] Added streaming URL: https://uqload.net/embed-orti9zequs7o.html (Uqload)
2025-05-21T00:27:57.930Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:27:57.931Z [info]: [Wiflix Detail Final] Processing link: onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo('https://do7go.com/e/wynbzqez19r7')", hostText="Lecteur 1"
2025-05-21T00:27:57.931Z [info]: [Wiflix Detail Final] Added streaming URL: https://do7go.com/e/wynbzqez19r7 (Do7go)
2025-05-21T00:27:57.931Z [info]: [Wiflix Detail Final] Processing link: onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo('https://magasavor.net/e/x7ysmyriy7kn')", hostText="Lecteur 2"
2025-05-21T00:27:57.931Z [info]: [Wiflix Detail Final] Added streaming URL: https://magasavor.net/e/x7ysmyriy7kn (Magasavor)
2025-05-21T00:27:57.932Z [info]: [Wiflix Detail Final] Processing link: onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo('https://uqload.net/embed-7hy2orc56p3c.html')", hostText="Lecteur 3"
2025-05-21T00:27:57.932Z [info]: [Wiflix Detail Final] Added streaming URL: https://uqload.net/embed-7hy2orc56p3c.html (Uqload)
2025-05-21T00:27:57.932Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:27:57.933Z [info]: [Wiflix Detail Final] Processing link: onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo('https://do7go.com/e/0u4hr40m3s9n')", hostText="Lecteur 1"
2025-05-21T00:27:57.933Z [info]: [Wiflix Detail Final] Added streaming URL: https://do7go.com/e/0u4hr40m3s9n (Do7go)
2025-05-21T00:27:57.933Z [info]: [Wiflix Detail Final] Processing link: onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo('https://magasavor.net/e/miqycxddd3bv')", hostText="Lecteur 2"
2025-05-21T00:27:57.933Z [info]: [Wiflix Detail Final] Added streaming URL: https://magasavor.net/e/miqycxddd3bv (Magasavor)
2025-05-21T00:27:57.933Z [info]: [Wiflix Detail Final] Processing link: onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo('https://uqload.net/embed-sz2nn3irsasc.html')", hostText="Lecteur 3"
2025-05-21T00:27:57.934Z [info]: [Wiflix Detail Final] Added streaming URL: https://uqload.net/embed-sz2nn3irsasc.html (Uqload)
2025-05-21T00:27:57.934Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:27:57.935Z [info]: [Wiflix Detail Final] Processing link: onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo('https://do7go.com/e/84huzgtp99nl')", hostText="Lecteur 1"
2025-05-21T00:27:57.935Z [info]: [Wiflix Detail Final] Added streaming URL: https://do7go.com/e/84huzgtp99nl (Do7go)
2025-05-21T00:27:57.935Z [info]: [Wiflix Detail Final] Processing link: onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo('https://magasavor.net/e/6nrwtcnochx1')", hostText="Lecteur 2"
2025-05-21T00:27:57.935Z [info]: [Wiflix Detail Final] Added streaming URL: https://magasavor.net/e/6nrwtcnochx1 (Magasavor)
2025-05-21T00:27:57.935Z [info]: [Wiflix Detail Final] Processing link: onclick="if (!window.__cfRLUnblockHandlers) return false; loadVideo('https://uqload.net/embed-butpl3s2scil.html')", hostText="Lecteur 3"
2025-05-21T00:27:57.935Z [info]: [Wiflix Detail Final] Added streaming URL: https://uqload.net/embed-butpl3s2scil.html (Uqload)
2025-05-21T00:27:57.935Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:27:57.935Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33595-le-renard-rouge-saison-1.html... | Episodes: 4, Movie Streams: 0
2025-05-21T00:27:57.935Z [info]: [Wiflix Detail Final] Detailed results for problematic URL:
2025-05-21T00:27:57.936Z [info]: [Wiflix Detail Final] Episodes found: S1:E1 (3 streams), S1:E2 (3 streams), S1:E3 (3 streams), S1:E4 (3 streams)
2025-05-21T00:27:57.936Z [warn]: [Wiflix Detail Final] No metadata found for problematic URL
2025-05-21T00:27:57.936Z [info]: Using advanced enrichment for Le Renard rouge
2025-05-21T00:27:58.412Z [info]: Using Gemini-optimized query for 'Le Renard rouge': 'Le Renard rouge'
2025-05-21T00:27:58.413Z [info]: Searching TMDB for 'Le Renard rouge' (tv)
2025-05-21T00:27:58.455Z [warn]: No TMDb match for Le Renard rouge (tv)
2025-05-21T00:27:58.455Z [info]: Cleaned title: Le Renard rouge -> Le Renard rouge
2025-05-21T00:27:58.455Z [warn]: No TMDb match for Le Renard rouge (type: tv, year: none)
2025-05-21T00:27:59.456Z [info]: Updating series (latest mode): Le Renard rouge - No new episodes but updating metadata/seasons
2025-05-21T00:27:59.485Z [info]: Processing series: 9-1-1 (2018) (https://flemmix.ws/serie-en-streaming/31630-9-1-1-2018-saison-8.html)
2025-05-21T00:27:59.485Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/31630-9-1-1-2018-saison-8.html...
2025-05-21T00:27:59.485Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:27:59.485Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/31630-9-1-1-2018-saison-8.html
2025-05-21T00:28:02.974Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:28:02.974Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:28:02.976Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:28:02.981Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:28:02.983Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:28:02.985Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:28:02.998Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:28:03.001Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:28:03.002Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vs"
2025-05-21T00:28:03.004Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vs"
2025-05-21T00:28:03.007Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vs"
2025-05-21T00:28:03.010Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vs"
2025-05-21T00:28:03.012Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 11" with rel="ep11vs"
2025-05-21T00:28:03.014Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 12" with rel="ep12vs"
2025-05-21T00:28:03.016Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 13" with rel="ep13vs"
2025-05-21T00:28:03.018Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 14" with rel="ep14vs"
2025-05-21T00:28:03.020Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 15" with rel="ep15vs"
2025-05-21T00:28:03.022Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 16" with rel="ep16vs"
2025-05-21T00:28:03.024Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 17" with rel="ep17vs"
2025-05-21T00:28:03.027Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 18" with rel="ep18vs"
2025-05-21T00:28:03.030Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:28:03.030Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:28:03.033Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:28:03.035Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:28:03.037Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:28:03.038Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:28:03.040Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:28:03.043Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vf"
2025-05-21T00:28:03.047Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vf"
2025-05-21T00:28:03.050Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vf"
2025-05-21T00:28:03.052Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 11" with rel="ep11vf"
2025-05-21T00:28:03.055Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/31630-9-1-1-2018-saison-8.html... | Episodes: 18, Movie Streams: 0
2025-05-21T00:28:03.055Z [info]: Using advanced enrichment for 9-1-1 (2018)
2025-05-21T00:28:03.391Z [info]: Using Gemini-optimized query for '9-1-1 (2018)': '9-1-1'
2025-05-21T00:28:03.391Z [info]: Searching TMDB for '9-1-1' (tv)
2025-05-21T00:28:03.536Z [info]: Fetching TMDB details for '9-1-1' (ID: 89393)
2025-05-21T00:28:03.677Z [info]: Cleaned title: 9-1-1 -> 9-1-1
2025-05-21T00:28:03.678Z [info]: Enriched 9-1-1 with TMDb ID: 89393
2025-05-21T00:28:03.678Z [info]: Fetching TMDB seasons data for 9-1-1 (TMDB ID: 89393)
2025-05-21T00:28:03.678Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=89393
2025-05-21T00:28:03.828Z [info]: Found 5 seasons for TMDB TV series 89393.
2025-05-21T00:28:03.828Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/89393/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:05.381Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/89393/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:07.077Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/89393/season/3?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:08.630Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/89393/season/4?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:10.265Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/89393/season/5?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:11.809Z [info]: Successfully fetched details for 5 out of 5 seasons for series 89393.
2025-05-21T00:28:11.809Z [info]: Formatted 5 out of 5 seasons for DB.
2025-05-21T00:28:11.809Z [info]: Found 5 TMDB seasons for 9-1-1
2025-05-21T00:28:11.809Z [info]: TMDB title similarity check: "9-1-1 (2018)" vs "9-1-1 : Lone Star" = 0.348
2025-05-21T00:28:11.809Z [info]: Asking Gemini for final verdict: Does the title "9-1-1 (2018)" likely refer to the same series as the TMDB entry with ID 89393 which has a title of "9-1-1 : Lone Star"? Consider potential translations or variations. Respond with ONLY 'YES' or 'NO'.
2025-05-21T00:28:12.201Z [info]: Gemini Verdict Response: "NO"
2025-05-21T00:28:12.201Z [info]: TMDB match verified by Gemini: false
2025-05-21T00:28:12.201Z [warn]: TMDB match rejected for '9-1-1 (2018)'. Attempting with English translation...
2025-05-21T00:28:12.202Z [info]: Translation Utils: Gemini AI initialized.
2025-05-21T00:28:12.202Z [info]: Translation Utils: Translating "9-1-1 (2018)" to English...
2025-05-21T00:28:12.563Z [info]: Translation Utils: Translated "9-1-1 (2018)" to "9-1-1 (2018)"
2025-05-21T00:28:12.563Z [warn]: TMDB match rejected for '9-1-1 (2018)' even after translation attempt. Clearing TMDB data.
2025-05-21T00:28:13.564Z [info]: Updating series (latest mode): 9-1-1 (2018) - No new episodes but updating metadata/seasons
2025-05-21T00:28:13.577Z [info]: Processing series: Catch Me a Killer (https://flemmix.ws/serie-en-streaming/33180-catch-me-a-killer-saison-1.html)
2025-05-21T00:28:13.578Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33180-catch-me-a-killer-saison-1.html...
2025-05-21T00:28:13.578Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:28:13.578Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33180-catch-me-a-killer-saison-1.html
2025-05-21T00:28:16.960Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:28:16.960Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:28:16.960Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:28:16.962Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:28:16.963Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:28:16.964Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:28:16.966Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:28:16.968Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:28:16.969Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vs"
2025-05-21T00:28:16.970Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vs"
2025-05-21T00:28:16.971Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:28:16.971Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:28:16.972Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:28:16.973Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:28:16.975Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:28:16.976Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:28:16.978Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:28:16.979Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vf"
2025-05-21T00:28:16.980Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vf"
2025-05-21T00:28:16.982Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33180-catch-me-a-killer-saison-1.html... | Episodes: 8, Movie Streams: 0
2025-05-21T00:28:16.982Z [info]: Using advanced enrichment for Catch Me a Killer
2025-05-21T00:28:17.422Z [info]: Using Gemini-optimized query for 'Catch Me a Killer': 'Catch Me a Killer'
2025-05-21T00:28:17.423Z [info]: Searching TMDB for 'Catch Me a Killer' (tv)
2025-05-21T00:28:17.581Z [info]: Fetching TMDB details for 'Catch Me a Killer' (ID: 244596)
2025-05-21T00:28:17.726Z [info]: Cleaned title: Catch Me a Killer -> Catch Me a Killer
2025-05-21T00:28:17.727Z [info]: Enriched Catch Me a Killer with TMDb ID: 244596
2025-05-21T00:28:17.727Z [info]: Fetching TMDB seasons data for Catch Me a Killer (TMDB ID: 244596)
2025-05-21T00:28:17.727Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=244596
2025-05-21T00:28:17.863Z [info]: Found 1 seasons for TMDB TV series 244596.
2025-05-21T00:28:17.863Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/244596/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:19.413Z [info]: Successfully fetched details for 1 out of 1 seasons for series 244596.
2025-05-21T00:28:19.413Z [info]: Formatted 1 out of 1 seasons for DB.
2025-05-21T00:28:19.413Z [info]: Found 1 TMDB seasons for Catch Me a Killer
2025-05-21T00:28:19.413Z [info]: Set tmdbSeason field for season 1
2025-05-21T00:28:19.413Z [info]: TMDB title similarity check: "Catch Me a Killer" vs "La profileuse" = 0.167
2025-05-21T00:28:19.414Z [info]: Asking Gemini for final verdict: Does the title "Catch Me a Killer" likely refer to the same series as the TMDB entry with ID 244596 which has a title of "La profileuse"? Consider potential translations or variations. Respond with ONLY 'YES' or 'NO'.
2025-05-21T00:28:19.766Z [info]: Gemini Verdict Response: "NO"
2025-05-21T00:28:19.766Z [info]: TMDB match verified by Gemini: false
2025-05-21T00:28:19.766Z [warn]: TMDB match rejected for 'Catch Me a Killer'. Attempting with English translation...
2025-05-21T00:28:19.767Z [info]: Translation Utils: Translating "Catch Me a Killer" to English...
2025-05-21T00:28:20.143Z [info]: Translation Utils: Translated "Catch Me a Killer" to "Catch Me a Killer"
2025-05-21T00:28:20.144Z [warn]: TMDB match rejected for 'Catch Me a Killer' even after translation attempt. Clearing TMDB data.
2025-05-21T00:28:21.144Z [info]: Updating series (latest mode): Catch Me a Killer - No new episodes but updating metadata/seasons
2025-05-21T00:28:21.171Z [info]: Processing series: Doctor Who (2024) (https://flemmix.ws/serie-en-streaming/32360-doctor-who-2024-saison-2.html)
2025-05-21T00:28:21.171Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/32360-doctor-who-2024-saison-2.html...
2025-05-21T00:28:21.171Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:28:21.171Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/32360-doctor-who-2024-saison-2.html
2025-05-21T00:28:24.586Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:28:24.587Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:28:24.589Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 00" with rel="ep00vs"
2025-05-21T00:28:24.596Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:28:24.605Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:28:24.609Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:28:24.620Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:28:24.624Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:28:24.627Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:28:24.646Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:28:24.646Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 00" with rel="ep00vf"
2025-05-21T00:28:24.654Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:28:24.658Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:28:24.661Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:28:24.666Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:28:24.669Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:28:24.675Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:28:24.679Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/32360-doctor-who-2024-saison-2.html... | Episodes: 7, Movie Streams: 0
2025-05-21T00:28:24.680Z [info]: Using advanced enrichment for Doctor Who (2024)
2025-05-21T00:28:25.062Z [info]: Using Gemini-optimized query for 'Doctor Who (2024)': 'Doctor Who'
2025-05-21T00:28:25.062Z [info]: Searching TMDB for 'Doctor Who' (tv)
2025-05-21T00:28:25.137Z [info]: Fetching TMDB details for 'Doctor Who' (ID: 121)
2025-05-21T00:28:25.279Z [info]: Cleaned title: Doctor Who -> Doctor Who
2025-05-21T00:28:25.279Z [info]: Enriched Doctor Who with TMDb ID: 121
2025-05-21T00:28:25.279Z [info]: Fetching TMDB seasons data for Doctor Who (TMDB ID: 121)
2025-05-21T00:28:25.279Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=121
2025-05-21T00:28:25.427Z [info]: Found 27 seasons for TMDB TV series 121.
2025-05-21T00:28:25.427Z [warn]: Missing required parameters for fetchTmdbSeason: tmdbId=121, seasonNumber=0
2025-05-21T00:28:26.928Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:28.571Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:30.224Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/3?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:31.778Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/4?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:33.324Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/5?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:34.881Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/6?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:36.532Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/7?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:38.083Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/8?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:39.756Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/9?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:41.431Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/10?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:43.080Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/11?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:44.739Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/12?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:46.282Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/13?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:47.920Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/14?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:49.478Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/15?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:51.140Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/16?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:52.686Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/17?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:54.232Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/18?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:55.787Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/19?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:57.442Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/20?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:28:59.000Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/21?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:29:00.636Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/22?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:29:02.180Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/23?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:29:03.748Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/24?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:29:05.298Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/25?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:29:06.936Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/121/season/26?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:29:08.481Z [info]: Successfully fetched details for 26 out of 27 seasons for series 121.
2025-05-21T00:29:08.481Z [info]: Formatted 26 out of 26 seasons for DB.
2025-05-21T00:29:08.482Z [info]: Found 26 TMDB seasons for Doctor Who
2025-05-21T00:29:08.482Z [info]: Set tmdbSeason field for season 2
2025-05-21T00:29:08.482Z [info]: TMDB title similarity check: "Doctor Who (2024)" vs "Doctor Who" = 0.727
2025-05-21T00:29:08.482Z [info]: TMDB match verified by similarity: 0.727 >= 0.5
2025-05-21T00:29:09.483Z [info]: Seasons count changed for Doctor Who (2024): TMDB (0 -> 26)
2025-05-21T00:29:09.484Z [info]: Updating series (latest mode): Doctor Who (2024) - metadata changes detected, updating database
2025-05-21T00:29:09.504Z [info]: Processing series: Accused US (https://flemmix.ws/serie-en-streaming/33434-accused-us-saison-2.html)
2025-05-21T00:29:09.505Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33434-accused-us-saison-2.html...
2025-05-21T00:29:09.505Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:29:09.505Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33434-accused-us-saison-2.html
2025-05-21T00:29:12.816Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:29:12.817Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:29:12.817Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:29:12.820Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:29:12.822Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:29:12.825Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:29:12.825Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:29:12.827Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:29:12.829Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:29:12.831Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33434-accused-us-saison-2.html... | Episodes: 3, Movie Streams: 0
2025-05-21T00:29:12.831Z [info]: Using advanced enrichment for Accused US
2025-05-21T00:29:13.248Z [info]: Using Gemini-optimized query for 'Accused US': 'Accused US'
2025-05-21T00:29:13.249Z [info]: Searching TMDB for 'Accused US' (tv)
2025-05-21T00:29:13.379Z [warn]: No TMDb match for Accused US (tv)
2025-05-21T00:29:13.379Z [info]: Cleaned title: Accused US -> Accused US
2025-05-21T00:29:13.379Z [warn]: No TMDb match for Accused US (type: tv, year: none)
2025-05-21T00:29:14.379Z [info]: Updating series (latest mode): Accused US - No new episodes but updating metadata/seasons
2025-05-21T00:29:14.401Z [info]: Processing series: SEAL Team (https://flemmix.ws/serie-en-streaming/31295-seal-team-saison-7.html)
2025-05-21T00:29:14.402Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/31295-seal-team-saison-7.html...
2025-05-21T00:29:14.402Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:29:14.402Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/31295-seal-team-saison-7.html
2025-05-21T00:29:17.560Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:29:17.560Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:29:17.562Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:29:17.565Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:29:17.567Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:29:17.569Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:29:17.571Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:29:17.574Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:29:17.577Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vs"
2025-05-21T00:29:17.579Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vs"
2025-05-21T00:29:17.582Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vs"
2025-05-21T00:29:17.594Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vs"
2025-05-21T00:29:17.596Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:29:17.597Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:29:17.599Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:29:17.601Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/31295-seal-team-saison-7.html... | Episodes: 10, Movie Streams: 0
2025-05-21T00:29:17.602Z [info]: Using advanced enrichment for SEAL Team
2025-05-21T00:29:17.930Z [info]: Using Gemini-optimized query for 'SEAL Team': 'SEAL Team'
2025-05-21T00:29:17.930Z [info]: Searching TMDB for 'SEAL Team' (tv)
2025-05-21T00:29:18.075Z [info]: Fetching TMDB details for 'SEAL Team' (ID: 71789)
2025-05-21T00:29:18.121Z [info]: Cleaned title: SEAL Team -> SEAL Team
2025-05-21T00:29:18.121Z [info]: Enriched SEAL Team with TMDb ID: 71789
2025-05-21T00:29:18.122Z [info]: Fetching TMDB seasons data for SEAL Team (TMDB ID: 71789)
2025-05-21T00:29:18.122Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=71789
2025-05-21T00:29:18.164Z [info]: Found 7 seasons for TMDB TV series 71789.
2025-05-21T00:29:18.164Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/71789/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:29:19.808Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/71789/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:29:21.361Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/71789/season/3?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:29:23.216Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/71789/season/4?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:29:24.782Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/71789/season/5?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:29:26.346Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/71789/season/6?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:29:28.013Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/71789/season/7?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:29:29.556Z [info]: Successfully fetched details for 7 out of 7 seasons for series 71789.
2025-05-21T00:29:29.557Z [info]: Formatted 7 out of 7 seasons for DB.
2025-05-21T00:29:29.557Z [info]: Found 7 TMDB seasons for SEAL Team
2025-05-21T00:29:29.557Z [info]: Set tmdbSeason field for season 7
2025-05-21T00:29:29.557Z [info]: TMDB title similarity check: "SEAL Team" vs "SEAL Team" = 1.000
2025-05-21T00:29:29.557Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:29:30.559Z [info]: Seasons count changed for SEAL Team: TMDB (0 -> 7)
2025-05-21T00:29:30.560Z [info]: Updating series (latest mode): SEAL Team - metadata changes detected, updating database
2025-05-21T00:29:30.583Z [info]: Processing series: Skymed (https://flemmix.ws/serie-en-streaming/33061-skymed-saison-2.html)
2025-05-21T00:29:30.583Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33061-skymed-saison-2.html...
2025-05-21T00:29:30.584Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:29:30.584Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33061-skymed-saison-2.html
2025-05-21T00:29:33.952Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:29:33.953Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:29:33.953Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:29:33.957Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:29:33.960Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:29:33.963Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:29:33.966Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:29:33.968Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:29:33.971Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vs"
2025-05-21T00:29:33.974Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vs"
2025-05-21T00:29:33.977Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vs"
2025-05-21T00:29:33.980Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:29:33.980Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:29:33.982Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:29:33.985Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:29:33.987Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:29:33.990Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:29:34.001Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:29:34.003Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vf"
2025-05-21T00:29:34.005Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vf"
2025-05-21T00:29:34.007Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vf"
2025-05-21T00:29:34.010Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33061-skymed-saison-2.html... | Episodes: 9, Movie Streams: 0
2025-05-21T00:29:34.010Z [info]: Using advanced enrichment for Skymed
2025-05-21T00:29:34.436Z [info]: Using Gemini-optimized query for 'Skymed': 'Skymed'
2025-05-21T00:29:34.437Z [info]: Searching TMDB for 'Skymed' (tv)
2025-05-21T00:29:34.477Z [info]: Fetching TMDB details for 'Skymed' (ID: 133908)
2025-05-21T00:29:34.519Z [info]: Cleaned title: Skymed -> Skymed
2025-05-21T00:29:34.519Z [info]: Enriched Skymed with TMDb ID: 133908
2025-05-21T00:29:34.519Z [info]: Fetching TMDB seasons data for Skymed (TMDB ID: 133908)
2025-05-21T00:29:34.519Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=133908
2025-05-21T00:29:34.567Z [info]: Found 3 seasons for TMDB TV series 133908.
2025-05-21T00:29:34.567Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/133908/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:29:36.110Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/133908/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:29:37.652Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/133908/season/3?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:29:39.195Z [info]: Successfully fetched details for 3 out of 3 seasons for series 133908.
2025-05-21T00:29:39.196Z [info]: Formatted 3 out of 3 seasons for DB.
2025-05-21T00:29:39.196Z [info]: Found 3 TMDB seasons for Skymed
2025-05-21T00:29:39.197Z [info]: Set tmdbSeason field for season 3
2025-05-21T00:29:39.197Z [info]: TMDB title similarity check: "Skymed" vs "SkyMed" = 1.000
2025-05-21T00:29:39.198Z [info]: TMDB match verified by similarity: 1.000 >= 0.5
2025-05-21T00:29:40.200Z [info]: Seasons count changed for Skymed: TMDB (0 -> 3)
2025-05-21T00:29:40.201Z [info]: Updating series (latest mode): Skymed - metadata changes detected, updating database
2025-05-21T00:29:40.220Z [info]: Processing series: Plus belle la vie, encore plus belle (https://flemmix.ws/serie-en-streaming/33538-plus-belle-la-vie-encore-plus-belle-saison-2-part-4.html)
2025-05-21T00:29:40.220Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33538-plus-belle-la-vie-encore-plus-belle-saison-2-part-4.html...
2025-05-21T00:29:40.220Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:29:40.220Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33538-plus-belle-la-vie-encore-plus-belle-saison-2-part-4.html
2025-05-21T00:29:41.570Z [info]: GraphQL Config query accessed
2025-05-21T00:29:41.887Z [info]: GraphQL Anime: sort=TRENDING, page=1, limit=15
2025-05-21T00:29:41.888Z [info]: Fetching trending items: type=tv, page=1, limit=15
2025-05-21T00:29:41.923Z [info]: GraphQL Anime: sort=LATEST, page=1, limit=40
2025-05-21T00:29:41.954Z [info]: GraphQL Anime: sort=TRENDING, page=1, limit=15
2025-05-21T00:29:41.954Z [info]: Fetching trending items: type=tv, page=1, limit=15
2025-05-21T00:29:42.093Z [info]: GraphQL Movies: sort=TRENDING, page=1, limit=15
2025-05-21T00:29:42.104Z [info]: Fetching trending items: type=movie, page=1, limit=15
2025-05-21T00:29:42.848Z [info]: Returning 2 trending items sorted by rank for tv page 1.
2025-05-21T00:29:44.406Z [info]: Returning 2 trending items sorted by rank for tv page 1.
2025-05-21T00:29:44.497Z [info]: Returning 12 trending items sorted by rank for movie page 1.
2025-05-21T00:29:44.507Z [info]: GraphQL Series: sort=TRENDING, page=1, limit=15
2025-05-21T00:29:44.507Z [info]: Fetching trending items: type=tv, page=1, limit=15
2025-05-21T00:29:44.527Z [info]: GraphQL Anime: sort=TRENDING, page=1, limit=15
2025-05-21T00:29:44.528Z [info]: Fetching trending items: type=tv, page=1, limit=15
2025-05-21T00:29:44.557Z [info]: GraphQL Movies: sort=TRENDING, page=1, limit=15
2025-05-21T00:29:44.557Z [info]: Fetching trending items: type=movie, page=1, limit=15
2025-05-21T00:29:44.670Z [info]: GraphQL Series: sort=TRENDING, page=1, limit=15
2025-05-21T00:29:44.670Z [info]: Fetching trending items: type=tv, page=1, limit=15
2025-05-21T00:29:45.556Z [info]: Returning 27 trending items sorted by rank for tv page 1.
2025-05-21T00:29:46.437Z [info]: Returning 2 trending items sorted by rank for tv page 1.
2025-05-21T00:29:46.508Z [info]: Returning 12 trending items sorted by rank for movie page 1.
2025-05-21T00:29:46.515Z [info]: GraphQL Anime: sort=TRENDING, page=1, limit=15
2025-05-21T00:29:46.515Z [info]: Fetching trending items: type=tv, page=1, limit=15
2025-05-21T00:29:46.572Z [info]: GraphQL Series: sort=TRENDING, page=1, limit=15
2025-05-21T00:29:46.574Z [info]: Fetching trending items: type=tv, page=1, limit=15
2025-05-21T00:29:46.775Z [info]: Returning 27 trending items sorted by rank for tv page 1.
2025-05-21T00:29:46.815Z [info]: GraphQL Series: sort=TRENDING, page=1, limit=15
2025-05-21T00:29:46.815Z [info]: Fetching trending items: type=tv, page=1, limit=15
2025-05-21T00:29:46.831Z [info]: GraphQL Series: sort=LATEST, page=1, limit=40
2025-05-21T00:29:47.089Z [info]: Returning 2 trending items sorted by rank for tv page 1.
2025-05-21T00:29:47.303Z [info]: Returning 27 trending items sorted by rank for tv page 1.
2025-05-21T00:29:47.542Z [info]: Returning 27 trending items sorted by rank for tv page 1.
2025-05-21T00:29:51.461Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:29:51.462Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:29:51.462Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:29:51.463Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:29:51.465Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:29:51.467Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:29:51.469Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:29:51.472Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:29:51.474Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:29:51.476Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vf"
2025-05-21T00:29:51.479Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33538-plus-belle-la-vie-encore-plus-belle-saison-2-part-4.html... | Episodes: 7, Movie Streams: 0
2025-05-21T00:29:51.479Z [info]: Using advanced enrichment for Plus belle la vie, encore plus belle
2025-05-21T00:29:51.942Z [info]: Using Gemini-optimized query for 'Plus belle la vie, encore plus belle': 'Plus belle la vie'
2025-05-21T00:29:51.942Z [info]: Searching TMDB for 'Plus belle la vie' (tv)
2025-05-21T00:29:52.094Z [info]: Fetching TMDB details for 'Plus belle la vie' (ID: 8590)
2025-05-21T00:29:52.166Z [info]: Cleaned title: Plus belle la vie -> Plus belle la vie
2025-05-21T00:29:52.166Z [info]: Enriched Plus belle la vie with TMDb ID: 8590
2025-05-21T00:29:52.167Z [info]: Fetching TMDB seasons data for Plus belle la vie (TMDB ID: 8590)
2025-05-21T00:29:52.167Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=8590
2025-05-21T00:29:52.233Z [info]: Found 19 seasons for TMDB TV series 8590.
2025-05-21T00:29:52.233Z [warn]: Missing required parameters for fetchTmdbSeason: tmdbId=8590, seasonNumber=0
2025-05-21T00:29:53.734Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/8590/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:29:55.334Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/8590/season/2?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:29:56.934Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/8590/season/3?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:29:58.612Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/8590/season/4?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:30:00.238Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/8590/season/5?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:30:01.895Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/8590/season/6?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:30:03.483Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/8590/season/7?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:30:05.056Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/8590/season/8?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:30:06.619Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/8590/season/9?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:30:08.258Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/8590/season/10?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:30:09.821Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/8590/season/11?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:30:11.408Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/8590/season/12?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:30:12.981Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/8590/season/13?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:30:14.627Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/8590/season/14?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:30:16.197Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/8590/season/15?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:30:17.775Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/8590/season/16?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:30:19.356Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/8590/season/17?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:30:20.940Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/8590/season/18?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:30:22.506Z [info]: Successfully fetched details for 18 out of 19 seasons for series 8590.
2025-05-21T00:30:22.507Z [info]: Formatted 18 out of 18 seasons for DB.
2025-05-21T00:30:22.507Z [info]: Found 18 TMDB seasons for Plus belle la vie
2025-05-21T00:30:22.508Z [info]: Set tmdbSeason field for season 2
2025-05-21T00:30:22.508Z [info]: TMDB title similarity check: "Plus belle la vie, encore plus belle" vs "Plus belle la vie" = 0.619
2025-05-21T00:30:22.509Z [info]: TMDB match verified by similarity: 0.619 >= 0.5
2025-05-21T00:30:23.509Z [info]: Seasons count changed for Plus belle la vie, encore plus belle: TMDB (0 -> 18)
2025-05-21T00:30:23.510Z [info]: Updating series (latest mode): Plus belle la vie, encore plus belle - metadata changes detected, updating database
2025-05-21T00:30:23.536Z [info]: Processing series: Le premier mariage de Georgie et ​​Mandy (https://flemmix.ws/serie-en-streaming/31823-georgie-and-mandys-first-marriage-saison-1.html)
2025-05-21T00:30:23.536Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/31823-georgie-and-mandys-first-marriage-saison-1.html...
2025-05-21T00:30:23.536Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:30:23.536Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/31823-georgie-and-mandys-first-marriage-saison-1.html
2025-05-21T00:30:27.123Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:30:27.124Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:30:27.125Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:30:27.127Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:30:27.130Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:30:27.132Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:30:27.134Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:30:27.136Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:30:27.138Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vs"
2025-05-21T00:30:27.140Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vs"
2025-05-21T00:30:27.142Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 9" with rel="ep9vs"
2025-05-21T00:30:27.144Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 10" with rel="ep10vs"
2025-05-21T00:30:27.149Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 11" with rel="ep11vs"
2025-05-21T00:30:27.151Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 12" with rel="ep12vs"
2025-05-21T00:30:27.153Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 13" with rel="ep13vs"
2025-05-21T00:30:27.155Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 14" with rel="ep14vs"
2025-05-21T00:30:27.157Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 15" with rel="ep15vs"
2025-05-21T00:30:27.158Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 16" with rel="ep16vs"
2025-05-21T00:30:27.160Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 17" with rel="ep17vs"
2025-05-21T00:30:27.163Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 18" with rel="ep18vs"
2025-05-21T00:30:27.165Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 19" with rel="ep19vs"
2025-05-21T00:30:27.167Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 20" with rel="ep20vs"
2025-05-21T00:30:27.169Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 21" with rel="ep21vs"
2025-05-21T00:30:27.170Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 22" with rel="ep22vs"
2025-05-21T00:30:27.172Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:30:27.172Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vf"
2025-05-21T00:30:27.174Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vf"
2025-05-21T00:30:27.175Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vf"
2025-05-21T00:30:27.177Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vf"
2025-05-21T00:30:27.178Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vf"
2025-05-21T00:30:27.181Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vf"
2025-05-21T00:30:27.184Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 7" with rel="ep7vf"
2025-05-21T00:30:27.186Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 8" with rel="ep8vf"
2025-05-21T00:30:27.189Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/31823-georgie-and-mandys-first-marriage-saison-1.html... | Episodes: 22, Movie Streams: 0
2025-05-21T00:30:27.189Z [info]: Using advanced enrichment for Le premier mariage de Georgie et ​​Mandy
2025-05-21T00:30:27.684Z [info]: Using Gemini-optimized query for 'Le premier mariage de Georgie et ​​Mandy': 'Le premier mariage de Georgie et Mandy'
2025-05-21T00:30:27.684Z [info]: Searching TMDB for 'Le premier mariage de Georgie et Mandy' (tv)
2025-05-21T00:30:27.839Z [info]: Fetching TMDB details for 'Le premier mariage de Georgie et Mandy' (ID: 243875)
2025-05-21T00:30:27.886Z [info]: Cleaned title: Le premier mariage de Georgie et Mandy -> Le premier mariage de Georgie et Mandy
2025-05-21T00:30:27.887Z [info]: Enriched Le premier mariage de Georgie et Mandy with TMDb ID: 243875
2025-05-21T00:30:27.887Z [info]: Fetching TMDB seasons data for Le premier mariage de Georgie et Mandy (TMDB ID: 243875)
2025-05-21T00:30:27.887Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=243875
2025-05-21T00:30:27.932Z [info]: Found 1 seasons for TMDB TV series 243875.
2025-05-21T00:30:27.932Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/243875/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:30:29.477Z [info]: Successfully fetched details for 1 out of 1 seasons for series 243875.
2025-05-21T00:30:29.477Z [info]: Formatted 1 out of 1 seasons for DB.
2025-05-21T00:30:29.477Z [info]: Found 1 TMDB seasons for Le premier mariage de Georgie et Mandy
2025-05-21T00:30:29.477Z [info]: Set tmdbSeason field for season 1
2025-05-21T00:30:29.477Z [info]: TMDB title similarity check: "Le premier mariage de Georgie et ​​Mandy" vs "Georgie & Mandy's First Marriage" = 0.533
2025-05-21T00:30:29.478Z [info]: TMDB match verified by similarity: 0.533 >= 0.5
2025-05-21T00:30:30.479Z [info]: Seasons count changed for Le premier mariage de Georgie et ​​Mandy: TMDB (0 -> 1)
2025-05-21T00:30:30.479Z [info]: Updating series (latest mode): Le premier mariage de Georgie et ​​Mandy - metadata changes detected, updating database
2025-05-21T00:30:30.498Z [info]: Processing series: Super Cub (https://flemmix.ws/serie-en-streaming/33478-super-cub-saison-1.html)
2025-05-21T00:30:30.499Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/33478-super-cub-saison-1.html...
2025-05-21T00:30:30.499Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:30:30.499Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/33478-super-cub-saison-1.html
2025-05-21T00:30:33.764Z [info]: [Wiflix Detail Final] Found 2 language blocks. Processing as Series.
2025-05-21T00:30:33.765Z [info]: [Wiflix Detail Final] Processing VOSTFR block
2025-05-21T00:30:33.765Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 1" with rel="ep1vs"
2025-05-21T00:30:33.768Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 2" with rel="ep2vs"
2025-05-21T00:30:33.770Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 3" with rel="ep3vs"
2025-05-21T00:30:33.772Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 4" with rel="ep4vs"
2025-05-21T00:30:33.775Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 5" with rel="ep5vs"
2025-05-21T00:30:33.777Z [info]: [Wiflix Detail Final] Found episode tab: "Episode 6" with rel="ep6vs"
2025-05-21T00:30:33.779Z [info]: [Wiflix Detail Final] Processing VF block
2025-05-21T00:30:33.779Z [info]: [Wiflix Detail Final] Scraped: https://flemmix.ws/serie-en-streaming/33478-super-cub-saison-1.html... | Episodes: 6, Movie Streams: 0
2025-05-21T00:30:33.779Z [info]: Using advanced enrichment for Super Cub
2025-05-21T00:30:34.183Z [info]: Using Gemini-optimized query for 'Super Cub': 'Super Cub'
2025-05-21T00:30:34.183Z [info]: Searching TMDB for 'Super Cub' (tv)
2025-05-21T00:30:34.246Z [info]: Fetching TMDB details for 'Super Cub' (ID: 279182)
2025-05-21T00:30:34.501Z [info]: Cleaned title: Super Cub -> Super Cub
2025-05-21T00:30:34.501Z [info]: Enriched Super Cub with TMDb ID: 279182
2025-05-21T00:30:34.502Z [info]: Fetching TMDB seasons data for Super Cub (TMDB ID: 279182)
2025-05-21T00:30:34.502Z [info]: Fetching TMDB TV Series Details to get season count: SeriesID=279182
2025-05-21T00:30:34.649Z [info]: Found 1 seasons for TMDB TV series 279182.
2025-05-21T00:30:34.649Z [info]: Fetching TMDB season data: https://api.themoviedb.org/3/tv/279182/season/1?api_key=94b3e867d01d17b6de1f13d5775bf60a&language=fr-FR
2025-05-21T00:30:36.192Z [info]: Successfully fetched details for 1 out of 1 seasons for series 279182.
2025-05-21T00:30:36.193Z [info]: Formatted 1 out of 1 seasons for DB.
2025-05-21T00:30:36.193Z [info]: Found 1 TMDB seasons for Super Cub
2025-05-21T00:30:36.193Z [info]: Set tmdbSeason field for season 1
2025-05-21T00:30:36.194Z [info]: TMDB title similarity check: "Super Cub" vs "Super Cube" = 0.933
2025-05-21T00:30:36.194Z [info]: TMDB match verified by similarity: 0.933 >= 0.5
2025-05-21T00:30:37.196Z [info]: Seasons count changed for Super Cub: TMDB (0 -> 1)
2025-05-21T00:30:37.196Z [info]: Updating series (latest mode): Super Cub - metadata changes detected, updating database
2025-05-21T00:30:37.196Z [info]: Saving/Updating 20 series from page 2 to database
2025-05-21T00:30:37.197Z [info]: Attempting to save 20 series items to DB
2025-05-21T00:30:37.219Z [info]: Updating existing series: F1 Grand Prix d'Emilie-Romagne (2025) (ID: 682763e4611f0d6721d22fc1)
2025-05-21T00:30:37.219Z [info]: Updating 6 episodes for F1 Grand Prix d'Emilie-Romagne (2025)
2025-05-21T00:30:37.259Z [info]: Successfully updated series: F1 Grand Prix d'Emilie-Romagne (2025) with 8 update operations
2025-05-21T00:30:37.282Z [info]: Updating existing series: The Rehearsal (ID: 67de18e36825d721f009bbaa)
2025-05-21T00:30:37.282Z [info]: Updating 5 episodes for The Rehearsal
2025-05-21T00:30:37.282Z [info]: Updating 2 TMDB seasons for The Rehearsal
2025-05-21T00:30:37.282Z [info]: Updating TMDB season data for The Rehearsal season 2
2025-05-21T00:30:37.316Z [info]: Successfully updated series: The Rehearsal with 10 update operations
2025-05-21T00:30:37.396Z [info]: Updating existing series: Chicago Med (ID: 67de16706825d721f0046765)
2025-05-21T00:30:37.396Z [info]: Updating 21 episodes for Chicago Med
2025-05-21T00:30:37.397Z [info]: Updating 10 TMDB seasons for Chicago Med
2025-05-21T00:30:37.397Z [info]: Updating TMDB season data for Chicago Med season 10
2025-05-21T00:30:37.540Z [info]: Successfully updated series: Chicago Med with 10 update operations
2025-05-21T00:30:37.584Z [info]: Updating existing series: S.W.A.T. (ID: 67de166c6825d721f0043f93)
2025-05-21T00:30:37.584Z [info]: Updating 22 episodes for S.W.A.T.
2025-05-21T00:30:37.584Z [info]: Updating 8 TMDB seasons for S.W.A.T.
2025-05-21T00:30:37.585Z [info]: Updating TMDB season data for S.W.A.T. season 8
2025-05-21T00:30:37.666Z [info]: Successfully updated series: S.W.A.T. with 10 update operations
2025-05-21T00:30:37.690Z [info]: Updating existing series: New York Crime Organisé (ID: 6805231ba860dd99b70f2795)
2025-05-21T00:30:37.691Z [info]: Updating 6 episodes for New York Crime Organisé
2025-05-21T00:30:37.691Z [info]: Updating 5 TMDB seasons for New York Crime Organisé
2025-05-21T00:30:37.691Z [info]: Updating TMDB season data for New York Crime Organisé season 5
2025-05-21T00:30:37.738Z [info]: Successfully updated series: New York Crime Organisé with 10 update operations
2025-05-21T00:30:37.762Z [info]: Updating existing series: FBI: International (ID: 67de169f6825d721f0047265)
2025-05-21T00:30:37.762Z [info]: Updating 21 episodes for FBI: International
2025-05-21T00:30:37.762Z [info]: Updating 4 TMDB seasons for FBI: International
2025-05-21T00:30:37.762Z [info]: Updating TMDB season data for FBI: International season 4
2025-05-21T00:30:37.816Z [info]: Successfully updated series: FBI: International with 10 update operations
2025-05-21T00:30:37.832Z [info]: Updating existing series: Legado (ID: 682b9a1f1b9e04287d44af2a)
2025-05-21T00:30:37.832Z [info]: Updating 8 episodes for Legado
2025-05-21T00:30:37.833Z [info]: Updating 1 TMDB seasons for Legado
2025-05-21T00:30:37.833Z [info]: Updating TMDB season data for Legado season 1
2025-05-21T00:30:37.860Z [info]: Successfully updated series: Legado with 10 update operations
2025-05-21T00:30:37.875Z [info]: Updating existing series: Lazarus (ID: 67f456745e602f960ec143fc)
2025-05-21T00:30:37.875Z [info]: Updating 7 episodes for Lazarus
2025-05-21T00:30:37.875Z [info]: Updating 1 TMDB seasons for Lazarus
2025-05-21T00:30:37.875Z [info]: Updating TMDB season data for Lazarus season 1
2025-05-21T00:30:37.913Z [info]: Successfully updated series: Lazarus with 10 update operations
2025-05-21T00:30:37.964Z [info]: Updating existing series: NCIS : Enquêtes spéciales (ID: 67de16686825d721f0041874)
2025-05-21T00:30:37.964Z [info]: Updating 20 episodes for NCIS : Enquêtes spéciales
2025-05-21T00:30:37.965Z [info]: Updating 22 TMDB seasons for NCIS : Enquêtes spéciales
2025-05-21T00:30:37.965Z [info]: Updating TMDB season data for NCIS : Enquêtes spéciales season 22
2025-05-21T00:30:38.116Z [info]: Successfully updated series: NCIS : Enquêtes spéciales with 10 update operations
2025-05-21T00:30:38.136Z [info]: Updating existing series: The Chi (ID: 682d12455d080bbe8689effd)
2025-05-21T00:30:38.137Z [info]: Updating 1 episodes for The Chi
2025-05-21T00:30:38.137Z [info]: Updating 7 TMDB seasons for The Chi
2025-05-21T00:30:38.137Z [info]: Updating TMDB season data for The Chi season 7
2025-05-21T00:30:38.192Z [info]: Successfully updated series: The Chi with 10 update operations
2025-05-21T00:30:38.208Z [info]: Updating existing series: Le Renard rouge (ID: 682b9b961b9e04287d44f181)
2025-05-21T00:30:38.208Z [info]: Updating 4 episodes for Le Renard rouge
2025-05-21T00:30:38.235Z [info]: Successfully updated series: Le Renard rouge with 8 update operations
2025-05-21T00:30:38.259Z [info]: Updating existing series: 9-1-1 (2018) (ID: 67de166c6825d721f004462e)
2025-05-21T00:30:38.259Z [info]: Updating 18 episodes for 9-1-1 (2018)
2025-05-21T00:30:38.320Z [info]: Successfully updated series: 9-1-1 (2018) with 8 update operations
2025-05-21T00:30:38.338Z [info]: Updating existing series: Catch Me a Killer (ID: 67efb2cec5dcb0fc0905d66a)
2025-05-21T00:30:38.339Z [info]: Updating 8 episodes for Catch Me a Killer
2025-05-21T00:30:38.368Z [info]: Successfully updated series: Catch Me a Killer with 8 update operations
2025-05-21T00:30:38.432Z [info]: Updating existing series: Doctor Who (2024) (ID: 67de16a86825d721f004dd3a)
2025-05-21T00:30:38.432Z [info]: Updating 7 episodes for Doctor Who (2024)
2025-05-21T00:30:38.432Z [info]: Updating 26 TMDB seasons for Doctor Who (2024)
2025-05-21T00:30:38.432Z [info]: Updating TMDB season data for Doctor Who (2024) season 2
2025-05-21T00:30:38.609Z [info]: Successfully updated series: Doctor Who (2024) with 10 update operations
2025-05-21T00:30:38.624Z [info]: Updating existing series: Accused US (ID: 680fe3ae632ebee77ff76c7b)
2025-05-21T00:30:38.624Z [info]: Updating 3 episodes for Accused US
2025-05-21T00:30:38.645Z [info]: Successfully updated series: Accused US with 8 update operations
2025-05-21T00:30:38.665Z [info]: Updating existing series: SEAL Team (ID: 67de17186825d721f005bea6)
2025-05-21T00:30:38.665Z [info]: Updating 10 episodes for SEAL Team
2025-05-21T00:30:38.665Z [info]: Updating 7 TMDB seasons for SEAL Team
2025-05-21T00:30:38.665Z [info]: Updating TMDB season data for SEAL Team season 7
2025-05-21T00:30:38.707Z [info]: Successfully updated series: SEAL Team with 10 update operations
2025-05-21T00:30:38.726Z [info]: Updating existing series: Skymed (ID: 67de166a6825d721f00428fb)
2025-05-21T00:30:38.726Z [info]: Updating 9 episodes for Skymed
2025-05-21T00:30:38.726Z [info]: Updating 3 TMDB seasons for Skymed
2025-05-21T00:30:38.726Z [info]: Updating TMDB season data for Skymed season 3
2025-05-21T00:30:38.763Z [info]: Successfully updated series: Skymed with 10 update operations
2025-05-21T00:30:38.932Z [info]: Updating existing series: Plus belle la vie, encore plus belle (ID: 681e93f60fc14b79a71b9b95)
2025-05-21T00:30:38.932Z [info]: Updating 7 episodes for Plus belle la vie, encore plus belle
2025-05-21T00:30:38.932Z [info]: Updating 18 TMDB seasons for Plus belle la vie, encore plus belle
2025-05-21T00:30:38.932Z [info]: Updating TMDB season data for Plus belle la vie, encore plus belle season 2
2025-05-21T00:30:39.731Z [info]: Successfully updated series: Plus belle la vie, encore plus belle with 10 update operations
2025-05-21T00:30:39.755Z [info]: Updating existing series: Le premier mariage de Georgie et ​​Mandy (ID: 67de16a26825d721f0049b2d)
2025-05-21T00:30:39.756Z [info]: Updating 22 episodes for Le premier mariage de Georgie et ​​Mandy
2025-05-21T00:30:39.756Z [info]: Updating 1 TMDB seasons for Le premier mariage de Georgie et ​​Mandy
2025-05-21T00:30:39.756Z [info]: Updating TMDB season data for Le premier mariage de Georgie et ​​Mandy season 1
2025-05-21T00:30:39.807Z [info]: Successfully updated series: Le premier mariage de Georgie et ​​Mandy with 10 update operations
2025-05-21T00:30:39.828Z [info]: Updating existing series: Super Cub (ID: 6817aed8882c61756becf126)
2025-05-21T00:30:39.828Z [info]: Updating 6 episodes for Super Cub
2025-05-21T00:30:39.828Z [info]: Updating 1 TMDB seasons for Super Cub
2025-05-21T00:30:39.828Z [info]: Updating TMDB season data for Super Cub season 1
2025-05-21T00:30:39.862Z [info]: Successfully updated series: Super Cub with 10 update operations
2025-05-21T00:30:39.862Z [info]: 20 series items processed. Created: 0, Updated: 20, Errors: 0
2025-05-21T00:30:39.862Z [info]: Database operation results for page 2: Created: 0, Updated: 20, Errors: 0
2025-05-21T00:30:39.862Z [info]: Successfully processed/saved 20 series from page 2
2025-05-21T00:30:39.862Z [info]: [Wiflix Series] Pausing 3s between pages for endpoint serie-en-streaming...
2025-05-21T00:30:42.864Z [info]: [Wiflix Series] Processing page 3/6 for serie-en-streaming
2025-05-21T00:30:42.867Z [info]: [Wiflix List] Starting Wiflix series list scrape for "serie-en-streaming", planning to scrape UP TO 3 pages using Puppeteer... Base URL: https://flemmix.ws
2025-05-21T00:30:42.869Z [info]: [Wiflix List] Fetching page 1 for serie-en-streaming using Puppeteer...
2025-05-21T00:30:42.870Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/
2025-05-21T00:30:42.870Z [info]: [Wiflix List] Fetching page 2 for serie-en-streaming using Puppeteer...
2025-05-21T00:30:43.871Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/page/2/
2025-05-21T00:30:44.857Z [info]: [Wiflix List] Scraped Wiflix series endpoint "serie-en-streaming" page 1 - Found & Validated: 20 items
2025-05-21T00:30:44.863Z [info]: [Wiflix List] Fetching page 3 for serie-en-streaming using Puppeteer...
2025-05-21T00:30:45.653Z [info]: [Wiflix List] Scraped Wiflix series endpoint "serie-en-streaming" page 2 - Found & Validated: 20 items
2025-05-21T00:30:45.864Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/page/3/
2025-05-21T00:30:47.992Z [info]: [Wiflix List] Scraped Wiflix series endpoint "serie-en-streaming" page 3 - Found & Validated: 20 items
2025-05-21T00:30:47.992Z [info]: [Wiflix List] Finished Wiflix series list scrape for endpoint "serie-en-streaming". Total unique items collected: 60
2025-05-21T00:30:47.992Z [info]: Collected 60 series from serie-en-streaming, page 3 before processing
2025-05-21T00:30:47.993Z [info]: Skipping already processed URL in this session: The Handmaid’s Tale : la servante écarlate (https://flemmix.ws/serie-en-streaming/33255-the-handmaids-tale-la-servante-ecarlate-saison-6.html)
2025-05-21T00:30:47.993Z [info]: Skipping already processed URL in this session: The Last Of Us (https://flemmix.ws/serie-en-streaming/33307-the-last-of-us-saison-2.html)
2025-05-21T00:30:47.993Z [info]: Skipping already processed URL in this session: The Walking Dead : Dead City (https://flemmix.ws/serie-en-streaming/33497-the-walking-dead-dead-city-saison-2.html)
2025-05-21T00:30:47.993Z [info]: Skipping already processed URL in this session: Power Book III: Raising Kanan (https://flemmix.ws/serie-en-streaming/33004-power-book-iii-raising-kanan-saison-4.html)
2025-05-21T00:30:47.993Z [info]: Skipping already processed URL in this session: Andor (https://flemmix.ws/serie-en-streaming/33380-andor-saison-2.html)
2025-05-21T00:30:47.993Z [info]: Skipping already processed URL in this session: HPI (https://flemmix.ws/serie-en-streaming/33545-hpi-saison-5.html)
2025-05-21T00:30:47.994Z [info]: Skipping already processed URL in this session: Astérix et Obélix : le Combat des Chefs (https://flemmix.ws/serie-en-streaming/33451-asterix-et-obelix-le-combat-des-chefs-saison-1.html)
2025-05-21T00:30:47.994Z [info]: Skipping already processed URL in this session: You (2018) (https://flemmix.ws/serie-en-streaming/33398-you-saison-5.html)
2025-05-21T00:30:47.994Z [info]: Skipping already processed URL in this session: Daredevil: Born Again (https://flemmix.ws/serie-en-streaming/32957-daredevil-born-again.html)
2025-05-21T00:30:47.994Z [info]: Skipping already processed URL in this session: Black Mirror (https://flemmix.ws/serie-en-streaming/33278-black-mirror-saison-7.html)
2025-05-21T00:30:47.994Z [info]: Skipping already processed URL in this session: Ici Tout Commence (https://flemmix.ws/serie-en-streaming/33504-ici-tout-commence-saison-9-1170.html)
2025-05-21T00:30:47.994Z [info]: Skipping already processed URL in this session: À l'épreuve du diable (https://flemmix.ws/serie-en-streaming/33515-a-lepreuve-du-diable-saison-2.html)
2025-05-21T00:30:47.994Z [info]: Skipping already processed URL in this session: Alaska : la ruée vers l'or (https://flemmix.ws/serie-en-streaming/32507-alaska-la-ruee-vers-lor-saison-15.html)
2025-05-21T00:30:47.994Z [info]: Skipping already processed URL in this session: Motorheads (https://flemmix.ws/serie-en-streaming/33603-motorheads-saison-1.html)
2025-05-21T00:30:47.994Z [info]: Skipping already processed URL in this session: Le Négociateur (https://flemmix.ws/serie-en-streaming/33602-le-negociateur-saison-2.html)
2025-05-21T00:30:47.994Z [info]: Skipping already processed URL in this session: Allegiance (https://flemmix.ws/serie-en-streaming/33553-allegiance-saison-2.html)
2025-05-21T00:30:47.994Z [info]: Skipping already processed URL in this session: Les Simpson (https://flemmix.ws/serie-en-streaming/31907-les-simpson-saison-36.html)
2025-05-21T00:30:47.995Z [info]: Skipping already processed URL in this session: Law & Order Toronto: Criminal Intent (https://flemmix.ws/serie-en-streaming/33169-toronto-section-criminelle-saison-2.html)
2025-05-21T00:30:47.995Z [info]: Skipping already processed URL in this session: Cimetière indien (https://flemmix.ws/serie-en-streaming/33253-cimetiere-indien-saison-1.html)
2025-05-21T00:30:47.995Z [info]: Skipping already processed URL in this session: Le Remplaçant (https://flemmix.ws/serie-en-streaming/33593-le-remplacant-saison-1.html)
2025-05-21T00:30:47.995Z [info]: Skipping already processed URL in this session: F1 Grand Prix d'Emilie-Romagne (2025) (https://flemmix.ws/serie-en-streaming/33570-f1-grand-prix-demilie-romagne-2025-saison-1.html)
2025-05-21T00:30:47.995Z [info]: Skipping already processed URL in this session: The Rehearsal (https://flemmix.ws/serie-en-streaming/25258-the-rehearsal-saison-1.html)
2025-05-21T00:30:47.995Z [info]: Skipping already processed URL in this session: Chicago Med (https://flemmix.ws/serie-en-streaming/31608-chicago-med-saison-10.html)
2025-05-21T00:30:47.995Z [info]: Skipping already processed URL in this session: S.W.A.T. (https://flemmix.ws/serie-en-streaming/31808-swat-saison-8.html)
2025-05-21T00:30:47.995Z [info]: Skipping already processed URL in this session: New York Crime Organisé (https://flemmix.ws/serie-en-streaming/33368-new-york-crime-organise-saison-5.html)
2025-05-21T00:30:47.995Z [info]: Skipping already processed URL in this session: FBI: International (https://flemmix.ws/serie-en-streaming/31809-fbi-international-saison-4.html)
2025-05-21T00:30:47.995Z [info]: Skipping already processed URL in this session: Legado (https://flemmix.ws/serie-en-streaming/33581-legado-saison-1.html)
2025-05-21T00:30:47.995Z [info]: Skipping already processed URL in this session: Lazarus (https://flemmix.ws/serie-en-streaming/33244-lazarus-saison-1.html)
2025-05-21T00:30:47.995Z [info]: Skipping already processed URL in this session: NCIS : Enquêtes spéciales (https://flemmix.ws/serie-en-streaming/31773-ncis-enquetes-speciales-saison-22.html)
2025-05-21T00:30:47.995Z [info]: Skipping already processed URL in this session: The Chi (https://flemmix.ws/serie-en-streaming/33576-the-chi-saison-7.html)
2025-05-21T00:30:47.995Z [info]: Skipping already processed URL in this session: Le Renard rouge (https://flemmix.ws/serie-en-streaming/33595-le-renard-rouge-saison-1.html)
2025-05-21T00:30:47.995Z [info]: Skipping already processed URL in this session: 9-1-1 (2018) (https://flemmix.ws/serie-en-streaming/31630-9-1-1-2018-saison-8.html)
2025-05-21T00:30:47.996Z [info]: Skipping already processed URL in this session: Catch Me a Killer (https://flemmix.ws/serie-en-streaming/33180-catch-me-a-killer-saison-1.html)
2025-05-21T00:30:47.996Z [info]: Skipping already processed URL in this session: Doctor Who (2024) (https://flemmix.ws/serie-en-streaming/32360-doctor-who-2024-saison-2.html)
2025-05-21T00:30:47.996Z [info]: Skipping already processed URL in this session: Accused US (https://flemmix.ws/serie-en-streaming/33434-accused-us-saison-2.html)
2025-05-21T00:30:47.996Z [info]: Skipping already processed URL in this session: SEAL Team (https://flemmix.ws/serie-en-streaming/31295-seal-team-saison-7.html)
2025-05-21T00:30:47.996Z [info]: Skipping already processed URL in this session: Skymed (https://flemmix.ws/serie-en-streaming/33061-skymed-saison-2.html)
2025-05-21T00:30:47.996Z [info]: Skipping already processed URL in this session: Plus belle la vie, encore plus belle (https://flemmix.ws/serie-en-streaming/33538-plus-belle-la-vie-encore-plus-belle-saison-2-part-4.html)
2025-05-21T00:30:47.996Z [info]: Skipping already processed URL in this session: Le premier mariage de Georgie et ​​Mandy (https://flemmix.ws/serie-en-streaming/31823-georgie-and-mandys-first-marriage-saison-1.html)
2025-05-21T00:30:47.996Z [info]: Skipping already processed URL in this session: Super Cub (https://flemmix.ws/serie-en-streaming/33478-super-cub-saison-1.html)
2025-05-21T00:30:48.018Z [info]: Processing series: Suits: L.A. (https://flemmix.ws/serie-en-streaming/32913-suits-la-saison-1.html)
2025-05-21T00:30:48.018Z [info]: [Wiflix Detail Final] Scraping detail page using Puppeteer: https://flemmix.ws/serie-en-streaming/32913-suits-la-saison-1.html...
2025-05-21T00:30:48.018Z [info]: [Wiflix Detail Final] Attempt 1 with timeout 75000ms
2025-05-21T00:30:48.018Z [info]: [BrowserUtils] Fetching with Puppeteer+Stealth: https://flemmix.ws/serie-en-streaming/32913-suits-la-saison-1.html
