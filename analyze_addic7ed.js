const axios = require('axios');
const cheerio = require('cheerio');
const fs = require('fs');

// Function to fetch and analyze a URL
async function fetchAndAnalyze(url) {
  console.log(`Fetching URL: ${url}`);
  
  try {
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9',
        'Referer': 'https://www.addic7ed.com/',
        'Connection': 'keep-alive',
        'Cache-Control': 'max-age=0'
      },
      timeout: 10000
    });
    
    console.log(`Status: ${response.status}`);
    console.log(`Content Type: ${response.headers['content-type']}`);
    
    // Save the HTML to a file for inspection
    fs.writeFileSync('addic7ed_page.html', response.data);
    console.log('HTML saved to addic7ed_page.html');
    
    // Parse the HTML with cheerio
    const $ = cheerio.load(response.data);
    
    // Extract page title
    const pageTitle = $('title').text();
    console.log(`Page Title: ${pageTitle}`);
    
    // Check if this is an episode page
    if (url.includes('/serie/')) {
      console.log('This appears to be an episode page. Analyzing subtitle tables...');
      
      // Find all subtitle tables
      const subtitleTables = $('table.tabel95');
      console.log(`Found ${subtitleTables.length} subtitle tables`);
      
      // Analyze the first table
      if (subtitleTables.length > 0) {
        const firstTable = subtitleTables.first();
        
        // Get the version
        const versionElement = firstTable.find('td.NewsTitle');
        const versionText = versionElement.text();
        console.log(`Version: ${versionText}`);
        
        // Find language rows
        const languageRows = firstTable.find('td.language').closest('tr');
        console.log(`Found ${languageRows.length} language rows in the first table`);
        
        // Analyze the first language row
        if (languageRows.length > 0) {
          const firstRow = languageRows.first();
          const language = firstRow.find('td.language').text().trim();
          const downloadLink = firstRow.find('a.buttonDownload').attr('href');
          
          console.log(`First Language: ${language}`);
          console.log(`Download Link: ${downloadLink}`);
          
          // Check if the download link is valid
          if (downloadLink) {
            console.log(`Full Download URL: https://www.addic7ed.com${downloadLink}`);
          }
        }
      }
      
      // Extract all download links
      const downloadLinks = [];
      $('a.buttonDownload').each((i, el) => {
        const href = $(el).attr('href');
        if (href) {
          downloadLinks.push(href);
        }
      });
      
      console.log(`Found ${downloadLinks.length} download links`);
      if (downloadLinks.length > 0) {
        console.log('Sample download links:');
        downloadLinks.slice(0, 5).forEach(link => {
          console.log(`- https://www.addic7ed.com${link}`);
        });
      }
    }
    
    return true;
  } catch (error) {
    console.error(`Error fetching URL: ${error.message}`);
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Headers: ${JSON.stringify(error.response.headers)}`);
    }
    return false;
  }
}

// Main function
async function main() {
  // URLs to analyze
  const urls = [
    'https://www.addic7ed.com/',
    'https://www.addic7ed.com/shows.php',
    'https://www.addic7ed.com/show/9857', // The Last of Us show page
    'https://www.addic7ed.com/serie/The_Last_of_Us/2/1/Future_Days', // Working URL
    'https://www.addic7ed.com/serie/The_Last_of_Us/2/5/Feel_Her_Love' // URL that returns 404
  ];
  
  // Analyze each URL
  for (const url of urls) {
    console.log('\n' + '='.repeat(80));
    console.log(`Analyzing URL: ${url}`);
    console.log('='.repeat(80));
    
    const success = await fetchAndAnalyze(url);
    
    if (!success) {
      console.log(`Failed to analyze URL: ${url}`);
    }
    
    // Wait a bit between requests to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
}

// Run the main function
main().catch(error => {
  console.error('Unhandled error:', error);
});
