

// File: /home/<USER>/NetStream/src/scrapers/services/scrapeService.js - REVISED STRUCTURE
const mongoose = require('mongoose');
const logger = require('../../utils/logger');
const { mongoUri, SCRAPE_INTERVAL, SCRAPING_ORDER, SCRAPE_MODE, scrapeMode } = require('../../config/env'); // Get constants from env
const { scrapeFrenchAnime } = require('../../../scripts/scrapeFrenchAnime'); // Corrected path
const { scrapeWitv } = require('../../../scripts/scrapeWitv'); // Corrected path
const { scrapeWiflixMovies } = require('../../../scripts/scrapeWiflixMovies');
const { scrapeWiflixSeries } = require('../../../scripts/scrapeWiflixSeries');
const { saveToDB } = require('../../../scripts/saveToDB');
const { closeBrowser } = require('../../utils/browserUtils'); // Keep import for closeBrowser

// Page limits (keep the function as is)
// Update page limits to properly handle film-ancien and film-en-streaming
const DEFAULT_PAGE_LIMITS = { witv: -1, movies: -1, series: -1, anime: -1 };
// Modify to include film-ancien and film-en-streaming
const LATEST_PAGE_LIMITS = { movies: 0, series: 0, anime: 2, witv: 4 };
const SCRAPER_TIMEOUTS = {
    movies: 90 * 60 * 1000, // Increased to 90 minutes
    series: 60 * 60 * 1000,
    anime: 30 * 60 * 1000,
    witv: 15 * 60 * 1000
};

function getPageLimits(mode) {
    const isLatestMode = mode === SCRAPE_MODE.LATEST;
    const baseLimits = isLatestMode ? LATEST_PAGE_LIMITS : DEFAULT_PAGE_LIMITS;
    const limits = { ...baseLimits };

    // Allow overriding via environment variables
    for (const key in limits) {
        const envVar = `PAGE_LIMIT_${key.toUpperCase()}`;
        if (process.env[envVar]) {
            const parsedValue = parseInt(process.env[envVar], 10);
            if (!isNaN(parsedValue)) {
                limits[key] = parsedValue;
            } else {
                logger.warn(`Invalid value for environment variable ${envVar}: ${process.env[envVar]}`);
            }
        }
    }

    // Allow overriding via command line arguments (less common now)
    const args = process.argv.slice(2);
    args.forEach(arg => {
        const [key, value] = arg.split('=');
        if (limits.hasOwnProperty(key)) {
            const parsedValue = parseInt(value, 10);
            limits[key] = isNaN(parsedValue) ? limits[key] : parsedValue;
        }
    });
    logger.info(`Page limits parsed for ${mode} mode: ${JSON.stringify(limits)}`);
    return limits;
}

async function connectDB() {
    if (mongoose.connection.readyState !== 1) {
        logger.info('Attempting to connect to MongoDB in scrapeService...');
        await mongoose.connect(mongoUri);
        logger.info('Connected to MongoDB in scrapeService');
    } else {
        logger.info('Using existing MongoDB connection in scrapeService');
    }
}

async function timeout(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Add a delay between scraping tasks to prevent overwhelming the browser
async function runScraperWithTimeout(taskFn, timeoutDuration, taskName) {
     // Add retry mechanism for protocol errors
     let retries = 0;
     const maxRetries = 3;

     while (retries <= maxRetries) {
         try {
             return await Promise.race([
                 taskFn(),
                 new Promise((_, reject) =>
                     setTimeout(() => reject(new Error(`${taskName} scraping timeout after ${timeoutDuration / 1000}s`)), timeoutDuration)
                 )
             ]);
         } catch (err) {
             if (err.message.includes('Protocol timeout') || err.name === 'ProtocolError') {
                 retries++;
                 if (retries <= maxRetries) {
                     logger.warn(`Protocol error in ${taskName}, retry ${retries}/${maxRetries} after 10s delay...`);
                     await timeout(10000); // 10 second delay before retry
                     continue;
                 }
             }

             logger.error(`Error or Timeout in ${taskName} scraper: ${err.message}`);
             return []; // Return empty array on error/timeout to allow others to proceed
         }
     }
}


async function scrapeAll(mode = scrapeMode) {
  const startTime = Date.now();
  logger.info(`Starting scrapeAll function in ${mode} mode`);
  try {
    await connectDB();

    const pageLimits = getPageLimits(mode);
    logger.info(`Using page limits for ${mode} mode: ${JSON.stringify(pageLimits)}`);

    let totalCounts = { movies: 0, series: 0, anime: 0, witv: 0 };
    let taskResults = { movies: [], series: [], anime: [], witv: [] };

    // Define scraper functions clearly
    const tasks = {
      anime: async () => {
          if (pageLimits.anime === 0) {
              logger.info('Skipping anime (limit 0)');
              return [];
          }
          logger.info(`Starting anime scraping with limit ${pageLimits.anime}`);
          const results = await runScraperWithTimeout(
              () => scrapeFrenchAnime(pageLimits.anime, saveToDB, mode === SCRAPE_MODE.LATEST),
              SCRAPER_TIMEOUTS.anime,
              'Anime'
          );
          logger.info(`Anime scraping completed, got ${results.length} results`);
          taskResults.anime = results;
          return results;
      },
      wiflix_movies: async () => {
          if (pageLimits.movies === 0) {
              logger.info('Skipping movies (limit 0)');
              return [];
          }
          logger.info(`Starting movies scraping with limit ${pageLimits.movies}`);
          const results = await runScraperWithTimeout(
              () => scrapeWiflixMovies(pageLimits.movies, saveToDB, mode === SCRAPE_MODE.LATEST),
              SCRAPER_TIMEOUTS.movies,
              'Wiflix Movies'
          );
          logger.info(`Movies scraping completed, got ${results.length} results`);
          taskResults.movies = results;
          return results;
      },
      wiflix_series: async () => {
          if (pageLimits.series === 0) {
              logger.info('Skipping series (limit 0)');
              return [];
          }
          logger.info(`Starting series scraping with limit ${pageLimits.series}`);
          const results = await runScraperWithTimeout(
              () => scrapeWiflixSeries(pageLimits.series, saveToDB, mode === SCRAPE_MODE.LATEST),
              SCRAPER_TIMEOUTS.series,
              'Wiflix Series'
          );
          logger.info(`Series scraping completed, got ${results.length} results`);
          taskResults.series = results;
          return results;
      },
      witv: async () => {
          if (pageLimits.witv === 0) {
              logger.info('Skipping WiTV (limit 0)');
              return [];
          }
          logger.info(`Starting WiTV scraping with limit ${pageLimits.witv}`);
          const results = await runScraperWithTimeout(
              () => scrapeWitv(pageLimits.witv, saveToDB),
              SCRAPER_TIMEOUTS.witv,
              'WiTV'
          );
          logger.info(`WiTV scraping completed, got ${results.length} results`);
          taskResults.witv = results;
          return results;
      }
    };

    logger.info(`SCRAPING_ORDER: ${SCRAPING_ORDER.join(', ')}`);

    // Separate tasks
    const puppeteerTaskNames = SCRAPING_ORDER.filter(task => task.startsWith('wiflix_'));
    const axiosTaskNames = SCRAPING_ORDER.filter(task => !task.startsWith('wiflix_'));

    // Get concurrency settings from environment variables or use defaults
    const MAX_CONCURRENT_PUPPETEER_TASKS = process.env.MAX_CONCURRENT_PUPPETEER_TASKS ? parseInt(process.env.MAX_CONCURRENT_PUPPETEER_TASKS) : 1;
    const MAX_CONCURRENT_AXIOS_TASKS = process.env.MAX_CONCURRENT_AXIOS_TASKS ? parseInt(process.env.MAX_CONCURRENT_AXIOS_TASKS) : 2;

    logger.info(`Scrape concurrency settings: MAX_CONCURRENT_PUPPETEER_TASKS=${MAX_CONCURRENT_PUPPETEER_TASKS}, MAX_CONCURRENT_AXIOS_TASKS=${MAX_CONCURRENT_AXIOS_TASKS}`);

    let allResults = [];

    // Run Axios tasks with controlled concurrency
    if (axiosTaskNames.length > 0) {
        logger.info(`Starting Axios tasks with concurrency ${MAX_CONCURRENT_AXIOS_TASKS}: ${axiosTaskNames.join(', ')}`);

        try {
            // Process Axios tasks in batches to control concurrency
            for (let i = 0; i < axiosTaskNames.length; i += MAX_CONCURRENT_AXIOS_TASKS) {
                const batch = axiosTaskNames.slice(i, i + MAX_CONCURRENT_AXIOS_TASKS);
                logger.info(`Processing Axios batch ${Math.floor(i/MAX_CONCURRENT_AXIOS_TASKS) + 1}/${Math.ceil(axiosTaskNames.length/MAX_CONCURRENT_AXIOS_TASKS)}: ${batch.join(', ')}`);

                const batchPromises = batch.map(taskName => tasks[taskName]());
                const batchResults = await Promise.all(batchPromises);

                // Flatten and add to allResults
                const flattenedResults = batchResults.flat();
                allResults.push(...flattenedResults);

                logger.info(`Axios batch completed. Batch results: ${flattenedResults.length}`);
            }

            logger.info(`All Axios tasks completed. Total results: ${allResults.length}`);
        } catch (axiosError) {
            logger.error(`Error during Axios tasks: ${axiosError.message}`, { stack: axiosError.stack });
        }
    }

    // Run Puppeteer tasks with controlled concurrency
    if (puppeteerTaskNames.length > 0) {
        logger.info(`Starting Puppeteer tasks with concurrency ${MAX_CONCURRENT_PUPPETEER_TASKS}: ${puppeteerTaskNames.join(', ')}`);

        // Process Puppeteer tasks in batches to control concurrency
        for (let i = 0; i < puppeteerTaskNames.length; i += MAX_CONCURRENT_PUPPETEER_TASKS) {
            const batch = puppeteerTaskNames.slice(i, i + MAX_CONCURRENT_PUPPETEER_TASKS);
            logger.info(`Processing Puppeteer batch ${Math.floor(i/MAX_CONCURRENT_PUPPETEER_TASKS) + 1}/${Math.ceil(puppeteerTaskNames.length/MAX_CONCURRENT_PUPPETEER_TASKS)}: ${batch.join(', ')}`);

            // Run tasks in the current batch concurrently
            const batchPromises = batch.map(taskName => {
                return new Promise(async (resolve) => {
                    try {
                        logger.info(`Starting Puppeteer task: ${taskName}`);
                        const result = await tasks[taskName]();

                        if (Array.isArray(result)) {
                            logger.info(`Puppeteer task ${taskName} completed with ${result.length} results`);
                            resolve(result);
                        } else {
                            logger.warn(`Puppeteer task ${taskName} did not return an array: ${typeof result}`);
                            resolve([]);
                        }
                    } catch (puppeteerError) {
                        logger.error(`Error during Puppeteer task ${taskName}: ${puppeteerError.message}`, { stack: puppeteerError.stack });
                        resolve([]);
                    }
                });
            });

            const batchResults = await Promise.all(batchPromises);
            const flattenedResults = batchResults.flat();
            allResults.push(...flattenedResults);

            logger.info(`Puppeteer batch completed. Batch results: ${flattenedResults.length}`);

            // Add a delay between batches to allow resources to be freed
            if (i + MAX_CONCURRENT_PUPPETEER_TASKS < puppeteerTaskNames.length) {
                const batchDelay = 10000; // 10 seconds between batches
                logger.info(`Waiting ${batchDelay}ms between Puppeteer batches...`);
                await timeout(batchDelay);
            }
        }

        logger.info(`All Puppeteer tasks completed. Total results: ${allResults.filter(r => r).length}`);
    }

    // Log detailed results by type
    for (const type in taskResults) {
        totalCounts[type] = taskResults[type].length;
    }

    logger.info(`Scraping results by type: ${JSON.stringify(totalCounts)}`);

    const duration = ((Date.now() - startTime) / 1000).toFixed(1);
    logger.info(`Full scrape completed in ${mode} mode in ${duration}s.`);
    logger.info(`Total items potentially saved across all executed tasks: ${allResults.length}`);

    return {
        totalItems: allResults.length,
        itemsByType: totalCounts,
        duration: parseFloat(duration)
    };

  } catch (err) {
    // Catch errors from connectDB or other setup issues
    logger.error(`ScrapeAll main process error in ${mode} mode: ${err.message}`, { stack: err.stack });
    throw err; // Re-throw to allow proper handling by caller
  } finally {
    // Centralized browser closing
    logger.info('ScrapeAll function finished or errored. Attempting to close browser...');
    await closeBrowser().catch(err => logger.error('[ScrapeAll Finally] Error closing browser:', err));
    // Optionally disconnect DB if needed, but likely handled by server shutdown
    // if (mongoose.connection.readyState === 1) {
    //     await mongoose.disconnect();
    //     logger.info('MongoDB disconnected via scrapeAll finally block.');
    // }
  }
}

module.exports = { scrapeAll, SCRAPE_MODE };