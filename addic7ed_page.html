<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
	<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
	<meta name="description" content="The Last of Us - 02x05 - Feel Her Love subtitles. Download subtitles in English from the source. " />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<title>
		 Subtitle The Last of Us - 02x05 - Feel Her Love subtitles from the source! - Addic7ed.com
	</title>
	<link href="https://www.addic7ed.com/css/wikisubtitles.css" rel="stylesheet" title="default" type="text/css" />
	<link rel="SHORTCUT ICON" href="https://www.addic7ed.com/favicon.ico" /> 
	<link rel="stylesheet" href="https://www.addic7ed.com/css/circle.css">

<link href="https://maxcdn.bootstrapcdn.com/font-awesome/4.1.0/css/font-awesome.min.css" rel="stylesheet">
	<link rel="stylesheet" href="https://www.addic7ed.com/css/uploadbutton.css">	
	<script type="text/javascript" src="https://www.addic7ed.com/js/mootools-core-1.4.5-full-compat.js"></script>
	<script type="text/javascript" src="https://www.addic7ed.com/js/mootools-more-*******.js"></script>
	<script type="text/javascript" src="https://www.addic7ed.com/js/moostarrating.js"></script>
  <style>
 @import url("//maxcdn.bootstrapcdn.com/font-awesome/4.2.0/css/font-awesome.min.css");
    .face-button {

      height: 24px;
      display: inline-block;
      border: 3px solid #4c956c;
      font-size: 15px;
      font-weight: 500;
      text-align: center;
      text-decoration: none;
      color: #e43;
      overflow: hidden;
      width: 230px;
      
      .icon {
        margin-right: 6px;
      }
      
      .face-primary,
      .face-secondary {
        display: block;
        padding: 0 32px;
        line-height: 24px;
        transition: margin .4s;
      }
      
      .face-primary {
        background-color: #4c956c;
        color: #fff;
      }
      
      &:hover .face-primary {
        margin-top: -24px;
      }
    }

</style>
	<script type="text/javascript">	function changeAppLang()
	{
		value = $("comboLang").value;
		if (value!='en')
		{
			window.location= "/changeapplang.php?applang=" + value;
		}
	}
	
	function showChange(showID)
	{
		if (showID==0)
			valor = $("qsShow").value;
		else
			valor = showID;
		if (valor>0)
		{
			$("qsSeason").innerHTML = '<img title="Please wait, loading" alt="Loading" src="https://www.addic7ed.com/images/loader.gif" />';
			$("qsEp").innerHTML = '';
			$("qssShow").innerHTML = '';

			var myRequest = new Request({
			    url: '/ajax_getSeasons.php',
			    method: 'get',
			    onSuccess: function(responseText){
				$("qsSeason").innerHTML = responseText;
			    }
			});
			myRequest.send('showID='+valor);
		}
	}
	
	function seasonChange(showID, season)
	{
	
		valor = showID;

		if (valor>0)
		{
			$("qsEp").innerHTML = '<img title="Please wait, loading" alt="Loading" src="https://www.addic7ed.com/images/loader.gif" />';
			
			if (season==-1)
				myseason = $("qsiSeason").value;
			else
				myseason=season;
                        var myRequest = new Request({
                            url: '/ajax_getEpisodes.php',
                            method: 'get',
                            onSuccess: function(responseText){
                                $("qsEp").innerHTML = responseText; 
		            }
                        });
                        myRequest.send('showID='+valor+'&season='+myseason); 
		}

	}
	
	function changeEp()
	{
		var valor = $("qsiEp").value;
		window.location = '/re_episode.php?ep=' + valor;
	}
	
	function qsClear()
	{
		$("qssShow").innerHTML = '<img title="Please wait, loading" alt="Loading" src="https://www.addic7ed.com/images/loader.gif" />';
		$("qsSeason").innerHTML = '&nbsp;';
		$("qsEp").innerHTML = '&nbsp;';
		
		var myRequest = new Request({
		    url: '/ajax_getShows.php',
		    method: 'get',
		    onSuccess: function(responseText){
			$("qssShow").innerHTML = responseText;
		    }
		});
		myRequest.send('showID='+valor);		
	}
</script>
	<script type="text/javascript">
		function saveFavorite(subid,langid,hi)
		{
	                return;
		alert('Now following!');
		/*        new Ajax('/ajax_saveFavorite.php?subid='+subid+'&langid='+langid+'&hi='+hi, {
				method: 'get',
				update: $("episodes")   
			}).request();
		*/	

		var myRequest = new Request({
		    url: '/ajax_saveFavorite.php',
		    method: 'get'
		});
		myRequest.send('subid='+subid+'&langid='+langid+'&hi='+hi);
		}
		
	</script>

	
	</head>

	<body>
	
<center>
<br />
<table border="0">
<tr>
  <td rowspan="9"><a href="/"><img width="350" height="111" src="https://www.addic7ed.com/images/addic7edlogonew.png"  border="0"  title="Addic7ed.com - Quality Subtitles for TV Shows and movies" alt="Addic7ed.com - Quality Subtitles for TV Shows and movies" /></a></td>
</tr>
<tr><td align="center" colspan="2">
<h1><small>Download free subtitles for TV Shows and Movies.</small>&nbsp; 
<select name="applang" class="inputCool" onchange="changeAppLang();" id="comboLang"><option value="ar">Arabic</option><option value="ca">Catala</option><option selected="selected" value="en">English</option><option value="eu">Euskera</option><option value="fr">French</option><option value="ga">Galician</option><option value="de">German</option><option value="gr">Greek</option><option value="hu">Hungarian</option><option value="it">Italian</option><option value="fa">Persian</option><option value="pl">Polish</option><option value="pt">Portuguese</option><option value="br">Portuguese (Brazilian)</option><option value="ro">Romanian</option><option value="ru">Russian</option><option value="es">Spanish</option><option value="se">Swedish</option></select></h1>
</td></tr>
<tr><td align="center" colspan="2">
<div id="hBar">
			  <ul>
				<li><a class="button white" href="/newaccount.php">Signup</a></li>
				<li><a class="button white" href="/login.php">Login</a></li>
				<li><a class="button white" href="/shows.php">Shows</a></li>
				<li><a class="button white" href="http://www.sub-talk.net/topic/6961-recruitment-syncers-only-not-translators/">Join the team</a></li>
				<li><a class="button white" href="http://www.sub-talk.net">Forum</a></li>
			  </ul>
			  </div>
</td></tr> 

<tr>
  <td><a href="https://www.patreon.com/bePatron?u=7679598"><img width="170px" height="40px" src="https://c5.patreon.com/external/logo/become_a_patron_button.png" /></a>
</td><td>
	
    <a href="http://twitter.com/addic7ed" target="_blank"><img width="32" height="32" src="https://www.addic7ed.com/images/twitter_right.png" alt="Twitter" border="0" /></a>
	


  </td>
 </tr>
 
<!--  <tr>
   <td></td>
<td>       

         <div alt="Donation Status" class="c100 p0 small">
                    <span><a title="Donation Status" href="https://www.addic7ed.com/contact.php">0%</a></span>
                    <div class="slice">
                        <div class="bar"></div>
                        <div class="fill"></div>
                    </div>
                </div></td>
 </tr>
-->
</table>
</center>

<center>

<!--[if lt IE 7]>
 <style type="text/css">
 div, img { behavior: url(https://www.addic7ed.com/js/iepngfix.htc) }
 </style>
<![endif]-->
<!--
    This file is part of wikisubtitles.

    wikisubtitles is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Wikisubtitles is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Foobar.  If not, see <http://www.gnu.org/licenses/>.
-->
<table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
  <tr>
    <td width="50%" align="center">    
    <a href="javascript:qsClear();"><img width="24" height="24" title="Clear Search" alt="Clear Search Terms" src="https://www.addic7ed.com/images/view.png" border="0" /></a> <span id="qssShow"><b>
Quick search    </b>
  &nbsp;		<script type="text/javascript">
			window.addEvent('domready', function() { showChange(9256 ); } );
		</script>
    
    </span>
    <span id="qsSeason">&nbsp;
<script type="text/javascript">window.addEvent('domready', function() { seasonChange(9256,2) } );</script>    
    </span> 
	&nbsp;
    <span id="qsEp">&nbsp;</span></td>
    <td><form id="form1" name="form1" method="get" action="/search.php">
      <div align="center">
Search subtitle      &nbsp;
        <input name="search" type="text" id="search" size="20" /> &nbsp;
<input name="Submit" type="submit" class="coolBoton" value="Search" />      </div>
    </form>

    </td>
  </tr>
</table>
<br><center>

      </center>



<center>

      
</center>


		<br />
		<script type="text/javascript">
			function saveWatched(subid,season,episode)
			{
			
				alert("Login before using this feature");
				return false;
			var myRequest = new Request({
			    url: '/ajax_saveWatched.php',
			    method: 'get',
			});
			myRequest.send('subid='+subid+'&season='+season+'&episode='+episode);
			}
			
			function saveShowRating(tvrage,rating)
			{
				alert("Login before using this feature");                return false;
			var myRequest = new Request({
			    url: '/ajax_saveShowRating.php',
			    method: 'get',
			});
			myRequest.send('tvrage='+tvrage+'&rating='+rating);

			}
		</script>

		<script type='text/javascript'>//<![CDATA[ 
		window.addEvent('load', function() {
		// Default images folder definition. You will use your own.
		MooStarRatingImages.defaultImageFolder = 
		    'https://www.addic7ed.com/img/';

		// ================================================================

		// Advanced options
		var advancedRating = new MooStarRating({
		    form: 'advanced',
		    radios: 'my_rating',                // Radios name
		    half: true,                         // That means one star for two values!
		    imageEmpty: 'star_boxed_empty.png', // Different image
		    imageFull:  'star_boxed_full.png',  // Different image
		    imageHover: 'star_boxed_hover.png', // Different image
		    width: 17,                          // One pixel bigger
		    tip: '<i>[VALUE] / 10.0</i>', // Mouse rollover tip
		    tipTarget: $('htmlTip'),            // Tip element
		    tipTargetType: 'html'               // Tip type, now is HTML, not only text
		});

		// ================================================================

		// Click callback function
		function myCallback(value) {
			saveShowRating(0,value)
		}

		// Click event
		advancedRating.addEvent('click', myCallback);
		});//]]>  

		</script>

		<br />
		<div id="container">
		<table class="tabel70" border="0">
		    <tr> <!-- table header -->
			<td class="tablecorner"><img src="https://www.addic7ed.com/images/tl.gif" /></td>
			<td>&nbsp;</td>
			<td class="tablecorner"><img src="https://www.addic7ed.com/images/tr.gif" /></td>
		    </tr>
		    <tr>
			<td>&nbsp;</td>
			<td>
				<table  border="0" align="center" class="tabel95">
				  <tr>
				   <td width="50%"><div align="left"><span class="titulo">
				    The Last of Us - 02x05 - Feel Her Love <small>Subtitle</small>
				    </span></div>
				    </td>
				    <td width="20%">
				    </td>		
				    <td width="30%" rowspan="5"><div valign="top">
					<table class="tabel" width="40%" border="0" align="center">
					      <tr>
						<td><img class="resize" border="0" src="https://www.addic7ed.com/images/showimages/9256.jpg" />
						</td>
					      </tr>
					</table></div>   
				    </td>
				  </tr>

				  <tr>
				    <td><img src="https://www.addic7ed.com/images/folder_image.png" width="16" height="16" />&nbsp;<a href="/show/9256">The Last of Us</a>, <a href='/season/9256/2'>Season 2</a>, Episode 5 subtitles
				    </td>
				    <td>
					</td>		
				  </tr>
				  
				  <tr>
				    <td><a href="https://www.tvhoard.com"><font color=red>Episode list and air dates</font></a>
				   </td>
				    <td>
				  </td></tr><tr><td>	</td></tr>
			  <tr>
			   <td>
			    </td>		
			  </tr>

			  <tr>
			    <td>
			     <img src="https://www.addic7ed.com/images/download.png" width="16" height="16" align="absmiddle" /> 
				1728
			    </td>
			    <td>
			    </td>		
			  </tr>
			  <tr>
			   <td><a href="/show/9256">Multi Download</a></td>
			    <td>
			    </td>
			   <td>

							  </td>
			</tr>
		</table>
		</td>
			<td>&nbsp;</td>
		</tr>
		<tr> <!-- table footer -->
			<td class="tablecorner"><img src="https://www.addic7ed.com//images/bl.gif" /></td>
			<td>&nbsp;</td>
			<td class="tablecorner"><img src="https://www.addic7ed.com//images/br.gif" /></td>
		</tr>
		</table>
		</div>
		<p>

		<script type="text/javascript">
			function sortChange()
			{
				var sortCheck = document.getElementById("sort_versions");
				window.location='/serie/The Last of Us/2/5/'+sortCheck.checked;
			}	
			function filterChange()
			{
				var filter = document.getElementById("filterlang");
				window.location='/serie/The Last of Us/2/5/'+filter.options[filter.selectedIndex].value;
			}	
		</script><b>Sort versions alphabetically by language</b><INPUT TYPE="CHECKBOX" id="sort_versions" onclick="javascript:sortChange();" checked="yes" > - Filter Language: <select id="filterlang" name="filterlang" class="inputCool" onchange="javascript:filterChange();" >
		<option value="0">All</option><option value="52">Albanian</option><option value="38">Arabic</option><option value="50">Armenian</option><option value="48">Azerbaijani</option><option value="47">Bengali</option><option value="44">Bosnian</option><option value="35">Bulgarian</option><option value="64">Cantonese</option><option value="12">Català</option><option value="41">Chinese (Simplified)</option><option value="24">Chinese (Traditional)</option><option value="31">Croatian</option><option value="14">Czech</option><option value="30">Danish</option><option value="17">Dutch</option><option value="1">English</option><option value="54">Estonian</option><option value="13">Euskera</option><option value="28">Finnish</option><option value="8">French</option><option value="53">French (Canadian)</option><option value="15">Galego</option><option value="11">German</option><option value="27">Greek</option><option value="23">Hebrew</option><option value="55">Hindi</option><option value="20">Hungarian</option><option value="56">Icelandic</option><option value="37">Indonesian</option><option value="7">Italian</option><option value="32">Japanese</option><option value="66">Kannada</option><option value="61">Klingon</option><option value="42">Korean</option><option value="57">Latvian</option><option value="58">Lithuanian</option><option value="49">Macedonian</option><option value="40">Malay</option><option value="67">Malayalam</option><option value="62">Marathi</option><option value="29">Norwegian</option><option value="43">Persian</option><option value="21">Polish</option><option value="9">Portuguese</option><option value="10">Portuguese (Brazilian)</option><option value="26">Romanian</option><option value="19">Russian</option><option value="39">Serbian (Cyrillic)</option><option value="36">Serbian (Latin)</option><option value="60">Sinhala</option><option value="25">Slovak</option><option value="22">Slovenian</option><option value="4">Spanish</option><option value="69">Spanish (Argentina)</option><option value="6">Spanish (Latin America)</option><option value="5">Spanish (Spain)</option><option value="18">Swedish</option><option value="68">Tagalog</option><option value="59">Tamil</option><option value="63">Telugu</option><option value="46">Thai</option><option value="16">Turkish</option><option value="51">Ukrainian</option><option value="45">Vietnamese</option><option value="65">Welsh</option></select><br>
	<div id="container95m">
		<table class="tabel95">
			<tr> <!-- table header -->
			<td class="tablecorner"><img src="https://www.addic7ed.com/images/tl.gif" /></td>
			<td>&nbsp;</td>
			<td class="tablecorner"><img src="https://www.addic7ed.com/images/tr.gif" /></td>
		    </tr>
		    <tr>
			<td>&nbsp;</td>
			<td>
	<table width="100%" border="0" align="center" class="tabel95">
	  <tr>
	    <td colspan="3" align="center" class="NewsTitle"><img src="https://www.addic7ed.com/images/folder_page.png" width="16" height="16" />Version FLUX+Kitsune+NTb+playWEB+SuccessfulCrab+SYLiX, Duration: 0.00 &nbsp;<img title="720/1080" src="https://www.addic7ed.com/images/hdicon.png" width="24" height="24" /></td><td colspan="1">
			<img src="https://www.addic7ed.com/images/movie_faq.png" title="LOL &amp; SYS always work with 720p DIMENSION;	XII & ASAP always work with 720p IMMERSE; 2HD always works with 720p 2HD; BiA always works with 720p BiA; FoV always works with 720p FoV" border="0" width="24" height="24" />
			<img src="https://www.addic7ed.com/images/subtitle.gif" width="22" height="14" /><a href="/index.php" onclick="return confirm('You must be a user for a few days before you can make edits.')">New translation</a><img src="https://www.addic7ed.com/images/link.png"  /> uploaded by <a href='/user/890973'>firefly</a>  3 days ago
		    </td>
		    <td width="16%">
		    </td>
		  </tr>
		  <tr>
			  <td colspan="4"></td>
			  <td class="newsDate" colspan="3">
				Duration: 44:34<img src="https://www.addic7ed.com/images/invisible.gif" />
			  </td>
		  </tr><tr><td width="10%" rowspan="2" valign="top"><a href="http://www.addic7ed.com"><img height="35" width="100" src="https://www.addic7ed.com/friends/addic7ed.png" border="0" /></a>&nbsp;&nbsp;&nbsp;</td>
			<td width="1%" rowspan="2" valign="top"><img src="https://www.addic7ed.com/images/invisible.gif" />
			</td>
			<td width="21%" class="language">English<a href="javascript:saveFavorite(195225,1,1)"><img title="Start following..." src="https://www.addic7ed.com/images/icons/favorite.png" height="20" width="20" border="0" /></a>    
			</td>
			<td width="19%"><b>Completed
		    </b>
		    </td>
		    <td colspan="3"><a class="face-button" href="/original/195225/1">  <div class="face-primary">
    <span class="icon fa fa-cloud"></span>
    Download
  </div>
          <div class="face-secondary">
    <span class="icon fa fa-hdd-o"></span>
    English
  </div></a></td>
		    <td>
		    </td>
		  </tr>
		  <tr>
		    <td colspan="2" class="newsDate" style="padding: 15px 0";><img title="Corrected" src="https://www.addic7ed.com/images/bullet_go.png" width="24" height="24" /><img title="Hearing Impaired" src="https://www.addic7ed.com/images/hi.jpg" width="24" height="24" />0 times edited · 816 Downloads · 640 sequences
		</td>
		    <td colspan="3"><img border="0" src="https://www.addic7ed.com/images/edit.png" width="24" height="24" /><a href="/index.php?id=195225&amp;fversion=1&amp;lang=1" onclick="return confirm('You must be a user for a few days before you can make edits.')">view &amp; edit</a>&nbsp;</table>
				</td>
				<td>&nbsp;</td>
			    </tr>
			    <tr> <!-- table footer -->
				<td class="tablecorner"><img src="https://www.addic7ed.com/images/bl.gif" /></td>
				<td>&nbsp;</td>
				<td class="tablecorner"><img src="https://www.addic7ed.com/images/br.gif" /></td>
			    </tr>
			</table>
		    </div>
	<div id="container95m">
		<table class="tabel95">
			<tr> <!-- table header -->
			<td class="tablecorner"><img src="https://www.addic7ed.com/images/tl.gif" /></td>
			<td>&nbsp;</td>
			<td class="tablecorner"><img src="https://www.addic7ed.com/images/tr.gif" /></td>
		    </tr>
		    <tr>
			<td>&nbsp;</td>
			<td>
	<table width="100%" border="0" align="center" class="tabel95">
	  <tr>
	    <td colspan="3" align="center" class="NewsTitle"><img src="https://www.addic7ed.com/images/folder_page.png" width="16" height="16" />Version FLUX+Kitsune+NTb+playWEB+SuccessfulCrab+SYLiX, Duration: 0.00 &nbsp;<img title="720/1080" src="https://www.addic7ed.com/images/hdicon.png" width="24" height="24" /></td><td colspan="1">
			<img src="https://www.addic7ed.com/images/movie_faq.png" title="LOL &amp; SYS always work with 720p DIMENSION;	XII & ASAP always work with 720p IMMERSE; 2HD always works with 720p 2HD; BiA always works with 720p BiA; FoV always works with 720p FoV" border="0" width="24" height="24" />
			<img src="https://www.addic7ed.com/images/subtitle.gif" width="22" height="14" /><a href="/index.php?id=195225&amp;fversion=1&amp;lang=1" onclick="return confirm('You must be a user for a few days before you can make edits.')">New translation</a><img src="https://www.addic7ed.com/images/link.png"  /> uploaded by <a href='/user/890973'>firefly</a>  3 days ago
		    </td>
		    <td width="16%">
		    </td>
		  </tr>
		  <tr>
			  <td colspan="4"></td>
			  <td class="newsDate" colspan="3">
				Duration: 44:34<img src="https://www.addic7ed.com/images/invisible.gif" />
			  </td>
		  </tr><tr><td width="10%" rowspan="2" valign="top"><a href="http://www.addic7ed.com"><img height="35" width="100" src="https://www.addic7ed.com/friends/addic7ed.png" border="0" /></a>&nbsp;&nbsp;&nbsp;</td>
			<td width="1%" rowspan="2" valign="top"><img src="https://www.addic7ed.com/images/invisible.gif" />
			</td>
			<td width="21%" class="language">English<a href="javascript:saveFavorite(195225,1,0)"><img title="Start following..." src="https://www.addic7ed.com/images/icons/favorite.png" height="20" width="20" border="0" /></a>    
			</td>
			<td width="19%"><b>Completed
		    </b>
		    </td>
		    <td colspan="3"><a class="face-button" href="/original/195225/0">  <div class="face-primary">
    <span class="icon fa fa-cloud"></span>
    Download
  </div>
          <div class="face-secondary">
    <span class="icon fa fa-hdd-o"></span>
    English
  </div></a></td>
		    <td>
		    </td>
		  </tr>
		  <tr>
		    <td colspan="2" class="newsDate" style="padding: 15px 0";><img title="Corrected" src="https://www.addic7ed.com/images/bullet_go.png" width="24" height="24" /><img src="https://www.addic7ed.com/images/icons/invisible.gif" width="1" height="1" />0 times edited · 646 Downloads · 463 sequences
		</td>
		    <td colspan="3"><img border="0" src="https://www.addic7ed.com/images/edit.png" width="24" height="24" /><a href="/index.php?id=195225&amp;fversion=0&amp;lang=1" onclick="return confirm('You must be a user for a few days before you can make edits.')">view &amp; edit</a>&nbsp;</table>
				</td>
				<td>&nbsp;</td>
			    </tr>
			    <tr> <!-- table footer -->
				<td class="tablecorner"><img src="https://www.addic7ed.com/images/bl.gif" /></td>
				<td>&nbsp;</td>
				<td class="tablecorner"><img src="https://www.addic7ed.com/images/br.gif" /></td>
			    </tr>
			</table>
		    </div>
	<div id="container95m">
		<table class="tabel95">
			<tr> <!-- table header -->
			<td class="tablecorner"><img src="https://www.addic7ed.com/images/tl.gif" /></td>
			<td>&nbsp;</td>
			<td class="tablecorner"><img src="https://www.addic7ed.com/images/tr.gif" /></td>
		    </tr>
		    <tr>
			<td>&nbsp;</td>
			<td>
	<table width="100%" border="0" align="center" class="tabel95">
	  <tr>
	    <td colspan="3" align="center" class="NewsTitle"><img src="https://www.addic7ed.com/images/folder_page.png" width="16" height="16" />Version FLUX+Kitsune+NTb+playWEB+SuccessfulCrab+SYLiX, Duration: 0.00 &nbsp;<img title="720/1080" src="https://www.addic7ed.com/images/hdicon.png" width="24" height="24" /></td><td colspan="1">
			<img src="https://www.addic7ed.com/images/movie_faq.png" title="LOL &amp; SYS always work with 720p DIMENSION;	XII & ASAP always work with 720p IMMERSE; 2HD always works with 720p 2HD; BiA always works with 720p BiA; FoV always works with 720p FoV" border="0" width="24" height="24" />
			<img src="https://www.addic7ed.com/images/subtitle.gif" width="22" height="14" /><a href="/index.php?id=195225&amp;fversion=0&amp;lang=1" onclick="return confirm('You must be a user for a few days before you can make edits.')">New translation</a><img src="https://www.addic7ed.com/images/link.png"  /> uploaded by <a href='/user/1031011'>actumaxime</a>  3 days ago
		    </td>
		    <td width="16%">
		    </td>
		  </tr>
		  <tr>
			  <td colspan="4"></td>
			  <td class="newsDate" colspan="3">
				Durée: 44:34<img src="https://www.addic7ed.com/images/invisible.gif" />
			  </td>
		  </tr><tr><td width="10%" rowspan="2" valign="top">&nbsp;&nbsp;&nbsp;</td>
			<td width="1%" rowspan="2" valign="top"><img src="https://www.addic7ed.com/images/invisible.gif" />
			</td>
			<td width="21%" class="language">French<a href="javascript:saveFavorite(195225,8,0)"><img title="Start following..." src="https://www.addic7ed.com/images/icons/favorite.png" height="20" width="20" border="0" /></a>    
			</td>
			<td width="19%"><b>Completed
		    </b>
		    </td>
		    <td colspan="3"><a class="face-button" href="/original/195225/2">  <div class="face-primary">
    <span class="icon fa fa-cloud"></span>
    Download
  </div>
          <div class="face-secondary">
    <span class="icon fa fa-hdd-o"></span>
    French
  </div></a></td>
		    <td>
		    </td>
		  </tr>
		  <tr>
		    <td colspan="2" class="newsDate" style="padding: 15px 0";><img title="Corrected" src="https://www.addic7ed.com/images/bullet_go.png" width="24" height="24" /><img src="https://www.addic7ed.com/images/icons/invisible.gif" width="1" height="1" />0 times edited · 263 Downloads · 408 sequences
		</td>
		    <td colspan="3"><img border="0" src="https://www.addic7ed.com/images/edit.png" width="24" height="24" /><a href="/index.php?id=195225&amp;fversion=2&amp;lang=8" onclick="return confirm('You must be a user for a few days before you can make edits.')">view &amp; edit</a>&nbsp;</table>
				</td>
				<td>&nbsp;</td>
			    </tr>
			    <tr> <!-- table footer -->
				<td class="tablecorner"><img src="https://www.addic7ed.com/images/bl.gif" /></td>
				<td>&nbsp;</td>
				<td class="tablecorner"><img src="https://www.addic7ed.com/images/br.gif" /></td>
			    </tr>
			</table>
		    </div>
	<div id="container95m">
		<table class="tabel95">
			<tr> <!-- table header -->
			<td class="tablecorner"><img src="https://www.addic7ed.com/images/tl.gif" /></td>
			<td>&nbsp;</td>
			<td class="tablecorner"><img src="https://www.addic7ed.com/images/tr.gif" /></td>
		    </tr>
		    <tr>
			<td>&nbsp;</td>
			<td>
	<table width="100%" border="0" align="center" class="tabel95">
	  <tr>
	    <td colspan="3" align="center" class="NewsTitle"><img src="https://www.addic7ed.com/images/folder_page.png" width="16" height="16" />Version successfulcrab, Duration: 0.00 &nbsp;<img title="720/1080" src="https://www.addic7ed.com/images/hdicon.png" width="24" height="24" /></td><td colspan="1">
			<img src="https://www.addic7ed.com/images/movie_faq.png" title="LOL &amp; SYS always work with 720p DIMENSION;	XII & ASAP always work with 720p IMMERSE; 2HD always works with 720p 2HD; BiA always works with 720p BiA; FoV always works with 720p FoV" border="0" width="24" height="24" />
			<img src="https://www.addic7ed.com/images/subtitle.gif" width="22" height="14" /><a href="/index.php?id=195225&amp;fversion=2&amp;lang=8" onclick="return confirm('You must be a user for a few days before you can make edits.')">New translation</a><img src="https://www.addic7ed.com/images/link.png"  /> uploaded by <a href='/user/912532'>TheLegender</a>  23 hours ago
		    </td>
		    <td width="16%">
		    </td>
		  </tr>
		  <tr>
			  <td colspan="4"></td>
			  <td class="newsDate" colspan="3">
				<img src="https://www.addic7ed.com/images/invisible.gif" />
			  </td>
		  </tr><tr><td width="10%" rowspan="2" valign="top">&nbsp;&nbsp;&nbsp;</td>
			<td width="1%" rowspan="2" valign="top"><img src="https://www.addic7ed.com/images/invisible.gif" />
			</td>
			<td width="21%" class="language">Portuguese (Brazilian)<a href="javascript:saveFavorite(195225,10,0)"><img title="Start following..." src="https://www.addic7ed.com/images/icons/favorite.png" height="20" width="20" border="0" /></a>    
			</td>
			<td width="19%"><b>Completed
		    </b>
		    </td>
		    <td colspan="3"><a class="face-button" href="/original/195225/3">  <div class="face-primary">
    <span class="icon fa fa-cloud"></span>
    Download
  </div>
          <div class="face-secondary">
    <span class="icon fa fa-hdd-o"></span>
    Portuguese (Brazilian)
  </div></a></td>
		    <td>
		    </td>
		  </tr>
		  <tr>
		    <td colspan="2" class="newsDate" style="padding: 15px 0";><img src="https://www.addic7ed.com/images/icons/invisible.gif" width="1" height="1" /><img src="https://www.addic7ed.com/images/icons/invisible.gif" width="1" height="1" />0 times edited · 3 Downloads · 436 sequences
		</td>
		    <td colspan="3"><img border="0" src="https://www.addic7ed.com/images/edit.png" width="24" height="24" /><a href="/index.php?id=195225&amp;fversion=3&amp;lang=10" onclick="return confirm('You must be a user for a few days before you can make edits.')">view &amp; edit</a>&nbsp;</table>
				</td>
				<td>&nbsp;</td>
			    </tr>
			    <tr> <!-- table footer -->
				<td class="tablecorner"><img src="https://www.addic7ed.com/images/bl.gif" /></td>
				<td>&nbsp;</td>
				<td class="tablecorner"><img src="https://www.addic7ed.com/images/br.gif" /></td>
			    </tr>
			</table>
		    </div><div align="center">
		</div><p>
		</p>
			<p>&nbsp;</p>
		<center>

					 
		</center>
	
		<br />
			<div id="container95m">
			
				<table class="tabel95">
					<tr> <!-- table header -->
					<td class="tablecorner"><img src="https://www.addic7ed.com/images/tl.gif" /></td>
					<td>&nbsp;</td>
					<td class="tablecorner"><img src="https://www.addic7ed.com/images/tr.gif" /></td>
				    </tr>
				    <tr>
					<td>&nbsp;</td>
					<td>
						<span id="comments">
						</span>
					</td>
					<td>&nbsp;</td>
				    </tr>
				    <tr> <!-- table footer -->
					<td class="tablecorner"><img src="https://www.addic7ed.com/images/bl.gif" /></td>
					<td>&nbsp;</td>
					<td class="tablecorner"><img src="https://www.addic7ed.com/images/br.gif" /></td>
				    </tr>
				</table>
			    </div>


		<script type="text/javascript">
			function carga()
	{
var subID='195225';		$("comments").innerHTML = '<img src="https://www.addic7ed.com/images/loader.gif">';
		var myRequest = new Request({
		    url: '/ajax_getComments.php',
		    method: 'get',
		    onSuccess: function(responseText){
			$("comments").innerHTML = responseText;
		    }
		});
		myRequest.send('id='+subID);
	}
		
	function enviar()
	{
		var paramString = $("newc").toQueryString(); 
		$("comments").innerHTML = '<img src="https://www.addic7ed.com/images/loader.gif">';
                var myRequest = new Request({
                    url: '/ajax_getComments.php',
                    method: 'post',
                    onSuccess: function(responseText){
			$("comments").innerHTML = responseText;
                    }
                });
                myRequest.send(paramString);
	
		return false;
	}
		
	function notifyModerator(fversion, lang)
	{
var subID='195225';		
		if (confirm('Are you sure that you want to report a problem to the moderators?'))
		{
			var myRequest = new Request({
			    url: '/ajax_notify.php',
			    method: 'get',
			    onSuccess: function(responseText){
				alert("The notification has sent to the moderators. Please, be patient");
			    }
			});
			myRequest.send('id='+subID+'&fversion='+fversion+'&lang='+lang);

			$("comments").innerHTML = '<img src="https://www.addic7ed.com/images/loader.gif">';
		}
	}


	function addCorrection(fversion, lang)
	{
var subID='195225';		
		if (confirm('Are you sure you want to correct this subtitle??'))
		{
			var myRequest = new Request({
                            url: '/ajax_correction.php',
                            method: 'get',
                            onSuccess: function(responseText){
				alert("Subtitle added to Corrections page. Please correct it or announce that you bail");
                            }
                        });
                        myRequest.send('id='+subID+'&fversion='+fversion+'&lang='+lang);
		}
	}

	function delComment(cid)
	{
		var myRequest = new Request({
		    url: '/ajax_delComment.php',
		    method: 'get',
		    onSuccess: function(responseText){
//			alert("Subtitle added to Corrections page. Please correct it or announce that you bail");
		    }
		});
		myRequest.send('cid='+cid);
		
		$("comments").innerHTML = "<img src='https://www.addic7ed.com/images/loader.gif' />";

		var myRequest = new Request({
                    url: '/ajax_getComments.php',
                    method: 'get',
                    onSuccess: function(responseText){
			    $("comments").innerHTML = responseText;
		    }       
                });     
		myRequest.send('cid='+cid);
	}

	window.addEvent('domready', function(){ carga(); });	
		</script>
		<center>
		
<center><table border="0" width="90%">
<tr>
<td class="NewsTitle"><img width="20" height="20" src="https://www.addic7ed.com/images/television.png" alt="TV" /><img src="https://www.addic7ed.com/images/invisible.gif" alt=" " />Addic7ed</td>
<td class="NewsTitle"><img width="20" height="20" src="https://www.addic7ed.com/images/television.png" alt="TV" /><img src="https://www.addic7ed.com/images/invisible.gif" alt=" " />Popular Shows</td>
<td class="NewsTitle"><img width="20" height="20" src="https://www.addic7ed.com/images/television.png" alt="TV" /><img src="https://www.addic7ed.com/images/invisible.gif" alt=" " />Useful</td>
<td class="NewsTitle"><img width="20" height="20" src="https://www.addic7ed.com/images/television.png" alt="TV" /><img src="https://www.addic7ed.com/images/invisible.gif" alt=" " />Forums</td>
</tr>
<tr>
<td><div id="footermenu"><a href="/shows.php">Browse By Shows</a></div></td>
<td><div id="footermenu"><a href="https://www.addic7ed.com/show/8439">Sex/Life</a></div></td>
<td><div id="footermenu"><a href="/shows-schedule">TV Shows Schedule</a></div></td>
<td><div id="footermenu"><a href="http://www.sub-talk.net/topic/1031-changelog/">Site Changelog</a></div></td>
</tr>
<tr>
<td><div id="footermenu"><a href="/movie-subtitles">Browse By Movies</a></div></td>
<td><div id="footermenu"><a href="https://www.addic7ed.com/show/8435">Loki</a></div></td>
<td><div id="footermenu"><a href="http://www.sub-talk.net/topic/2784-frequently-asked-questions/">Frequently Asked Questions</a></div></td>
<td><div id="footermenu">Support Us</div></td>
</tr>
<tr>
<td><div id="footermenu"><a href="/top-uploaders">Top Uploaders</a></div></td>
<td><div id="footermenu"><a href="https://www.addic7ed.com/show/8470">Resident Evil</a></div></td>
<td><div id="footermenu">RSS Feeds</div></td>
<td><div id="footermenu">Premium Accounts</div></td>
</tr>
<tr>
<td><div id="footermenu"><a href="/log.php?mode=downloaded">Top Downloads</a></div></td>
<td><div id="footermenu"><a href="https://www.addic7ed.com/show/8428">Sweet Tooth</a></div></td>
<td class="NewsTitle"><img width="20" height="20" src="https://www.addic7ed.com/images/television.png" alt="TV" /><img src="https://www.addic7ed.com/images/invisible.gif" alt=" "/>Tutorials</td>
<td><div id="footermenu"><a href="http://sub-talk.net/thread-6-1-1.html">Video Formats</a></div></td>
</tr>
<tr>
<td><div id="footermenu"><a href="/log.php?mode=news">All News</a></div></td>
<td><div id="footermenu"><a href="https://www.addic7ed.com/show/121">Gossip Girl</a></div></td>
<td><div id="footermenu"><a href="http://www.sub-talk.net/topic/338-guide-to-syncing-with-subtitleedit/page__p__1485__hl__%2B+%2Bsync__fromsearch__1#entry1485">How to Synchronize Subtitles</a></div></td>
<td><div id="footermenu">Frequently Asked Questions</div></td>
</tr> 
<tr>
<td><div id="footermenu"><a href="http://www.sub-talk.net">Sub-Talk Forums</a></div></td>
<td><div id="footermenu"><a href="/show/1277">Shameless (US)</a></div></td>
<td><div id="footermenu">What Are Subtitles</div></td>
<td><div id="footermenu"><a href="http://sub-talk.net/index.php?gid=7">TV Shows Talk</a></div></td>
</tr>
<tr>
<td><div id="footermenu"><a href="/latest_comments.php">Latest Comments</a></div></td>
<td><div id="footermenu"><a href="/show/126">The Big Bang Theory</a></div></td>
<td><div id="footermenu">New Translation Tutorial</div></td>
<td><div id="footermenu"><a href="http://sub-talk.net/index.php?gid=22">Movies Talk</a></div></td>
</tr>
<tr>
<td><div id="footermenu"><a href="https://www.fitint.ro/c/colanti/" title="Colanti modelatori" target="_blank" alt="Colanti modelatori">Colanti modelatori</a></div></td>
<td><div id="footermenu"><a href="/show/130">Family Guy</a></div></td>
<td><div id="footermenu">Upload a New Subtitle Tutorial</div></td>
<td class="NewsTitle"><img width="20" height="20" src="https://www.addic7ed.com/images/television.png" alt="TV" /><img src="https://www.addic7ed.com/images/invisible.gif" alt=" " />Stats</td>
</tr>
<tr>
<td><div id="footermenu"><a href="https://www.fitint.ro/c/costume-baie/" title="Costume de baie dama" target="_blank" alt="Costume de baie dama">Costume de baie</a></div></td>
<td><div id="footermenu"><a href="/show/1799">American Horror Story</a></div></td>
<td><div id="footermenu"><a href="http://sub-talk.net/viewthread.php?tid=294">How to have an Avatar</a></div></td>
<td align="left">.
				</td>
</tr>
<tr>
<td><div id="footermenu"><a href="/contact.php">Contact</a></div></td>
<td><div id="footermenu"><a href="/show/15">House</a></div></td>
<td><div id="footermenu"><a href="http://www.vreaubagaj.ro/" alt="Trolere" title="Trolere">Trolere</a></div></td>
<td>
</td>
</tr>
</table></center>
</center>

<script type="text/javascript">
var gaJsHost = (("https:" == document.location.protocol) ? "https://ssl." : "http://www.");
document.write(unescape("%3Cscript src='" + gaJsHost + "google-analytics.com/ga.js' type='text/javascript'%3E%3C/script%3E"));
</script>
<script type="text/javascript">
try {
var pageTracker = _gat._getTracker("UA-10775680-1");
pageTracker._trackPageview();
} catch(err) {}</script>



                                                                                      
cached = build time: 0.096333026885986 <br>		</center>
		
		</body>
		</html>


