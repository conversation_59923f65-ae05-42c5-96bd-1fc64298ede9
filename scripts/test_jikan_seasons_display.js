// File: scripts/test_jikan_seasons_display.js
require('dotenv').config();
const mongoose = require('mongoose');
const Anime = require('../src/db/models/Anime');
const { advancedEnrichItem } = require('../src/enrichment/services/advancedEnrichService');
const { saveToDB } = require('./saveToDB');

async function testJikanSeasonsDisplay() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI;
    console.log(`Connecting to MongoDB: ${mongoUri}`);
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');

    // Find an anime with Jikan seasons data
    const anime = await Anime.findOne({ 'jikanSeasons.0': { $exists: true } });
    
    if (!anime) {
      console.log('No anime with Jikan seasons data found. Testing with a specific anime...');
      
      // Test with a specific anime - "Les Carnets de l'Apothicaire"
      const specificAnime = await Anime.findOne({ title: /Apothicaire/i });
      
      if (!specificAnime) {
        console.log('Specific anime not found. Exiting...');
        await mongoose.disconnect();
        return;
      }
      
      console.log(`Found specific anime: ${specificAnime.title}`);
      console.log(`Jikan data: ${specificAnime.jikan?.mal_id ? 'Found' : 'Not found'}`);
      console.log(`Jikan Seasons: ${specificAnime.jikanSeasons?.length || 0}`);
      
      // If no Jikan seasons data, enrich the anime
      if (!specificAnime.jikanSeasons || specificAnime.jikanSeasons.length === 0) {
        console.log('No Jikan seasons data found. Enriching anime...');
        
        // Create a test item
        const testItem = {
          _id: specificAnime._id,
          title: specificAnime.title,
          metadata: specificAnime.metadata || { year: '2024' },
          type: 'anime',
          detailUrl: specificAnime.detailUrl || 'https://example.com/anime/test',
          detailUrlPath: specificAnime.detailUrlPath || '/anime/test'
        };
        
        // Enrich the item
        console.log(`Enriching anime: ${testItem.title}`);
        const enriched = await advancedEnrichItem(testItem, 'anime', {
          fetchSeasons: true,
          useAdvanced: true
        });
        
        // Log the results
        console.log(`Enrichment results for '${testItem.title}':`);
        
        console.log(`Jikan data: ${enriched.jikan?.mal_id ? 'Found' : 'Not found'}`);
        if (enriched.jikan?.mal_id) {
          console.log(`MAL ID: ${enriched.jikan.mal_id}`);
          console.log(`MAL Title: ${enriched.jikan.title?.default || enriched.jikan.title?.english || 'N/A'}`);
          console.log(`Jikan Seasons: ${enriched.jikanSeasons?.length || 0}`);
          
          // Log each season
          if (enriched.jikanSeasons && enriched.jikanSeasons.length > 0) {
            enriched.jikanSeasons.forEach((season, index) => {
              console.log(`Season ${index + 1}:`);
              console.log(`  Title: ${season.title || 'N/A'}`);
              console.log(`  MAL ID: ${season.mal_id || 'N/A'}`);
              console.log(`  Season Number: ${season.season_number || 'N/A'}`);
              console.log(`  Episodes: ${season.episodes || 'N/A'}`);
              console.log(`  Score: ${season.score || 'N/A'}`);
              console.log(`  Year: ${season.year || 'N/A'}`);
            });
          }
        }
        
        // Save the enriched item to the database
        console.log(`Saving enriched anime to database...`);
        await saveToDB([enriched], 'anime');
        
        // Verify the update
        const updatedAnime = await Anime.findById(specificAnime._id);
        console.log(`\nVerifying database update for ${updatedAnime.title}:`);
        
        console.log(`Jikan data in DB: ${updatedAnime.jikan?.mal_id ? 'Found' : 'Not found'}`);
        if (updatedAnime.jikan?.mal_id) {
          console.log(`MAL ID in DB: ${updatedAnime.jikan.mal_id}`);
          console.log(`MAL Title in DB: ${updatedAnime.jikan.title?.default || updatedAnime.jikan.title?.english || 'N/A'}`);
          console.log(`Jikan Seasons in DB: ${updatedAnime.jikanSeasons?.length || 0}`);
          
          // Log each season
          if (updatedAnime.jikanSeasons && updatedAnime.jikanSeasons.length > 0) {
            updatedAnime.jikanSeasons.forEach((season, index) => {
              console.log(`Season ${index + 1}:`);
              console.log(`  Title: ${season.title || 'N/A'}`);
              console.log(`  MAL ID: ${season.mal_id || 'N/A'}`);
              console.log(`  Season Number: ${season.season_number || 'N/A'}`);
              console.log(`  Episodes: ${season.episodes || 'N/A'}`);
              console.log(`  Score: ${season.score || 'N/A'}`);
              console.log(`  Year: ${season.year || 'N/A'}`);
            });
          }
        }
      } else {
        console.log('Jikan seasons data found:');
        specificAnime.jikanSeasons.forEach((season, index) => {
          console.log(`Season ${index + 1}:`);
          console.log(`  Title: ${season.title || 'N/A'}`);
          console.log(`  MAL ID: ${season.mal_id || 'N/A'}`);
          console.log(`  Season Number: ${season.season_number || 'N/A'}`);
          console.log(`  Episodes: ${season.episodes || 'N/A'}`);
          console.log(`  Score: ${season.score || 'N/A'}`);
          console.log(`  Year: ${season.year || 'N/A'}`);
        });
      }
    } else {
      console.log(`Found anime with Jikan seasons data: ${anime.title}`);
      console.log(`Jikan data: ${anime.jikan?.mal_id ? 'Found' : 'Not found'}`);
      console.log(`Jikan Seasons: ${anime.jikanSeasons?.length || 0}`);
      
      // Log each season
      if (anime.jikanSeasons && anime.jikanSeasons.length > 0) {
        anime.jikanSeasons.forEach((season, index) => {
          console.log(`Season ${index + 1}:`);
          console.log(`  Title: ${season.title || 'N/A'}`);
          console.log(`  MAL ID: ${season.mal_id || 'N/A'}`);
          console.log(`  Season Number: ${season.season_number || 'N/A'}`);
          console.log(`  Episodes: ${season.episodes || 'N/A'}`);
          console.log(`  Score: ${season.score || 'N/A'}`);
          console.log(`  Year: ${season.year || 'N/A'}`);
        });
      }
    }

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error(`Error testing Jikan seasons display: ${error.message}`);
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
    }
  }
}

testJikanSeasonsDisplay();
