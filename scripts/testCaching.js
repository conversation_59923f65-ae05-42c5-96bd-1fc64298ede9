// Test script to verify caching functionality
require('dotenv').config();
const mongoose = require('mongoose');
const fs = require('fs');
const { enrichItemWithOptions } = require('../src/enrichment/services/enrichService');

// Define timeout function
const timeout = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Create a custom logger that writes to a file
const logFile = fs.createWriteStream('/root/app/NetStream_graphql/caching_test.log', { flags: 'w' });
const logger = {
  info: (message) => {
    const logMessage = `[INFO] ${message}`;
    console.log(logMessage);
    logFile.write(logMessage + '\n');
  },
  error: (message) => {
    const logMessage = `[ERROR] ${message}`;
    console.error(logMessage);
    logFile.write(logMessage + '\n');
  }
};

const env = require('../src/config/env');

// Test series titles to process multiple times
const testTitles = [
  'The Handmaid\'s Tale',
  'You',
  'Black Mirror',
  'Daredevil: Born Again',
  'HPI'
];

async function testCaching() {
  try {
    // Connect to MongoDB
    await mongoose.connect(env.mongoUri);
    logger.info('Connected to MongoDB');

    // Process each title twice to test caching
    for (let i = 0; i < 2; i++) {
      logger.info(`=== ITERATION ${i+1} ===`);

      for (const title of testTitles) {
        const startTime = Date.now();
        logger.info(`Processing title: ${title}`);

        // Create a dummy item
        const dummyItem = {
          title: title,
          type: 'series'
        };

        // Enrich the item
        const enriched = await enrichItemWithOptions(dummyItem, 'series', {
          fetchSeasons: true,
          useAdvanced: true
        });

        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;

        logger.info(`Completed enrichment for ${title} in ${duration}s`);
        logger.info(`TMDB ID: ${enriched.tmdb?.id || 'Not found'}`);
        logger.info(`TMDB Seasons: ${enriched.tmdbSeasons?.length || 0}`);
        logger.info('-----------------------------------');

        // Add a small delay between items
        await timeout(1000);
      }

      // Add a delay between iterations
      if (i < 1) {
        logger.info('Waiting 5 seconds before next iteration...');
        await timeout(5000);
      }
    }

    logger.info('Test completed successfully');
    logFile.end();
    setTimeout(() => process.exit(0), 500); // Give time for the file to close
  } catch (error) {
    logger.error(`Error in test: ${error.message}`);
    logFile.end();
    setTimeout(() => process.exit(1), 500); // Give time for the file to close
  }
}

// Run the test
testCaching();
