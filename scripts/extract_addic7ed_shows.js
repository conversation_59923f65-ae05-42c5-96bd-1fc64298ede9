/**
 * <PERSON><PERSON><PERSON> to extract the complete list of shows from Addic7ed's homepage
 * This script uses <PERSON><PERSON>pet<PERSON> to visit the site and extract all options from the dropdown
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// Configure the output file path
const outputFilePath = path.join(__dirname, '..', 'src', 'data', 'addic7ed_shows.json');

// Make sure the directory exists
const outputDir = path.dirname(outputFilePath);
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Function to extract shows from Addic7ed
async function extractAddic7edShows() {
  console.log('Launching browser...');
  
  // Launch a headless browser
  const browser = await puppeteer.launch({
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    console.log('Opening new page...');
    const page = await browser.newPage();
    
    // Set a user agent to avoid being blocked
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
    
    // Navigate to Addic7ed homepage
    console.log('Navigating to Addic7ed homepage...');
    await page.goto('https://www.addic7ed.com/', { waitUntil: 'networkidle2', timeout: 60000 });
    
    // Wait for the dropdown to be available
    console.log('Waiting for the shows dropdown to load...');
    await page.waitForSelector('#qsShow', { timeout: 30000 });
    
    // Extract all options from the dropdown
    console.log('Extracting shows from dropdown...');
    const shows = await page.evaluate(() => {
      const options = Array.from(document.querySelector('#qsShow').options);
      return options
        .map(option => ({
          id: option.value,
          name: option.textContent.trim()
        }))
        .filter(show => show.id !== '0'); // Filter out the "Choose a TV Show" option
    });
    
    console.log(`Extracted ${shows.length} shows from Addic7ed`);
    
    // Extract just the names for the simple list
    const showNames = shows.map(show => show.name);
    
    // Save the full data (with IDs) to a JSON file
    fs.writeFileSync(outputFilePath, JSON.stringify(shows, null, 2));
    console.log(`Saved full show data to ${outputFilePath}`);
    
    // Save just the names to a simpler file for easy import
    const simpleOutputFilePath = path.join(__dirname, '..', 'src', 'data', 'addic7ed_show_names.json');
    fs.writeFileSync(simpleOutputFilePath, JSON.stringify(showNames, null, 2));
    console.log(`Saved show names to ${simpleOutputFilePath}`);
    
    return shows;
  } catch (error) {
    console.error('Error extracting shows:', error);
    throw error;
  } finally {
    // Close the browser
    await browser.close();
    console.log('Browser closed');
  }
}

// Run the extraction
(async () => {
  try {
    await extractAddic7edShows();
    console.log('Extraction completed successfully');
  } catch (error) {
    console.error('Extraction failed:', error);
    process.exit(1);
  }
})();
