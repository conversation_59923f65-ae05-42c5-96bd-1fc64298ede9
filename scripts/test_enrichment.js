// File: scripts/test_enrichment.js
/**
 * Test script for the enrichment process
 *
 * This script tests the enrichment process with a few sample items.
 * It can be used to verify that the enrichment process works correctly.
 *
 * Usage:
 *   node scripts/test_enrichment.js [--advanced] [--no-seasons] [--type=<type>] [--title=<title>]
 *
 * Options:
 *   --advanced: Use advanced enrichment with Gemini AI
 *   --no-seasons: Don't fetch seasons data
 *   --type=<type>: The type of item to test (movie, series, anime)
 *   --title=<title>: The title of the item to test
 */

const mongoose = require('mongoose');
const { mongoUri } = require('../src/config/env');
const { enrichItemWithOptions, getConfig } = require('../src/enrichment/services/enrichService');
const { updateConfig } = require('../src/enrichment/config/enrichmentConfig');
const logger = require('../src/utils/logger');

// Parse command line arguments
const args = process.argv.slice(2);
const useAdvancedArg = args.includes('--advanced');
const useBasicArg = args.includes('--basic');
const fetchSeasons = !args.includes('--no-seasons');

// Determine if we should use advanced enrichment
// If --advanced is specified, use advanced enrichment
// If --basic is specified, use basic enrichment
// Otherwise, use the default configuration
const useAdvanced = useAdvancedArg || (!useBasicArg && require('../src/enrichment/config/enrichmentConfig').getConfig().USE_ADVANCED_MATCHING);

// Get type and title from command line arguments
let type = 'series';
let title = 'Stranger Things';

for (const arg of args) {
  if (arg.startsWith('--type=')) {
    type = arg.split('=')[1];
  } else if (arg.startsWith('--title=')) {
    title = arg.split('=')[1];
  }
}

// Validate type
if (!['movie', 'series', 'anime'].includes(type)) {
  console.error(`Invalid type: ${type}. Must be one of: movie, series, anime`);
  process.exit(1);
}

// Sample items for testing
const testItems = {
  movie: [
    { title: 'Inception', metadata: { year: '2010' } },
    { title: 'The Matrix', metadata: { year: '1999' } },
    { title: 'Interstellar', metadata: { year: '2014' } }
  ],
  series: [
    { title: 'Stranger Things', metadata: { year: '2016' } },
    { title: 'Breaking Bad', metadata: { year: '2008' } },
    { title: 'Game of Thrones', metadata: { year: '2011' } }
  ],
  anime: [
    { title: 'Attack on Titan', metadata: { year: '2013' } },
    { title: 'One Piece', metadata: { year: '1999' } },
    { title: 'Naruto', metadata: { year: '2002' } }
  ]
};

// Get the test item
let testItem;
if (title) {
  // Find the item with the specified title
  testItem = { title, metadata: {} };
} else {
  // Use the first item from the test items
  testItem = testItems[type][0];
}

// Connect to MongoDB
async function connectDB() {
  try {
    await mongoose.connect(mongoUri);
    logger.info('Connected to MongoDB');
  } catch (error) {
    logger.error(`Failed to connect to MongoDB: ${error.message}`);
    process.exit(1);
  }
}

// Test the enrichment process
async function testEnrichment() {
  try {
    // Connect to MongoDB
    await connectDB();

    // Update configuration
    updateConfig({
      FETCH_SEASONS: fetchSeasons,
      USE_ADVANCED_MATCHING: useAdvanced,
      USE_GEMINI: useAdvanced
    });

    // Log the configuration
    logger.info(`Testing enrichment with configuration: ${JSON.stringify(getConfig())}`);
    logger.info(`Testing ${useAdvanced ? 'advanced' : 'basic'} enrichment for ${type}: ${testItem.title}`);

    // Enrich the item
    const enrichedItem = await enrichItemWithOptions(testItem, type, {
      useAdvanced,
      fetchSeasons
    });

    // Log the results
    logger.info(`Enrichment completed for ${type}: ${testItem.title}`);

    // Log TMDB data
    if (enrichedItem.tmdb && enrichedItem.tmdb.id) {
      logger.info(`TMDB ID: ${enrichedItem.tmdb.id}`);
      logger.info(`TMDB Title: ${enrichedItem.tmdb.title}`);
      logger.info(`TMDB Overview: ${enrichedItem.tmdb.overview?.substring(0, 100)}...`);

      if (enrichedItem.tmdbSeasons && enrichedItem.tmdbSeasons.length > 0) {
        logger.info(`TMDB Seasons: ${enrichedItem.tmdbSeasons.length}`);
        enrichedItem.tmdbSeasons.forEach(season => {
          logger.info(`  Season ${season.season_number}: ${season.name} (${season.episodes?.length || 0} episodes)`);
        });
      } else {
        logger.info('No TMDB seasons found');
      }
    } else {
      logger.info('No TMDB data found');
    }

    // Log Jikan data
    if (type === 'anime' && enrichedItem.jikan && enrichedItem.jikan.mal_id) {
      logger.info(`Jikan MAL ID: ${enrichedItem.jikan.mal_id}`);
      logger.info(`Jikan Title: ${enrichedItem.jikan.title?.default}`);

      if (enrichedItem.jikanSeasons && enrichedItem.jikanSeasons.length > 0) {
        logger.info(`Jikan Seasons: ${enrichedItem.jikanSeasons.length}`);
        enrichedItem.jikanSeasons.forEach(season => {
          logger.info(`  Season ${season.season_number}: ${season.title} (${season.episodes || 0} episodes)`);
        });
      } else {
        logger.info('No Jikan seasons found');
      }
    } else if (type === 'anime') {
      logger.info('No Jikan data found');
    }

    // Disconnect from MongoDB
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  } catch (error) {
    logger.error(`Error testing enrichment: ${error.message}`);
    console.error(error);
  }
}

// Run the test
testEnrichment().catch(error => {
  logger.error(`Unhandled error: ${error.message}`);
  console.error(error);
  process.exit(1);
});
