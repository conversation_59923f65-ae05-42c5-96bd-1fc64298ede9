// File: scripts/test_enrichment_update.js
require('dotenv').config();
const mongoose = require('mongoose');
const Anime = require('../src/db/models/Anime');
const { advancedEnrichItem } = require('../src/enrichment/services/advancedEnrichService');
const { translateToEnglish } = require('../src/utils/translationUtils');
const { saveToDB } = require('./saveToDB');

async function testEnrichmentUpdate() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI;
    console.log(`Connecting to MongoDB: ${mongoUri}`);
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');

    // IDs to check
    const animeIds = [
      '67df4509cac26cbd6eb59827',  // Les Carnets de l'Apothicaire
      '680eb6bce55988ee0c97cd64'   // The Gorilla God's Go-To Girl
    ];

    // Process each anime
    for (const animeId of animeIds) {
      console.log(`\n=== Processing anime with ID: ${animeId} ===`);

      const anime = await Anime.findById(animeId);

      if (!anime) {
        console.log(`Anime with ID ${animeId} not found`);
        continue;
      }

      console.log(`Title: ${anime.title}`);

      // Create a test item
      const testItem = {
        _id: anime._id,
        title: anime.title,
        metadata: anime.metadata || { year: '2024' },
        type: 'anime',
        detailUrl: anime.detailUrl || 'https://example.com/anime/test',
        detailUrlPath: anime.detailUrlPath || '/anime/test'
      };

      // Enrich the item
      console.log(`Enriching anime: ${testItem.title}`);
      const enriched = await advancedEnrichItem(testItem, 'anime', {
        fetchSeasons: true,
        useAdvanced: true
      });

      // Log the results
      console.log(`Enrichment results for '${testItem.title}':`);

      console.log(`Jikan data: ${enriched.jikan?.mal_id ? 'Found' : 'Not found'}`);
      if (enriched.jikan?.mal_id) {
        console.log(`MAL ID: ${enriched.jikan.mal_id}`);
        console.log(`MAL Title: ${enriched.jikan.title?.default || enriched.jikan.title?.english || 'N/A'}`);
        console.log(`Jikan Seasons: ${enriched.jikanSeasons?.length || 0}`);
      }

      console.log(`TMDB data: ${enriched.tmdb?.id ? 'Found' : 'Not found'}`);
      if (enriched.tmdb?.id) {
        console.log(`TMDB ID: ${enriched.tmdb.id}`);
        console.log(`TMDB Title: ${enriched.tmdb.title || 'N/A'}`);
        console.log(`TMDB Seasons: ${enriched.tmdbSeasons?.length || 0}`);
      }

      // Save the enriched item to the database
      console.log(`Saving enriched anime to database...`);
      await saveToDB([enriched], 'anime');

      // Verify the update
      const updatedAnime = await Anime.findById(animeId);
      console.log(`\nVerifying database update for ${updatedAnime.title}:`);

      console.log(`Jikan data in DB: ${updatedAnime.jikan?.mal_id ? 'Found' : 'Not found'}`);
      if (updatedAnime.jikan?.mal_id) {
        console.log(`MAL ID in DB: ${updatedAnime.jikan.mal_id}`);
        console.log(`MAL Title in DB: ${updatedAnime.jikan.title?.default || updatedAnime.jikan.title?.english || 'N/A'}`);
        console.log(`Jikan Seasons in DB: ${updatedAnime.jikanSeasons?.length || 0}`);
      }

      console.log(`TMDB data in DB: ${updatedAnime.tmdb?.id ? 'Found' : 'Not found'}`);
      if (updatedAnime.tmdb?.id) {
        console.log(`TMDB ID in DB: ${updatedAnime.tmdb.id}`);
        console.log(`TMDB Title in DB: ${updatedAnime.tmdb.title || 'N/A'}`);
        console.log(`TMDB Seasons in DB: ${updatedAnime.tmdbSeasons?.length || 0}`);
      }
    }

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error(`Error testing enrichment update: ${error.message}`);
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
    }
  }
}

testEnrichmentUpdate();
