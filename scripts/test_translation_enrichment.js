// File: scripts/test_translation_enrichment.js
require('dotenv').config();
const mongoose = require('mongoose');
const logger = require('../src/utils/logger');
const { advancedEnrichItem } = require('../src/enrichment/services/advancedEnrichService');
const { translateToEnglish } = require('../src/utils/translationUtils');

async function testTranslationEnrichment() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI;
    logger.info(`Connecting to MongoDB: ${mongoUri}`);
    await mongoose.connect(mongoUri);
    logger.info('Connected to MongoDB');

    // Test cases
    const testCases = [
      {
        title: "Les Carnets de l'Apothicaire",
        type: 'anime',
        metadata: { year: '2024' }
      },
      {
        title: "La Casa de Papel",
        type: 'series',
        metadata: { year: '2017' }
      },
      {
        title: "Le Seigneur des Anneaux",
        type: 'movie',
        metadata: { year: '2001' }
      }
    ];

    // Process each test case
    for (const testCase of testCases) {
      logger.info(`\n=== Testing enrichment for: ${testCase.title} (${testCase.type}) ===`);
      
      // First, try direct translation
      logger.info(`Testing direct translation...`);
      const translatedTitle = await translateToEnglish(testCase.title);
      if (translatedTitle) {
        logger.info(`Translated title: '${testCase.title}' -> '${translatedTitle}'`);
      } else {
        logger.warn(`Translation failed for '${testCase.title}'`);
      }
      
      // Now test the advanced enrichment with retry mechanism
      logger.info(`Testing advanced enrichment with retry mechanism...`);
      const enriched = await advancedEnrichItem(testCase, testCase.type, {
        fetchSeasons: true,
        useAdvanced: true
      });
      
      // Log the results
      logger.info(`Enrichment results for '${testCase.title}':`);
      
      if (testCase.type === 'anime') {
        logger.info(`Jikan data: ${enriched.jikan?.mal_id ? 'Found' : 'Not found'}`);
        if (enriched.jikan?.mal_id) {
          logger.info(`MAL ID: ${enriched.jikan.mal_id}`);
          logger.info(`MAL Title: ${enriched.jikan.title?.default || enriched.jikan.title?.english || 'N/A'}`);
          logger.info(`Jikan Seasons: ${enriched.jikanSeasons?.length || 0}`);
        }
      }
      
      logger.info(`TMDB data: ${enriched.tmdb?.id ? 'Found' : 'Not found'}`);
      if (enriched.tmdb?.id) {
        logger.info(`TMDB ID: ${enriched.tmdb.id}`);
        logger.info(`TMDB Title: ${enriched.tmdb.title || 'N/A'}`);
        logger.info(`TMDB Seasons: ${enriched.tmdbSeasons?.length || 0}`);
      }
    }

    // Disconnect from MongoDB
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  } catch (error) {
    logger.error(`Error testing translation enrichment: ${error.message}`);
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
    }
  }
}

testTranslationEnrichment();
