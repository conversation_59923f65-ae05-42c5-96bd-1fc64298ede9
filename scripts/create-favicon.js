/**
 * Simple script to create a basic favicon for NetStream
 */
const fs = require('fs');
const path = require('path');

// Base64 encoded PNG data for a 32x32 favicon with "NT" text
// This is a blue square with white "NT" text
const faviconBase64 = `
iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAB
hUlEQVR4nO2XvUoDQRDHf3d7iYKFIFiIhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWF
hYWFhYWFhYWFhYWFhYWFYHGXu9u7cS8RYhLQwmZgdubbnX/+mV2hlMJ/LgUA0+l0r9frPQyHw0fX
dV/zPH9XSi0JIRbbtv1omqY7HA7v+v3+U7fbfQMgtwKqqroeDAYPSZK8CyG+lFLfUsqFbdsfnuc9
J0ny1ul0bsbj8RUAswXQdf2y3W6fW5b1yRhbUEoTQoiFEGLOGFtYlvUZBMG5YRgXADQAiDAMbxlj
C8aYIoQoxpiilCrOuXIcR4VheAsAzLbto8FgcJ+m6cx13ZfJZHISx/ExgDPHcZ6yLJtFUXSUJMle
FEVHWZbNsiw7DYLgbDab7QOA4Jx/13V9Xdf1erPZrJumWa7X6/VqtVqt1+v1crlcbjabTdM0y7qu
15zz7z+X+QfZN/1GIGBtjuGuAAAAAElFTkSuQmCC
`;

// Create the favicon directory if it doesn't exist
const faviconDir = path.join(__dirname, '..', 'public', 'favicon');
if (!fs.existsSync(faviconDir)) {
  fs.mkdirSync(faviconDir, { recursive: true });
}

// Write the favicon files
const faviconData = Buffer.from(faviconBase64.replace(/\s/g, ''), 'base64');
fs.writeFileSync(path.join(__dirname, '..', 'public', 'favicon.ico'), faviconData);
fs.writeFileSync(path.join(__dirname, '..', 'public', 'favicon.png'), faviconData);

// Create larger versions for apple-touch-icon
fs.writeFileSync(path.join(faviconDir, 'apple-touch-icon.png'), faviconData);
fs.writeFileSync(path.join(faviconDir, 'android-chrome-192x192.png'), faviconData);
fs.writeFileSync(path.join(faviconDir, 'android-chrome-512x512.png'), faviconData);

console.log('Basic favicon files created successfully!');
