// File: scripts/verify_enrichment_config.js
require('dotenv').config();
const { getConfig } = require('../src/enrichment/config/enrichmentConfig');
const { initEnrichmentConfig } = require('../src/config/enrichmentOptions');

// Initialize the enrichment configuration
const enrichmentConfig = initEnrichmentConfig();
console.log('Enrichment configuration:');
console.log(JSON.stringify(enrichmentConfig, null, 2));

// Get the current configuration
const currentConfig = getConfig();
console.log('Current configuration:');
console.log(JSON.stringify(currentConfig, null, 2));

// Check environment variables
console.log('Environment variables:');
console.log(`FETCH_SEASONS: ${process.env.FETCH_SEASONS}`);
console.log(`USE_ADVANCED_ENRICHMENT: ${process.env.USE_ADVANCED_ENRICHMENT}`);
console.log(`USE_GEMINI: ${process.env.USE_GEMINI}`);
console.log(`GEMINI_API_KEY: ${process.env.GEMINI_API_KEY ? 'Set' : 'Not set'}`);
