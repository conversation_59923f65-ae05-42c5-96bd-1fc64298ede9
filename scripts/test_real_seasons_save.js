// File: scripts/test_real_seasons_save.js
require('dotenv').config();
const mongoose = require('mongoose');
const logger = require('../src/utils/logger');
const Series = require('../src/db/models/Series');
const { enrichItem, enrichItemWithOptions } = require('../src/enrichment/services/enrichService');
const { saveToDB } = require('./saveToDB');
const { tmdbApiKey } = require('../src/config/env');
const axios = require('axios');

async function searchTmdb(title) {
  try {
    const url = `https://api.themoviedb.org/3/search/tv?api_key=${tmdbApiKey}&query=${encodeURIComponent(title)}&language=fr-FR`;
    const { data } = await axios.get(url);
    return data.results[0];
  } catch (error) {
    logger.error(`Error searching TMDB: ${error.message}`);
    return null;
  }
}

async function testRealSeasonsSave() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI;
    logger.info(`Connecting to MongoDB: ${mongoUri}`);
    await mongoose.connect(mongoUri);
    logger.info('Connected to MongoDB');

    // Create a test series with a real title that will match TMDB
    const testSeries = {
      title: "Stranger Things",
      detailUrl: "https://example.com/stranger-things",
      detailUrlPath: "/stranger-things-test",
      season: "1",
      episodes: [
        {
          episodeNumber: "1",
          season: "1",
          streamingUrls: [
            {
              url: "https://example.com/stream1",
              provider: "test",
              language: "VF"
            }
          ]
        }
      ]
    };

    // First, verify that we can find this series on TMDB
    const tmdbResult = await searchTmdb("Stranger Things");
    if (!tmdbResult) {
      logger.error("Could not find Stranger Things on TMDB. Test cannot continue.");
      await mongoose.disconnect();
      return;
    }

    logger.info(`Found Stranger Things on TMDB with ID: ${tmdbResult.id}`);

    // Enrich the test series with advanced options to ensure we get seasons
    logger.info("Enriching test series with advanced options...");
    const enriched = await enrichItemWithOptions(testSeries, 'series', {
      useAdvanced: true,
      fetchSeasons: true
    });

    // Log the enriched data
    logger.info(`Enriched series has tmdb: ${enriched.tmdb ? 'Yes' : 'No'}`);
    if (enriched.tmdb) {
      logger.info(`TMDB ID: ${enriched.tmdb.id}`);
    }

    logger.info(`Enriched series has tmdbSeasons: ${enriched.tmdbSeasons ? 'Yes' : 'No'}`);
    if (enriched.tmdbSeasons) {
      logger.info(`Number of seasons: ${enriched.tmdbSeasons.length}`);
      logger.info(`First season: ${JSON.stringify(enriched.tmdbSeasons[0].name)}`);
    }

    // Create the series data object
    const seriesData = {
      title: enriched.title || "Test Series",
      detailUrl: enriched.detailUrl,
      detailUrlPath: enriched.detailUrlPath,
      cleanedTitle: enriched.cleanedTitle || enriched.title,
      season: enriched.season || '1',
      episodes: enriched.episodes || [],
      metadata: enriched.metadata || {},
      tmdb: enriched.tmdb || null,
      tmdbSeasons: enriched.tmdbSeasons || null,
      tmdbSeason: enriched.tmdbSeason || null
    };

    // Save to database
    logger.info("Saving test series to database...");
    await saveToDB('series', [seriesData]);
    logger.info("Test series saved to database");

    // Verify the saved data
    const savedSeries = await Series.findOne({ title: "Stranger Things" });
    if (!savedSeries) {
      logger.error("Could not find saved series in database");
    } else {
      logger.info(`Retrieved series from database: ${savedSeries.title}`);
      logger.info(`Has tmdb: ${savedSeries.tmdb ? 'Yes' : 'No'}`);
      if (savedSeries.tmdb) {
        logger.info(`TMDB ID in database: ${savedSeries.tmdb.id}`);
      }

      logger.info(`Has tmdbSeasons: ${savedSeries.tmdbSeasons ? 'Yes' : 'No'}`);
      if (savedSeries.tmdbSeasons) {
        logger.info(`Number of seasons in database: ${savedSeries.tmdbSeasons.length}`);
        logger.info(`First season in database: ${JSON.stringify(savedSeries.tmdbSeasons[0].name)}`);
      }
    }

    // Don't clean up, we'll keep the test data for verification

    // Disconnect from MongoDB
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  } catch (error) {
    logger.error(`Error testing real seasons save: ${error.message}`);
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
    }
  }
}

testRealSeasonsSave();
