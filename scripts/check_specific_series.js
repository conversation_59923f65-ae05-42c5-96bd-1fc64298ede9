// File: scripts/check_specific_series.js
require('dotenv').config();
const mongoose = require('mongoose');
const logger = require('../src/utils/logger');
const Series = require('../src/db/models/Series');
const { enrichItemWithOptions } = require('../src/enrichment/services/enrichService');
const { saveToDB } = require('./saveToDB');

async function checkAndUpdateSeries() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI;
    logger.info(`Connecting to MongoDB: ${mongoUri}`);
    await mongoose.connect(mongoUri);
    logger.info('Connected to MongoDB');

    // Find the specific series
    const seriesId = '6817adcb882c61756bece555';
    const series = await Series.findById(seriesId);
    
    if (!series) {
      logger.error(`Series with ID ${seriesId} not found`);
      await mongoose.disconnect();
      return;
    }
    
    logger.info(`Found series: ${series.title}`);
    logger.info(`TMDB ID: ${series.tmdb?.id || 'None'}`);
    logger.info(`Has tmdbSeasons: ${series.tmdbSeasons ? 'Yes' : 'No'}`);
    if (series.tmdbSeasons) {
      logger.info(`Number of seasons: ${series.tmdbSeasons.length}`);
    }

    // Re-enrich the series with advanced options
    logger.info("Re-enriching series with advanced options...");
    const enriched = await enrichItemWithOptions(series.toObject(), 'series', {
      useAdvanced: true,
      fetchSeasons: true
    });
    
    // Log the enriched data
    logger.info(`Re-enriched series has tmdb: ${enriched.tmdb ? 'Yes' : 'No'}`);
    if (enriched.tmdb) {
      logger.info(`TMDB ID: ${enriched.tmdb.id}`);
    }
    
    logger.info(`Re-enriched series has tmdbSeasons: ${enriched.tmdbSeasons ? 'Yes' : 'No'}`);
    if (enriched.tmdbSeasons) {
      logger.info(`Number of seasons: ${enriched.tmdbSeasons.length}`);
      logger.info(`First season: ${JSON.stringify(enriched.tmdbSeasons[0].name)}`);
    }

    // Update the series in the database
    logger.info("Updating series in database...");
    await saveToDB('series', [enriched]);
    logger.info("Series updated in database");

    // Verify the updated data
    const updatedSeries = await Series.findById(seriesId);
    logger.info(`Retrieved updated series from database: ${updatedSeries.title}`);
    logger.info(`Has tmdb: ${updatedSeries.tmdb ? 'Yes' : 'No'}`);
    if (updatedSeries.tmdb) {
      logger.info(`TMDB ID in database: ${updatedSeries.tmdb.id}`);
    }
    
    logger.info(`Has tmdbSeasons: ${updatedSeries.tmdbSeasons ? 'Yes' : 'No'}`);
    if (updatedSeries.tmdbSeasons) {
      logger.info(`Number of seasons in database: ${updatedSeries.tmdbSeasons.length}`);
      logger.info(`First season in database: ${JSON.stringify(updatedSeries.tmdbSeasons[0].name)}`);
    }

    // Disconnect from MongoDB
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  } catch (error) {
    logger.error(`Error checking and updating series: ${error.message}`);
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
    }
  }
}

checkAndUpdateSeries();
