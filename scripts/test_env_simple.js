// File: scripts/test_env_simple.js
require('dotenv').config();

console.log('Environment Variables:');
console.log('GEMINI_API_KEY:', process.env.GEMINI_API_KEY ? 'Set (hidden for security)' : 'Not set');
console.log('USE_ADVANCED_ENRICHMENT:', process.env.USE_ADVANCED_ENRICHMENT);

// Initialize enrichment configuration
const { initEnrichmentConfig } = require('../src/config/enrichmentOptions');
const enrichmentConfig = initEnrichmentConfig();
console.log('\nEnrichment Configuration:');
console.log(JSON.stringify(enrichmentConfig, null, 2));

// Check if Gemini AI is available
const { isGeminiAvailable } = require('../src/enrichment/services/advancedEnrichService');
console.log('\nGemini AI Available:', isGeminiAvailable());

console.log('\nAdvanced Enrichment Enabled:', enrichmentConfig.USE_ADVANCED_MATCHING);
