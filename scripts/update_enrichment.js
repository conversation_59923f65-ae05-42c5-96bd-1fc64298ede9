// File: scripts/update_enrichment.js
/**
 * <PERSON><PERSON><PERSON> to update the enrichment for existing items in the database
 * 
 * This script updates the enrichment for existing items in the database.
 * It can be used to add seasons data to existing items.
 * 
 * Usage:
 *   node scripts/update_enrichment.js [--advanced] [--type=<type>] [--limit=<limit>] [--id=<id>]
 * 
 * Options:
 *   --advanced: Use advanced enrichment with Gemini AI
 *   --type=<type>: The type of items to update (movie, series, anime, all)
 *   --limit=<limit>: The maximum number of items to update
 *   --id=<id>: Update a specific item by ID
 */

const mongoose = require('mongoose');
const { mongoUri } = require('../src/config/env');
const { enrichItemWithOptions } = require('../src/enrichment/services/enrichService');
const { updateConfig } = require('../src/enrichment/config/enrichmentConfig');
const { saveToDB } = require('./saveToDB');
const logger = require('../src/utils/logger');
const Movie = require('../src/db/models/Movie');
const Series = require('../src/db/models/Series');
const Anime = require('../src/db/models/Anime');

// Parse command line arguments
const args = process.argv.slice(2);
const useAdvanced = args.includes('--advanced');

// Get type, limit, and id from command line arguments
let type = 'all';
let limit = 10;
let id = null;

for (const arg of args) {
  if (arg.startsWith('--type=')) {
    type = arg.split('=')[1];
  } else if (arg.startsWith('--limit=')) {
    limit = parseInt(arg.split('=')[1], 10);
  } else if (arg.startsWith('--id=')) {
    id = arg.split('=')[1];
  }
}

// Validate type
if (!['movie', 'series', 'anime', 'all'].includes(type)) {
  console.error(`Invalid type: ${type}. Must be one of: movie, series, anime, all`);
  process.exit(1);
}

// Connect to MongoDB
async function connectDB() {
  try {
    await mongoose.connect(mongoUri);
    logger.info('Connected to MongoDB');
  } catch (error) {
    logger.error(`Failed to connect to MongoDB: ${error.message}`);
    process.exit(1);
  }
}

// Update the enrichment for a single item
async function updateItemEnrichment(item, itemType) {
  try {
    logger.info(`Updating enrichment for ${itemType}: ${item.title}`);
    
    // Enrich the item
    const enrichedItem = await enrichItemWithOptions(item, itemType, {
      useAdvanced,
      fetchSeasons: true
    });
    
    // Save the enriched item to the database
    await saveToDB(itemType, [enrichedItem]);
    
    return true;
  } catch (error) {
    logger.error(`Error updating enrichment for ${itemType} ${item.title}: ${error.message}`);
    return false;
  }
}

// Update the enrichment for all items of a specific type
async function updateTypeEnrichment(itemType, itemLimit) {
  try {
    const Model = itemType === 'movie' ? Movie :
      itemType === 'series' ? Series :
        itemType === 'anime' ? Anime : null;
    
    if (!Model) {
      logger.error(`Invalid type: ${itemType}`);
      return 0;
    }
    
    // Find items to update
    const query = id ? { _id: id } : {};
    const items = await Model.find(query).limit(itemLimit);
    
    logger.info(`Found ${items.length} ${itemType}s to update`);
    
    // Update each item
    let successCount = 0;
    for (const item of items) {
      const success = await updateItemEnrichment(item.toObject(), itemType);
      if (success) successCount++;
    }
    
    logger.info(`Successfully updated ${successCount} out of ${items.length} ${itemType}s`);
    
    return successCount;
  } catch (error) {
    logger.error(`Error updating ${itemType}s: ${error.message}`);
    return 0;
  }
}

// Update the enrichment for all items
async function updateAllEnrichment() {
  try {
    // Connect to MongoDB
    await connectDB();
    
    // Update configuration
    updateConfig({
      FETCH_SEASONS: true
    });
    
    // Log the configuration
    logger.info(`Updating enrichment with ${useAdvanced ? 'advanced' : 'basic'} enrichment`);
    
    let totalSuccessCount = 0;
    
    // Update each type
    if (type === 'all' || type === 'movie') {
      const movieSuccessCount = await updateTypeEnrichment('movie', limit);
      totalSuccessCount += movieSuccessCount;
    }
    
    if (type === 'all' || type === 'series') {
      const seriesSuccessCount = await updateTypeEnrichment('series', limit);
      totalSuccessCount += seriesSuccessCount;
    }
    
    if (type === 'all' || type === 'anime') {
      const animeSuccessCount = await updateTypeEnrichment('anime', limit);
      totalSuccessCount += animeSuccessCount;
    }
    
    logger.info(`Total items successfully updated: ${totalSuccessCount}`);
    
    // Disconnect from MongoDB
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  } catch (error) {
    logger.error(`Error updating enrichment: ${error.message}`);
    console.error(error);
  }
}

// Run the update
updateAllEnrichment().catch(error => {
  logger.error(`Unhandled error: ${error.message}`);
  console.error(error);
  process.exit(1);
});
