// File: scripts/test_scraper_seasons.js
require('dotenv').config();
const mongoose = require('mongoose');
const logger = require('../src/utils/logger');
const { scrapeWiflixSeries } = require('./scrapeWiflixSeries');
const { saveToDB } = require('./saveToDB');
const Series = require('../src/db/models/Series');

async function testScraperSeasons() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI;
    logger.info(`Connecting to MongoDB: ${mongoUri}`);
    await mongoose.connect(mongoUri);
    logger.info('Connected to MongoDB');

    // Set environment variables
    process.env.FETCH_SEASONS = 'true';
    process.env.USE_ADVANCED_ENRICHMENT = 'true';
    
    // Run the scraper with a small page limit
    logger.info('Running scraper with page limit 1 and latest mode');
    const results = await scrapeWiflixSeries(1, saveToDB, true);
    
    logger.info(`<PERSON>raper returned ${results.length} results`);
    
    // Check if any of the results have tmdbSeasons
    if (results.length > 0) {
      for (const result of results) {
        logger.info(`Checking result: ${result.title}`);
        
        // Find the series in the database
        const series = await Series.findOne({ detailUrlPath: result.detailUrlPath });
        
        if (series) {
          logger.info(`Found series in database: ${series.title}`);
          logger.info(`Has tmdbSeasons: ${series.tmdbSeasons ? 'Yes' : 'No'}`);
          if (series.tmdbSeasons) {
            logger.info(`Number of seasons: ${series.tmdbSeasons.length}`);
          }
        } else {
          logger.warn(`Series not found in database: ${result.title}`);
        }
      }
    }
    
    // Disconnect from MongoDB
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  } catch (error) {
    logger.error(`Error testing scraper seasons: ${error.message}`);
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
    }
  }
}

testScraperSeasons();
