// File: scripts/verify_seasons.js
require('dotenv').config();
const mongoose = require('mongoose');
const logger = require('../src/utils/logger');
const Series = require('../src/db/models/Series');
const Anime = require('../src/db/models/Anime');

async function verifySeasons() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI;
    logger.info(`Connecting to MongoDB: ${mongoUri}`);
    await mongoose.connect(mongoUri);
    logger.info('Connected to MongoDB');

    // Check series with seasons
    const seriesWithSeasons = await Series.find({
      tmdbSeasons: { $exists: true, $ne: [] }
    }).select('title tmdbSeasons.length');
    
    logger.info(`Found ${seriesWithSeasons.length} series with seasons data`);
    
    // Display the first 5 series with seasons
    for (let i = 0; i < Math.min(5, seriesWithSeasons.length); i++) {
      const series = seriesWithSeasons[i];
      logger.info(`Series: ${series.title}, Seasons: ${series.tmdbSeasons.length}`);
    }

    // Check anime with seasons
    const animeWithSeasons = await Anime.find({
      $or: [
        { tmdbSeasons: { $exists: true, $ne: [] } },
        { jikanSeasons: { $exists: true, $ne: [] } }
      ]
    }).select('title tmdbSeasons jikanSeasons');
    
    logger.info(`Found ${animeWithSeasons.length} anime with seasons data`);
    
    // Display the first 5 anime with seasons
    for (let i = 0; i < Math.min(5, animeWithSeasons.length); i++) {
      const anime = animeWithSeasons[i];
      logger.info(`Anime: ${anime.title}, TMDB Seasons: ${anime.tmdbSeasons?.length || 0}, Jikan Seasons: ${anime.jikanSeasons?.length || 0}`);
    }

    // Disconnect from MongoDB
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  } catch (error) {
    logger.error(`Error verifying seasons: ${error.message}`);
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
    }
  }
}

verifySeasons();
