// Script to fix "Les Carnets de l'Apothicaire" anime with correct Jikan data
require('dotenv').config();
const mongoose = require('mongoose');
const Anime = require('../src/db/models/Anime');
const logger = require('../src/utils/logger');
const { translateToEnglish } = require('../src/utils/translationUtils');
const jikanService = require('../src/enrichment/jikan/jikanService');

async function fixApothecaryAnime() {
  try {
    console.log('Starting fix script...');
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI;
    logger.info(`Connecting to MongoDB: ${mongoUri}`);
    await mongoose.connect(mongoUri);
    logger.info('Connected to MongoDB');

    // Find the anime
    const animeId = '67df4509cac26cbd6eb59827';
    const anime = await Anime.findById(animeId);

    if (!anime) {
      logger.error(`Anime with ID ${animeId} not found`);
      return;
    }

    logger.info(`Found anime: ${anime.title}`);
    logger.info(`Current Jikan MAL ID: ${anime.jikan?.mal_id}`);
    logger.info(`Current Jikan title: ${anime.jikan?.title?.default}`);
    logger.info(`Current Jikan seasons count: ${anime.jikanSeasons?.length}`);

    // Translate the title to English
    logger.info(`Translating "${anime.title}" to English...`);
    const translatedTitle = await translateToEnglish(anime.title);
    logger.info(`Translated title: "${translatedTitle}"`);

    // Fetch Jikan data using the translated title
    logger.info(`Fetching Jikan data for "${translatedTitle}"...`);
    const jikanResult = await jikanService.searchAnime(translatedTitle);

    if (!jikanResult) {
      logger.error(`Failed to fetch Jikan data for "${translatedTitle}"`);
      return;
    }

    const malId = jikanResult.mal_id;
    logger.info(`Found Jikan match: ${jikanResult.title} (MAL ID: ${malId})`);

    // Get full Jikan data
    const fullJikanData = await jikanService.getAnimeById(malId);
    if (!fullJikanData) {
      logger.error(`Failed to fetch full Jikan data for MAL ID ${malId}`);
      return;
    }

    // Format Jikan data
    const formattedJikanData = jikanService.formatJikanData(fullJikanData);

    // Fetch Jikan seasons
    logger.info(`Fetching Jikan seasons for MAL ID ${malId}...`);
    const seasonsResult = await jikanService.getAnimeSeasons(malId, anime.season ? parseInt(anime.season, 10) : null);

    if (!seasonsResult.success) {
      logger.error(`Failed to fetch Jikan seasons: ${seasonsResult.error}`);
      return;
    }

    logger.info(`Found ${seasonsResult.seasons.length} Jikan seasons`);

    // Update the anime
    logger.info(`Updating anime in database...`);
    const updateResult = await Anime.updateOne(
      { _id: animeId },
      {
        $set: {
          jikan: formattedJikanData,
          jikanSeasons: seasonsResult.seasons,
          updatedAt: new Date()
        }
      }
    );

    logger.info(`Update result: ${JSON.stringify(updateResult)}`);

    // Verify the update
    const updatedAnime = await Anime.findById(animeId);
    logger.info(`Updated Jikan MAL ID: ${updatedAnime.jikan?.mal_id}`);
    logger.info(`Updated Jikan title: ${updatedAnime.jikan?.title?.default}`);
    logger.info(`Updated Jikan seasons count: ${updatedAnime.jikanSeasons?.length}`);
    logger.info(`Updated at: ${updatedAnime.updatedAt}`);

    logger.info('Fix completed successfully');
  } catch (error) {
    logger.error(`Error fixing anime: ${error.message}`);
  } finally {
    // Disconnect from MongoDB
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  }
}

// Run the function
fixApothecaryAnime();
