// File: scripts/update_all_seasons.js
require('dotenv').config();
const mongoose = require('mongoose');
const logger = require('../src/utils/logger');
const Series = require('../src/db/models/Series');
const Anime = require('../src/db/models/Anime');
const { enrichItemWithOptions } = require('../src/enrichment/services/enrichService');
const { saveToDB } = require('./saveToDB');
const pLimit = require('p-limit');

// Limit concurrent operations to avoid overwhelming the API
const limit = pLimit(5);

async function updateAllSeasons() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI;
    logger.info(`Connecting to MongoDB: ${mongoUri}`);
    await mongoose.connect(mongoUri);
    logger.info('Connected to MongoDB');

    // Get command line arguments
    const args = process.argv.slice(2);
    const useAdvanced = args.includes('--advanced');
    const limitCount = args.find(arg => arg.startsWith('--limit='))?.split('=')[1] || 10;
    const typeArg = args.find(arg => arg.startsWith('--type='))?.split('=')[1];
    
    logger.info(`Update options: Advanced=${useAdvanced}, Limit=${limitCount}, Type=${typeArg || 'all'}`);

    // Update series
    if (!typeArg || typeArg === 'series') {
      await updateSeriesSeasons(parseInt(limitCount), useAdvanced);
    }

    // Update anime
    if (!typeArg || typeArg === 'anime') {
      await updateAnimeSeasons(parseInt(limitCount), useAdvanced);
    }

    // Disconnect from MongoDB
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  } catch (error) {
    logger.error(`Error updating seasons: ${error.message}`);
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
    }
  }
}

async function updateSeriesSeasons(limitCount, useAdvanced) {
  // Find series with TMDB ID but no seasons
  const query = {
    'tmdb.id': { $exists: true, $ne: null },
    $or: [
      { tmdbSeasons: { $exists: false } },
      { tmdbSeasons: { $size: 0 } }
    ]
  };
  
  const series = await Series.find(query).limit(limitCount);
  logger.info(`Found ${series.length} series to update with seasons data`);

  let successCount = 0;
  let errorCount = 0;

  // Process each series
  await Promise.all(series.map(item => limit(async () => {
    try {
      logger.info(`Processing series: ${item.title} (TMDB ID: ${item.tmdb?.id})`);
      
      // Enrich the series
      const enriched = await enrichItemWithOptions(item.toObject(), 'series', {
        useAdvanced,
        fetchSeasons: true
      });
      
      if (enriched.tmdbSeasons && enriched.tmdbSeasons.length > 0) {
        logger.info(`Enriched ${item.title} with ${enriched.tmdbSeasons.length} seasons`);
        
        // Save to database
        await saveToDB('series', [enriched]);
        logger.info(`Updated series: ${item.title}`);
        successCount++;
      } else {
        logger.warn(`No seasons found for ${item.title}`);
        errorCount++;
      }
    } catch (error) {
      logger.error(`Error updating series ${item.title}: ${error.message}`);
      errorCount++;
    }
  })));

  logger.info(`Series update complete: ${successCount} successful, ${errorCount} failed`);
}

async function updateAnimeSeasons(limitCount, useAdvanced) {
  // Find anime with Jikan ID but no seasons
  const query = {
    'jikan.mal_id': { $exists: true, $ne: null },
    $or: [
      { jikanSeasons: { $exists: false } },
      { jikanSeasons: { $size: 0 } }
    ]
  };
  
  const animes = await Anime.find(query).limit(limitCount);
  logger.info(`Found ${animes.length} anime to update with seasons data`);

  let successCount = 0;
  let errorCount = 0;

  // Process each anime
  await Promise.all(animes.map(item => limit(async () => {
    try {
      logger.info(`Processing anime: ${item.title} (MAL ID: ${item.jikan?.mal_id})`);
      
      // Enrich the anime
      const enriched = await enrichItemWithOptions(item.toObject(), 'anime', {
        useAdvanced,
        fetchSeasons: true
      });
      
      if ((enriched.tmdbSeasons && enriched.tmdbSeasons.length > 0) || 
          (enriched.jikanSeasons && enriched.jikanSeasons.length > 0)) {
        logger.info(`Enriched ${item.title} with ${enriched.tmdbSeasons?.length || 0} TMDB seasons and ${enriched.jikanSeasons?.length || 0} Jikan seasons`);
        
        // Save to database
        await saveToDB('anime', [enriched]);
        logger.info(`Updated anime: ${item.title}`);
        successCount++;
      } else {
        logger.warn(`No seasons found for ${item.title}`);
        errorCount++;
      }
    } catch (error) {
      logger.error(`Error updating anime ${item.title}: ${error.message}`);
      errorCount++;
    }
  })));

  logger.info(`Anime update complete: ${successCount} successful, ${errorCount} failed`);
}

updateAllSeasons();
