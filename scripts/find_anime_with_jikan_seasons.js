// File: scripts/find_anime_with_jikan_seasons.js
require('dotenv').config();
const mongoose = require('mongoose');
const Anime = require('../src/db/models/Anime');

async function findAnimeWithJikanSeasons() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI;
    console.log(`Connecting to MongoDB: ${mongoUri}`);
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');

    // Find an anime with Jikan seasons data
    const anime = await Anime.findOne({ 'jikanSeasons.0': { $exists: true } });
    
    if (anime) {
      console.log('Found anime with Jikan seasons:', anime.title, anime._id);
      console.log('Jikan seasons count:', anime.jikanSeasons.length);
      
      // Log each season
      if (anime.jikanSeasons && anime.jikanSeasons.length > 0) {
        anime.jikanSeasons.forEach((season, index) => {
          console.log(`Season ${index + 1}:`);
          console.log(`  Title: ${season.title || 'N/A'}`);
          console.log(`  MAL ID: ${season.mal_id || 'N/A'}`);
          console.log(`  Season Number: ${season.season_number || 'N/A'}`);
          console.log(`  Episodes: ${season.episodes || 'N/A'}`);
          console.log(`  Score: ${season.score || 'N/A'}`);
          console.log(`  Year: ${season.year || 'N/A'}`);
        });
      }
    } else {
      console.log('No anime with Jikan seasons found');
    }

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error(`Error finding anime with Jikan seasons: ${error.message}`);
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
    }
  }
}

findAnimeWithJikanSeasons();
