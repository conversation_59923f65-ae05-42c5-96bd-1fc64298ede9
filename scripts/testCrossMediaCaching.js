// Test script to verify cross-media type caching
require('dotenv').config();
const mongoose = require('mongoose');
const { enrichItemWithOptions } = require('../src/enrichment/services/enrichService');
const fs = require('fs');

// Define timeout function
const timeout = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Create a custom logger that writes to a file
const logFile = fs.createWriteStream('/root/app/NetStream_graphql/cross_media_caching_test.log', { flags: 'w' });
const logger = {
  info: (message) => {
    const logMessage = `[INFO] ${message}`;
    console.log(logMessage);
    logFile.write(logMessage + '\n');
  },
  error: (message) => {
    const logMessage = `[ERROR] ${message}`;
    console.error(logMessage);
    logFile.write(logMessage + '\n');
  }
};

const env = require('../src/config/env');

// Test items for different media types
const testItems = [
  { title: 'The Handmaid\'s Tale', type: 'series' },
  { title: 'Inception', type: 'movie' },
  { title: 'Attack on Titan', type: 'anime' },
  { title: 'Stranger Things', type: 'series' },
  { title: 'Spirited Away', type: 'anime' }
];

async function testCrossMediaCaching() {
  try {
    // Connect to MongoDB
    await mongoose.connect(env.mongoUri);
    logger.info('Connected to MongoDB');

    // Process each item twice to test caching
    for (let i = 0; i < 2; i++) {
      logger.info(`=== ITERATION ${i+1} ===`);
      
      for (const item of testItems) {
        const startTime = Date.now();
        logger.info(`Processing ${item.type}: ${item.title}`);
        
        // Enrich the item
        const enriched = await enrichItemWithOptions(item, item.type, {
          fetchSeasons: true,
          useAdvanced: true
        });
        
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;
        
        logger.info(`Completed enrichment for ${item.title} (${item.type}) in ${duration}s`);
        
        if (item.type === 'anime') {
          logger.info(`TMDB ID: ${enriched.tmdb?.id || 'Not found'}`);
          logger.info(`TMDB Seasons: ${enriched.tmdbSeasons?.length || 0}`);
          logger.info(`Jikan MAL ID: ${enriched.jikan?.mal_id || 'Not found'}`);
          logger.info(`Jikan Seasons: ${enriched.jikanSeasons?.length || 0}`);
        } else {
          logger.info(`TMDB ID: ${enriched.tmdb?.id || 'Not found'}`);
          logger.info(`TMDB Seasons: ${item.type === 'series' ? (enriched.tmdbSeasons?.length || 0) : 'N/A'}`);
        }
        
        logger.info('-----------------------------------');
        
        // Add a small delay between items
        await timeout(1000);
      }
      
      // Add a delay between iterations
      if (i < 1) {
        logger.info('Waiting 5 seconds before next iteration...');
        await timeout(5000);
      }
    }
    
    logger.info('Test completed successfully');
    logFile.end();
    setTimeout(() => process.exit(0), 500); // Give time for the file to close
  } catch (error) {
    logger.error(`Error in test: ${error.message}`);
    logFile.end();
    setTimeout(() => process.exit(1), 500); // Give time for the file to close
  }
}

// Run the test
testCrossMediaCaching();
