// File: scripts/test_seasons_save.js
require('dotenv').config();
const mongoose = require('mongoose');
const logger = require('../src/utils/logger');
const Series = require('../src/db/models/Series');
const { enrichItem } = require('../src/enrichment/services/enrichService');
const { saveToDB } = require('./saveToDB');

async function testSeasonsSave() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI;
    logger.info(`Connecting to MongoDB: ${mongoUri}`);
    await mongoose.connect(mongoUri);
    logger.info('Connected to MongoDB');

    // Create a test series
    const testSeries = {
      title: "Test Series With Seasons",
      detailUrl: "https://example.com/test-series",
      detailUrlPath: "/test-series",
      season: "1",
      episodes: [
        {
          episodeNumber: "1",
          season: "1",
          streamingUrls: [
            {
              url: "https://example.com/stream1",
              provider: "test",
              language: "VF"
            }
          ]
        }
      ]
    };

    // Enrich the test series
    logger.info("Enriching test series...");
    const enriched = await enrichItem(testSeries, 'series');
    
    // Log the enriched data
    logger.info(`Enriched series has tmdbSeasons: ${enriched.tmdbSeasons ? 'Yes' : 'No'}`);
    if (enriched.tmdbSeasons) {
      logger.info(`Number of seasons: ${enriched.tmdbSeasons.length}`);
    }

    // Create the series data object
    const seriesData = {
      title: enriched.title || "Test Series",
      detailUrl: enriched.detailUrl,
      detailUrlPath: enriched.detailUrlPath,
      cleanedTitle: enriched.cleanedTitle || enriched.title,
      season: enriched.season || '1',
      episodes: enriched.episodes || [],
      metadata: enriched.metadata || {},
      tmdb: enriched.tmdb || null,
      tmdbSeasons: enriched.tmdbSeasons || null,
      tmdbSeason: enriched.tmdbSeason || null
    };

    // Save to database
    logger.info("Saving test series to database...");
    await saveToDB('series', [seriesData]);
    logger.info("Test series saved to database");

    // Verify the saved data
    const savedSeries = await Series.findOne({ detailUrlPath: "/test-series" });
    logger.info(`Retrieved series from database: ${savedSeries.title}`);
    logger.info(`Has tmdbSeasons: ${savedSeries.tmdbSeasons ? 'Yes' : 'No'}`);
    if (savedSeries.tmdbSeasons) {
      logger.info(`Number of seasons in database: ${savedSeries.tmdbSeasons.length}`);
    }

    // Clean up
    logger.info("Cleaning up test data...");
    await Series.deleteOne({ detailUrlPath: "/test-series" });
    logger.info("Test data cleaned up");

    // Disconnect from MongoDB
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  } catch (error) {
    logger.error(`Error testing seasons save: ${error.message}`);
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
    }
  }
}

testSeasonsSave();
