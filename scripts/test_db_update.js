// File: scripts/test_db_update.js
require('dotenv').config();
const mongoose = require('mongoose');
// Use console instead of logger for direct output
const logger = {
  info: (message) => console.log(`INFO: ${message}`),
  warn: (message) => console.warn(`WARN: ${message}`),
  error: (message) => console.error(`ERROR: ${message}`)
};
const Series = require('../src/db/models/Series');
const Anime = require('../src/db/models/Anime');

async function testDbUpdate() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI;
    logger.info(`Connecting to MongoDB: ${mongoUri}`);
    await mongoose.connect(mongoUri);
    logger.info('Connected to MongoDB');

    // Test updating a series
    const series = await Series.findOne({});
    if (series) {
      logger.info(`Found series: ${series.title}`);
      logger.info(`Current tmdbSeasons: ${series.tmdbSeasons ? series.tmdbSeasons.length : 0}`);
      logger.info(`Current jikanSeasons: ${series.jikanSeasons ? series.jikanSeasons.length : 0}`);

      // Create a test update
      const update = {
        $set: {
          updatedAt: new Date()
        }
      };

      // Add tmdbSeasons if they exist
      if (series.tmdbSeasons && series.tmdbSeasons.length > 0) {
        update.$set.tmdbSeasons = series.tmdbSeasons;
        logger.info(`Adding ${series.tmdbSeasons.length} tmdbSeasons to update`);
      } else {
        // Create a test tmdbSeasons array
        update.$set.tmdbSeasons = [{
          air_date: '2021-01-01',
          tmdb_season_id: 12345,
          name: 'Test Season',
          overview: 'Test overview',
          poster_path: '/test/path.jpg',
          season_number: 1,
          vote_average: 8.5,
          episodes: []
        }];
        logger.info('Adding test tmdbSeasons to update');
      }

      // Update the series
      logger.info(`Updating series: ${series.title}`);
      const result = await Series.updateOne({ _id: series._id }, update);
      logger.info(`Update result: ${JSON.stringify(result)}`);

      // Verify the update
      const updatedSeries = await Series.findOne({ _id: series._id });
      logger.info(`Updated series: ${updatedSeries.title}`);
      logger.info(`Updated tmdbSeasons: ${updatedSeries.tmdbSeasons ? updatedSeries.tmdbSeasons.length : 0}`);
    } else {
      logger.warn('No series found');
    }

    // Test updating an anime
    const anime = await Anime.findOne({});
    if (anime) {
      logger.info(`Found anime: ${anime.title}`);
      logger.info(`Current tmdbSeasons: ${anime.tmdbSeasons ? anime.tmdbSeasons.length : 0}`);
      logger.info(`Current jikanSeasons: ${anime.jikanSeasons ? anime.jikanSeasons.length : 0}`);

      // Create a test update
      const update = {
        $set: {
          updatedAt: new Date()
        }
      };

      // Add jikanSeasons if they exist
      if (anime.jikanSeasons && anime.jikanSeasons.length > 0) {
        update.$set.jikanSeasons = anime.jikanSeasons;
        logger.info(`Adding ${anime.jikanSeasons.length} jikanSeasons to update`);
      } else {
        // Create a test jikanSeasons array
        update.$set.jikanSeasons = [{
          mal_id: 12345,
          title: 'Test Season',
          title_english: 'Test Season',
          title_japanese: 'テストシーズン',
          season_number: 1,
          episodes: 12,
          aired: {
            from: new Date(),
            to: new Date(),
            string: '2021-01-01 to 2021-03-31'
          },
          synopsis: 'Test synopsis',
          score: 8.5,
          season: 'Winter',
          year: 2021,
          url: 'https://myanimelist.net/anime/12345'
        }];
        logger.info('Adding test jikanSeasons to update');
      }

      // Update the anime
      logger.info(`Updating anime: ${anime.title}`);
      const result = await Anime.updateOne({ _id: anime._id }, update);
      logger.info(`Update result: ${JSON.stringify(result)}`);

      // Verify the update
      const updatedAnime = await Anime.findOne({ _id: anime._id });
      logger.info(`Updated anime: ${updatedAnime.title}`);
      logger.info(`Updated jikanSeasons: ${updatedAnime.jikanSeasons ? updatedAnime.jikanSeasons.length : 0}`);
    } else {
      logger.warn('No anime found');
    }

    // Disconnect from MongoDB
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  } catch (error) {
    logger.error(`Error testing DB update: ${error.message}`);
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
    }
  }
}

testDbUpdate();
