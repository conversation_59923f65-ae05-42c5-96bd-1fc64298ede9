// File: scripts/test_env.js
require('dotenv').config();

console.log('Environment Variables:');
console.log('GEMINI_API_KEY:', process.env.GEMINI_API_KEY);
console.log('ENRICHMENT_MODE:', process.env.ENRICHMENT_MODE);
console.log('USE_GEMINI:', process.env.USE_GEMINI);
console.log('FETCH_SEASONS:', process.env.FETCH_SEASONS);
console.log('USE_ADVANCED_MATCHING:', process.env.USE_ADVANCED_MATCHING);

// Initialize enrichment configuration
const { initEnrichmentConfig } = require('../src/config/enrichmentOptions');
const enrichmentConfig = initEnrichmentConfig();
console.log('\nEnrichment Configuration:');
console.log(JSON.stringify(enrichmentConfig, null, 2));

// Check if Gemini AI is available
const { isGeminiAvailable } = require('../src/enrichment/services/advancedEnrichService');
console.log('\nGemini AI Available:', isGeminiAvailable());
