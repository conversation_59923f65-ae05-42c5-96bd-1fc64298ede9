// File: scripts/check_specific_anime.js
require('dotenv').config();
const mongoose = require('mongoose');
const Anime = require('../src/db/models/Anime');

async function checkSpecificAnime() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI;
    console.log(`Connecting to MongoDB: ${mongoUri}`);
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');

    // IDs to check
    const animeIds = [
      
      '67df453acac26cbd6eb59be8'
    ];

    // Check each anime
    for (const animeId of animeIds) {
      console.log(`\n=== Checking anime with ID: ${animeId} ===`);
      
      const anime = await Anime.findById(animeId);
      
      if (!anime) {
        console.log(`Anime with ID ${animeId} not found`);
        continue;
      }
      
      console.log(`Title: ${anime.title}`);
      console.log(`Original Title: ${anime.originalTitle || 'N/A'}`);
      console.log(`Cleaned Title: ${anime.cleanedTitle || 'N/A'}`);
      
      // Check TMDB data
      if (anime.tmdb && Object.keys(anime.tmdb).length > 0) {
        console.log(`TMDB ID: ${anime.tmdb.id || 'N/A'}`);
        console.log(`TMDB Title: ${anime.tmdb.title || 'N/A'}`);
      } else {
        console.log('No TMDB data');
      }
      
      // Check TMDB Seasons
      if (anime.tmdbSeasons && anime.tmdbSeasons.length > 0) {
        console.log(`TMDB Seasons: ${anime.tmdbSeasons.length}`);
        anime.tmdbSeasons.forEach((season, index) => {
          console.log(`  Season ${index + 1}: ${season.name || 'N/A'} (${season.season_number || 'N/A'})`);
        });
      } else {
        console.log('No TMDB Seasons data');
      }
      
      // Check Jikan data
      if (anime.jikan && Object.keys(anime.jikan).length > 0) {
        console.log(`Jikan MAL ID: ${anime.jikan.mal_id || 'N/A'}`);
        console.log(`Jikan Title: ${anime.jikan.title?.default || anime.jikan.title?.english || 'N/A'}`);
      } else {
        console.log('No Jikan data');
      }
      
      // Check Jikan Seasons
      if (anime.jikanSeasons && anime.jikanSeasons.length > 0) {
        console.log(`Jikan Seasons: ${anime.jikanSeasons.length}`);
        anime.jikanSeasons.forEach((season, index) => {
          console.log(`  Season ${index + 1}: ${season.title || 'N/A'}`);
        });
      } else {
        console.log('No Jikan Seasons data');
      }
    }

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error(`Error checking specific anime: ${error.message}`);
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
    }
  }
}

checkSpecificAnime();
