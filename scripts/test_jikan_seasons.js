/**
 * test_jikan_seasons.js
 * 
 * This script tests the Jikan seasons fetching functionality.
 * It fetches seasons data for a specific anime or a sample of anime from the database.
 * 
 * Usage: node scripts/test_jikan_seasons.js [options]
 * Options:
 *   --id=<id>        Test a specific anime by ID
 *   --mal-id=<id>    Test a specific anime by MAL ID
 *   --sample=<N>     Number of anime to sample from the database (default: 5)
 *   --verbose        Show detailed output for each test
 *   --update-db      Update the database with fetched seasons data
 */

require('dotenv').config();
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const jikanSeasonsFetcher = require('./jikan_seasons_fetcher');

// Import Mongoose Models
const Anime = require('../src/db/models/Anime');

// Configuration
const MONGO_URI = process.env.MONGO_URI;
const LOG_FILE = path.join(__dirname, 'jikan_seasons_test_results.log');
const RESULTS_FILE = path.join(__dirname, 'jikan_seasons_test_results.json');
const API_REQUEST_DELAY_MS = 1000; // 1 second to respect Jikan's rate limit

// Setup Logging
const logStream = fs.createWriteStream(LOG_FILE, {
  flags: 'a',
  encoding: 'utf-8'
});

let verbose = false;

const log = (message, forceVerbose = false) => {
  const ts = new Date().toISOString();
  const logMsg = `${ts} - ${message}`;

  // Always log to file, but only print to console if verbose or forceVerbose
  if (verbose || forceVerbose) {
    console.log(logMsg);
  }

  logStream.write(logMsg + '\n');
};

// Parse command line arguments
const args = process.argv.slice(2);
let targetAnimeId = null;
let targetMalId = null;
let sampleSize = 5;
let updateDb = false;

for (const arg of args) {
  if (arg === '--verbose') {
    verbose = true;
  } else if (arg === '--update-db') {
    updateDb = true;
    log('Update DB mode enabled. Will update database with fetched seasons.', true);
  } else if (arg.startsWith('--id=')) {
    targetAnimeId = arg.replace('--id=', '');
    verbose = true; // Always enable verbose mode when testing a specific item
    log(`Testing specific anime ID: ${targetAnimeId}. Verbose mode automatically enabled.`, true);
  } else if (arg.startsWith('--mal-id=')) {
    targetMalId = parseInt(arg.replace('--mal-id=', ''), 10);
    if (isNaN(targetMalId)) {
      log(`Invalid MAL ID: ${arg.replace('--mal-id=', '')}`, true);
      process.exit(1);
    }
    verbose = true; // Always enable verbose mode when testing a specific item
    log(`Testing specific MAL ID: ${targetMalId}. Verbose mode automatically enabled.`, true);
  } else if (arg.startsWith('--sample=')) {
    sampleSize = parseInt(arg.replace('--sample=', ''), 10);
    if (isNaN(sampleSize) || sampleSize < 1) {
      sampleSize = 5;
    }
    log(`Sample size set to: ${sampleSize}`, true);
  }
}

// Utility function to sleep
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Function to test Jikan seasons fetching for a specific anime
async function testJikanSeasons(anime) {
  const animeId = anime._id.toString();
  const title = anime.title;
  const malId = anime.jikan?.mal_id;

  log(`Testing Jikan seasons for anime: ${title} (ID: ${animeId}, MAL ID: ${malId || 'N/A'})`, true);

  if (!malId) {
    log(`Anime ${title} (ID: ${animeId}) has no MAL ID. Skipping.`, true);
    return {
      animeId,
      title,
      malId: null,
      success: false,
      message: 'No MAL ID',
      seasonCount: 0,
      seasons: []
    };
  }

  try {
    // Fetch seasons data
    const seasonsResult = await jikanSeasonsFetcher.getAnimeSeasons(malId, verbose);

    if (seasonsResult.success) {
      log(`Successfully fetched ${seasonsResult.seasonCount} seasons for ${title} (MAL ID: ${malId})`, true);

      // Update the database if requested
      if (updateDb && seasonsResult.seasons && seasonsResult.seasons.length > 0) {
        try {
          const updateResult = await Anime.updateOne(
            { _id: animeId },
            { $set: { jikanSeasons: seasonsResult.seasons } }
          );

          if (updateResult.modifiedCount > 0) {
            log(`Successfully updated database with ${seasonsResult.seasonCount} seasons for ${title}`, true);
          } else {
            log(`Database update reported success but no document was modified for ${title}`, true);
          }
        } catch (dbError) {
          log(`Error updating database for ${title}: ${dbError.message}`, true);
        }
      }

      return {
        animeId,
        title,
        malId,
        success: true,
        message: 'Success',
        seasonCount: seasonsResult.seasonCount,
        seasons: seasonsResult.seasons
      };
    } else {
      log(`Failed to fetch seasons for ${title} (MAL ID: ${malId}): ${seasonsResult.message}`, true);
      return {
        animeId,
        title,
        malId,
        success: false,
        message: seasonsResult.message,
        seasonCount: 0,
        seasons: []
      };
    }
  } catch (error) {
    log(`Error testing Jikan seasons for ${title} (MAL ID: ${malId}): ${error.message}`, true);
    return {
      animeId,
      title,
      malId,
      success: false,
      message: `Error: ${error.message}`,
      seasonCount: 0,
      seasons: []
    };
  }
}

// Main function
async function main() {
  log('Starting Jikan seasons test...', true);

  try {
    // Connect to MongoDB
    await mongoose.connect(MONGO_URI);
    log('Connected to MongoDB', true);

    let animes = [];

    if (targetAnimeId) {
      // Test a specific anime by ID
      const anime = await Anime.findById(targetAnimeId);
      if (anime) {
        animes = [anime];
      } else {
        log(`Anime with ID ${targetAnimeId} not found`, true);
        process.exit(1);
      }
    } else if (targetMalId) {
      // Test a specific anime by MAL ID
      const anime = await Anime.findOne({ 'jikan.mal_id': targetMalId });
      if (anime) {
        animes = [anime];
      } else {
        log(`Anime with MAL ID ${targetMalId} not found in database`, true);
        
        // Test directly with the MAL ID
        log(`Testing MAL ID ${targetMalId} directly...`, true);
        const seasonsResult = await jikanSeasonsFetcher.getAnimeSeasons(targetMalId, true);
        
        if (seasonsResult.success) {
          log(`Successfully fetched ${seasonsResult.seasonCount} seasons for MAL ID ${targetMalId}`, true);
          log(`Seasons: ${JSON.stringify(seasonsResult.seasons.map(s => ({ mal_id: s.mal_id, title: s.title, season_number: s.season_number })))}`, true);
        } else {
          log(`Failed to fetch seasons for MAL ID ${targetMalId}: ${seasonsResult.message}`, true);
        }
        
        await mongoose.disconnect();
        log('Disconnected from MongoDB', true);
        return;
      }
    } else {
      // Sample anime from the database
      animes = await Anime.find({ 'jikan.mal_id': { $exists: true, $ne: null } })
        .sort({ 'jikan.popularity': 1 }) // Sort by popularity to get more popular anime first
        .limit(sampleSize);
      
      log(`Found ${animes.length} anime with MAL IDs`, true);
    }

    const results = [];

    // Process each anime
    for (let i = 0; i < animes.length; i++) {
      const anime = animes[i];
      log(`Processing ${i+1}/${animes.length}: ${anime.title}`, true);
      
      const result = await testJikanSeasons(anime);
      results.push(result);
      
      // Add a delay to respect Jikan's rate limit
      if (i < animes.length - 1) {
        log(`Waiting ${API_REQUEST_DELAY_MS}ms before next anime...`, verbose);
        await sleep(API_REQUEST_DELAY_MS);
      }
    }

    // Write results to file
    fs.writeFileSync(RESULTS_FILE, JSON.stringify(results, null, 2));
    log(`Results written to ${RESULTS_FILE}`, true);

    // Calculate statistics
    const successCount = results.filter(r => r.success).length;
    const totalSeasons = results.reduce((sum, r) => sum + r.seasonCount, 0);
    const avgSeasonsPerAnime = totalSeasons / results.length;

    // Log summary
    log('\n--- Test Summary ---', true);
    log(`Total anime tested: ${results.length}`, true);
    log(`Success rate: ${(successCount / results.length * 100).toFixed(2)}%`, true);
    log(`Total seasons found: ${totalSeasons}`, true);
    log(`Average seasons per anime: ${avgSeasonsPerAnime.toFixed(2)}`, true);

    // Disconnect from MongoDB
    await mongoose.disconnect();
    log('Disconnected from MongoDB', true);
  } catch (error) {
    log(`Error: ${error.message}`, true);
    process.exit(1);
  }
}

// Run the main function
main();
